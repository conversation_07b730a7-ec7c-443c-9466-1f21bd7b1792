// Copyright 2025 <Copyright Owner>

#include "x86_64_pipeline.h"

#include <algorithm>
#include <chrono>
#include <cstdint>
#include <mutex>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include <emmintrin.h> // For _mm_cmpsd and other SSE2 intrinsics
#include <spdlog/spdlog.h>
#include <xmmintrin.h> // For _mm_cmpss and other SSE intrinsics

#ifdef _MSC_VER
#include <intrin.h>
#endif

#include "cpu/x86_64_cpu.h"
#include "emulator/interrupt_handler.h"
#include "ps4/fiber_manager.h"
#include "ps4/ps4_emulator.h"

namespace x86_64 {

struct PipelineException : std::runtime_error {
  explicit PipelineException(const std::string &msg)
      : std::runtime_error(msg) {}
};

BranchPredictor::BranchPredictor()
    : m_pht(TABLE_SIZE, 2), m_ghr(0), m_hits(0), m_misses(0) {
  spdlog::info("GShare Branch Predictor initialized with {} entries",
               TABLE_SIZE);
}

bool BranchPredictor::PredictTaken(uint64_t pc) {
  size_t index = (pc ^ m_ghr) & (TABLE_SIZE - 1);
  return m_pht[index] >= 2; // Predict taken if counter >= 2
}

void BranchPredictor::Update(uint64_t pc, bool taken) {
  size_t index = (pc ^ m_ghr) & (TABLE_SIZE - 1);
  if (taken) {
    m_pht[index] = std::min<uint8_t>(m_pht[index] + 1, 3);
  } else {
    m_pht[index] = std::max<int8_t>(m_pht[index] - 1, 0);
  }
  m_ghr = ((m_ghr << 1) | (taken ? 1 : 0)) & ((1ULL << HISTORY_BITS) - 1);
}

void BranchPredictor::RecordPrediction(bool correct) {
  correct ? m_hits++ : m_misses++;
}

void BranchPredictor::ResetStats() {
  m_hits = 0;
  m_misses = 0;
  std::fill(m_pht.begin(), m_pht.end(), 2);
  m_ghr = 0;
}

Pipeline::Pipeline(X86_64CPU &cpu_ref)
    : cpu(cpu_ref), decoder(),
      jit(std::make_unique<X86_64JITCompiler>(&cpu_ref)), branchPredictor(),
      execution_units(2) {
  ResetStats();
  fetchStage.reserve(8);
  decodeStage.reserve(8);
  executeStage.reserve(4);
  memoryStage.reserve(4);
  writeBackStage.reserve(4);
  spdlog::info("Pipeline initialized for CPU at 0x{:x}",
               reinterpret_cast<uintptr_t>(&cpu_ref));
}

void Pipeline::Step() {
  // CRITICAL DEADLOCK FIX: Use try_lock to prevent deadlocks with CPU mutex
  std::unique_lock<std::timed_mutex> lock(mutex, std::try_to_lock);
  if (!lock.owns_lock()) {
    // If we can't acquire the lock immediately, it might indicate deadlock
    spdlog::warn("Pipeline::Step() could not acquire mutex, potential deadlock "
                 "scenario");
    stats.lock_contentions++;

    // Try with timeout to detect deadlock
    if (!lock.try_lock_for(std::chrono::milliseconds(1))) {
      spdlog::error("Pipeline::Step() mutex acquisition timeout - aborting to "
                    "prevent deadlock");
      return;
    }
  }

  try {
    // CRITICAL FIX: Validate pipeline state before proceeding
    if (!ValidatePipelineState()) {
      spdlog::error("Pipeline state validation failed, flushing pipeline");
      Flush();
      return;
    }

    // Execute pipeline stages with individual error handling
    try {
      WriteBack();
    } catch (const std::exception &e) {
      spdlog::error("WriteBack stage failed: {}", e.what());
      FlushFromStage(4);
    }

    try {
      Memory();
    } catch (const std::exception &e) {
      spdlog::error("Memory stage failed: {}", e.what());
      FlushFromStage(3);
    }

    try {
      Execute();
    } catch (const std::exception &e) {
      spdlog::error("Execute stage failed: {}", e.what());
      FlushFromStage(2);
    }

    try {
      Decode();
    } catch (const std::exception &e) {
      spdlog::error("Decode stage failed: {}", e.what());
      FlushFromStage(1);
    }

    try {
      FetchMultiple();
    } catch (const std::exception &e) {
      spdlog::error("Fetch stage failed: {}", e.what());
      Flush();
    }

    stats.cycles++;
    UpdateProfiling();

    if (stats.cycles % 10000 == 0) {
      spdlog::trace(
          "Pipeline step: cycles={}, instructionsExecuted={}, stalls={}, "
          "lock_contentions={}, fetch_stage={}, decode_stage={}, "
          "execute_stage={}, "
          "memory_stage={}, writeback_stage={}",
          stats.cycles, stats.instructionsExecuted, stats.stalls,
          stats.lock_contentions, fetchStage.size(), decodeStage.size(),
          executeStage.size(), memoryStage.size(), writeBackStage.size());
    }
  } catch (const PipelineException &e) {
    spdlog::error("Pipeline error: {}", e.what());
    Flush();
  } catch (const std::exception &e) {
    spdlog::error("Unexpected pipeline error: {}", e.what());
    Flush();
  }
}

void Pipeline::Flush() {
  // CRITICAL DEADLOCK FIX: Use try_lock to prevent deadlocks
  std::unique_lock<std::timed_mutex> lock(mutex, std::try_to_lock);
  if (!lock.owns_lock()) {
    spdlog::warn("Pipeline::Flush() could not acquire mutex immediately");
    if (!lock.try_lock_for(std::chrono::milliseconds(5))) {
      spdlog::error(
          "Pipeline::Flush() mutex acquisition timeout - forcing flush");
      spdlog::critical("Emergency pipeline flush without lock protection");
    }
  }

  fetchStage.clear();
  decodeStage.clear();
  executeStage.clear();
  memoryStage.clear();
  writeBackStage.clear();
  spdlog::debug("Pipeline flushed");
}

void Pipeline::ResetStats() {
  std::lock_guard<std::timed_mutex> lock(mutex);
  stats = {};
  stats.cycles = 0;
  stats.instructionsExecuted = 0;
  stats.stalls = 0;
  stats.data_hazard_stalls = 0;
  stats.structural_hazard_stalls = 0;
  stats.memory_stalls = 0;
  stats.branch_hits = 0;
  stats.branch_mispredictions = 0;
  stats.avg_instruction_latency = 0;
  stats.simd_instructions = 0;
  stats.fetch_cycle_start = 0;
  stats.jit_executions = 0;
  stats.jit_fallbacks = 0;
  stats.jit_compile_failures = 0;
  stats.jit_cache_hits = 0;
  stats.jit_cache_misses = 0;
  stats.memory_protection_faults = 0;
  stats.tlb_hits = 0;
  stats.tlb_misses = 0;
  stats.cache_l1_hits = 0;
  stats.cache_l1_misses = 0;
  stats.cache_l2_hits = 0;
  stats.cache_l2_misses = 0;
  stats.prefetch_hits = 0;
  stats.prefetch_misses = 0;
  stats.thread_switches = 0;
  stats.lock_contentions = 0;
  stats.atomic_operations = 0;
  stats.ipc = 0.0;
  stats.branch_prediction_accuracy = 0.0;
  stats.jit_efficiency = 0.0;
  stats.cache_hit_ratio = 0.0;

  profilingData.clear();
  branchPredictor.ResetStats();
  spdlog::info(
      "Pipeline stats reset with comprehensive zero-initialized values");
}

void Pipeline::FetchMultiple() {
  constexpr size_t MAX_FETCH = 4;
  if (fetchStage.size() >= MAX_FETCH) {
    spdlog::trace("Fetch stage full, skipping fetch");
    stats.stalls++;
    return;
  }

  // CRITICAL DEADLOCK FIX: Release pipeline mutex before CPU operations
  if (!mutex.try_lock()) {
    spdlog::warn("Pipeline::FetchMultiple() could not acquire mutex");
    return;
  }
  mutex.unlock();

  uint64_t pc = 0;
  try {
    pc = cpu.GetRegister(Register::RIP);

    // CRITICAL SAFETY: Validate instruction pointer
    if (pc == 0 || pc == 0xDEADBEEF || pc == 0xCCCCCCCC || pc == 0xFEEEFEEE) {
      spdlog::error("Invalid RIP for fetch: 0x{:x}, aborting fetch", pc);
      std::unique_lock<std::timed_mutex> error_lock(mutex, std::try_to_lock);
      if (!error_lock.owns_lock()) {
        error_lock.try_lock_for(std::chrono::milliseconds(5));
      }
      if (error_lock.owns_lock()) {
        Flush();
      }
      return;
    }

    if (pc < 0x1000 || pc >= 0x800000000000ULL) {
      spdlog::error("RIP out of valid range for fetch: 0x{:x}, aborting fetch",
                    pc);
      std::unique_lock<std::timed_mutex> error_lock(mutex, std::try_to_lock);
      if (!error_lock.owns_lock()) {
        error_lock.try_lock_for(std::chrono::milliseconds(5));
      }
      if (error_lock.owns_lock()) {
        Flush();
      }
      return;
    }
  } catch (const std::exception &e) {
    spdlog::error("Failed to get RIP for fetch: {}", e.what());
    std::unique_lock<std::timed_mutex> error_lock(mutex, std::try_to_lock);
    if (!error_lock.owns_lock()) {
      error_lock.try_lock_for(std::chrono::milliseconds(5));
    }
    if (error_lock.owns_lock()) {
      stats.stalls++;
      Flush();
    }
    return;
  }

  std::vector<uint8_t> buffer(16 * MAX_FETCH);
  try {
    if (!cpu.GetMemory().ReadVirt(pc, buffer.data(), buffer.size(),
                                  cpu.GetProcessId())) {
      spdlog::error("Fetch failed at 0x{:x}: memory not accessible", pc);
      std::unique_lock<std::timed_mutex> error_lock(mutex, std::try_to_lock);
      if (!error_lock.owns_lock()) {
        error_lock.try_lock_for(std::chrono::milliseconds(5));
      }
      if (error_lock.owns_lock()) {
        stats.stalls++;
        Flush();
        throw PipelineException("Fetch memory access failure");
      }
      return;
    }

    // CRITICAL: Reacquire pipeline mutex after memory operations
    if (!mutex.try_lock()) {
      spdlog::error(
          "Failed to reacquire pipeline mutex after fetch memory read");
      return;
    }
    std::unique_lock<std::timed_mutex> relock(mutex, std::adopt_lock);

    size_t offset = 0;
    for (size_t i = 0; i < MAX_FETCH && offset < buffer.size(); ++i) {
      if (fetchStage.size() >= MAX_FETCH) {
        spdlog::trace("Fetch stage reached capacity at instruction {}", i);
        break;
      }

      DecodedInstruction tempInstr;
      DecoderErrorInfo errorInfo =
          decoder.Decode(pc + offset, buffer.data() + offset,
                         buffer.size() - offset, tempInstr);

      if (errorInfo.error != DecoderError::Success) {
        spdlog::error("Fetch decode failed at 0x{:x}: error={}", pc + offset,
                      static_cast<int>(errorInfo.error));

        relock.unlock();
        try {
          cpu.TriggerInterrupt(EXC_UD, 0, false);
        } catch (const std::exception &e) {
          spdlog::error("Failed to trigger interrupt after decode failure: {}",
                        e.what());
        }
        return;
      }

      FetchStage stage;
      stage.pc = pc + offset;
      stage.instr = tempInstr;
      stage.valid = true;
      stage.fetch_cycle = stats.cycles;

      if (IsBranchInstruction(tempInstr)) {
        uint64_t nextRip = stage.pc + tempInstr.length;
        if (tempInstr.instType == InstructionType::Jcc) {
          stage.predicted_taken = branchPredictor.PredictTaken(stage.pc);
          stage.predicted_target =
              PredictBranchTarget(tempInstr, stage.pc, nextRip);

          if (stage.predicted_taken) {
            pc = stage.predicted_target;
            spdlog::trace("Branch prediction: taken at 0x{:x}, target=0x{:x}",
                          stage.pc, stage.predicted_target);
          }
        } else if (tempInstr.instType == InstructionType::Jump ||
                   tempInstr.instType == InstructionType::Call) {
          stage.predicted_taken = true;
          stage.predicted_target =
              PredictBranchTarget(tempInstr, stage.pc, nextRip);
          if (stage.predicted_target != 0) {
            pc = stage.predicted_target;
            spdlog::trace("Unconditional branch at 0x{:x}, target=0x{:x}",
                          stage.pc, stage.predicted_target);
          }
        }
      }

      fetchStage.push_back(stage);
      offset += tempInstr.length;

      if (stage.predicted_taken && stage.predicted_target != 0) {
        spdlog::trace("Stopping fetch at branch instruction");
        break;
      }
    }
  } catch (const std::exception &e) {
    spdlog::error("Fetch failed at 0x{:x}: {}", pc, e.what());
    std::unique_lock<std::timed_mutex> error_lock(mutex, std::try_to_lock);
    if (!error_lock.owns_lock()) {
      error_lock.try_lock_for(std::chrono::milliseconds(5));
    }
    if (error_lock.owns_lock()) {
      stats.stalls++;
      Flush();
      throw PipelineException("Fetch memory access failure");
    }
  }
}

void Pipeline::Decode() {
  if (fetchStage.empty() || decodeStage.size() >= 8) {
    spdlog::trace("Decode stage full or fetch stage empty, skipping decode");
    stats.stalls++;
    return;
  }

  auto stage = std::move(fetchStage.front());
  fetchStage.erase(fetchStage.begin());

  if (!stage.valid) {
    spdlog::warn("Invalid fetch stage entry, skipping decode");
    return;
  }

  try {
    DecodeStage decode;
    decode.instr = stage.instr;
    decode.pc = stage.pc;
    decode.valid = true;
    decode.predicted_taken = stage.predicted_taken;
    decode.predicted_target = stage.predicted_target;
    decode.decode_cycle = stats.cycles;
    decode.fetch_cycle = stage.fetch_cycle;

    spdlog::debug("Decoded at 0x{:x}: type={}, operands={}", stage.pc,
                  static_cast<int>(decode.instr.instType),
                  decode.instr.operandCount);
    decodeStage.push_back(std::move(decode));

    if (!stage.predicted_taken)
      cpu.SetRegister(Register::RIP, stage.pc + stage.instr.length);
  } catch (const std::exception &e) {
    spdlog::error("Decode failed at 0x{:x}: {}", stage.pc, e.what());
    stats.stalls++;
    Flush();
    throw PipelineException("Decode memory access failure");
  }
}

void Pipeline::Execute() {
  if (decodeStage.empty() || executeStage.size() >= 4 ||
      HasStructuralHazard()) {
    spdlog::trace("Execute stage full, decode empty, or structural hazard, "
                  "skipping execute");
    stats.stalls++;
    if (HasStructuralHazard())
      stats.structural_hazard_stalls++;
    return;
  }

  auto it = std::find_if(decodeStage.begin(), decodeStage.end(),
                         [this](const DecodeStage &stage) {
                           return stage.valid && !HasDataHazard(stage.instr);
                         });

  if (it == decodeStage.end()) {
    spdlog::trace("No executable instruction due to hazards");
    stats.data_hazard_stalls++;
    stats.stalls++;
    return;
  }

  auto stage = std::move(*it);
  decodeStage.erase(it);

  // CRITICAL VALIDATION: Check for invalid instruction pointers
  if (stage.pc == 0 || stage.pc == 0xDEADBEEF || stage.pc == 0xCCCCCCCC ||
      stage.pc == 0xFEEEFEEE) {
    spdlog::error(
        "Execute stage received invalid PC: 0x{:x}, aborting execution",
        stage.pc);
    return;
  }

  ExecuteStage exec;
  exec.instr = stage.instr;
  exec.pc = stage.pc;
  exec.valid = true;
  exec.predicted_taken = stage.predicted_taken;
  exec.predicted_target = stage.predicted_target;
  exec.execute_cycle = stats.cycles;
  exec.fetch_cycle = stage.fetch_cycle;

  try {
    // CRITICAL DEADLOCK FIX: Release pipeline mutex before CPU operations
    if (!mutex.try_lock()) {
      spdlog::warn("Pipeline::Execute() could not acquire mutex");
      return;
    }
    mutex.unlock();

    try {
      InterpretInstruction(exec);
    } catch (const std::exception &e) {
      spdlog::error("Instruction interpretation failed at 0x{:x}: {}", exec.pc,
                    e.what());
      if (mutex.try_lock()) {
        std::unique_lock<std::timed_mutex> error_lock(mutex, std::adopt_lock);
        Flush();
      }
      return;
    }

    // CRITICAL: Reacquire pipeline mutex after CPU operations
    if (!mutex.try_lock()) {
      spdlog::error("Failed to reacquire pipeline mutex after instruction "
                    "interpretation");
      return;
    }
    std::unique_lock<std::timed_mutex> relock(mutex, std::adopt_lock);

    executeStage.push_back(std::move(exec));
    spdlog::trace("Execute stage processed at 0x{:x}", exec.pc);

  } catch (const std::exception &e) {
    spdlog::error("Execute stage failed at 0x{:x}: {}", exec.pc, e.what());
    stats.stalls++;
    Flush();
    throw PipelineException("Execute stage failure");
  }
}

void Pipeline::Memory() {
  if (executeStage.empty()) {
    spdlog::trace("Memory stage empty, skipping");
    return;
  }

  auto stage = std::move(executeStage.front());
  executeStage.erase(executeStage.begin());

  if (!stage.valid) {
    spdlog::warn("Invalid execute stage entry, skipping memory");
    return;
  }

  // CRITICAL VALIDATION: Check for invalid instruction pointers
  if (stage.pc == 0 || stage.pc == 0xDEADBEEF || stage.pc == 0xCCCCCCCC ||
      stage.pc == 0xFEEEFEEE) {
    spdlog::error("Memory stage received invalid PC: 0x{:x}, aborting",
                  stage.pc);
    return;
  }

  MemoryStage mem;
  mem.instr = stage.instr;
  mem.pc = stage.pc;
  mem.valid = true;
  mem.memory_cycle = stats.cycles;
  mem.fetch_cycle = stage.fetch_cycle;

  memoryStage.push_back(std::move(mem));
  spdlog::trace("Memory stage processed at 0x{:x}", mem.pc);
}

void Pipeline::WriteBack() {
  if (memoryStage.empty()) {
    spdlog::trace("Writeback stage empty, skipping");
    return;
  }

  auto stage = std::move(memoryStage.front());
  memoryStage.erase(memoryStage.begin());

  if (!stage.valid) {
    spdlog::warn("Invalid memory stage entry, skipping writeback");
    return;
  }

  WriteBackStage wb;
  wb.instr = stage.instr;
  wb.pc = stage.pc;
  wb.valid = true;
  wb.writeback_cycle = stats.cycles;
  wb.fetch_cycle = stage.fetch_cycle;

  stats.instructionsExecuted++;

  if (IsSIMDInstruction(stage.instr)) {
    stats.simd_instructions++;
    spdlog::trace("SIMD instruction completed: type={}, total_simd={}",
                  static_cast<int>(stage.instr.instType),
                  stats.simd_instructions);
  }

  uint64_t latency = wb.writeback_cycle - wb.fetch_cycle + 1;

  if (stats.instructionsExecuted == 1) {
    stats.avg_instruction_latency = latency;
  } else {
    stats.avg_instruction_latency =
        (stats.avg_instruction_latency * 7 + latency) / 8;
  }

  if (wb.fetch_cycle == 0) {
    spdlog::warn("Instruction completed with uninitialized fetch_cycle, using "
                 "current cycle as estimate");
    wb.fetch_cycle = stats.cycles;
    latency = 1;
  }

  spdlog::trace("Instruction latency: fetch_cycle={}, writeback_cycle={}, "
                "latency={}, avg={}",
                wb.fetch_cycle, wb.writeback_cycle, latency,
                stats.avg_instruction_latency);

  writeBackStage.push_back(std::move(wb));
  spdlog::trace("Writeback at 0x{:x}, instructionsExecuted={}", stage.pc,
                stats.instructionsExecuted);
}

void Pipeline::InterpretInstruction(ExecuteStage &stage) {
  auto &instr = stage.instr;
  uint64_t nextRip = stage.pc + instr.length;

  switch (instr.instType) {
  case InstructionType::Mov: {
    uint64_t value = cpu.ReadOperandValue(instr.operands[1]);
    cpu.WriteOperandValue(instr.operands[0], value);
    spdlog::trace("MOV: value=0x{:x}", value);
    break;
  }
  case InstructionType::Nop: {
    spdlog::trace("NOP");
    break;
  }
  default:
    spdlog::error("Unsupported instruction type {} at 0x{:x}",
                  static_cast<int>(instr.instType), stage.pc);
    cpu.TriggerInterrupt(EXC_UD, 0, false);
    throw PipelineException("Unsupported instruction");
  }
}

// Helper functions
bool Pipeline::IsBranchInstruction(const DecodedInstruction &instr) const {
  return instr.instType == InstructionType::Jump ||
         instr.instType == InstructionType::Jcc ||
         instr.instType == InstructionType::Call ||
         instr.instType == InstructionType::Ret;
}

bool Pipeline::IsSIMDInstruction(const DecodedInstruction &instr) const {
  switch (instr.instType) {
  case InstructionType::Addps:
  case InstructionType::Subps:
  case InstructionType::Mulps:
  case InstructionType::Divps:
  case InstructionType::Sqrtps:
  case InstructionType::Maxps:
  case InstructionType::Minps:
  case InstructionType::Andps:
  case InstructionType::Orps:
  case InstructionType::Xorps:
  case InstructionType::Movaps:
  case InstructionType::Movups:
    return true;
  default:
    return false;
  }
}

uint64_t Pipeline::PredictBranchTarget(const DecodedInstruction &instr,
                                       uint64_t pc, uint64_t nextRip) const {
  switch (instr.instType) {
  case InstructionType::Jump:
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::IMMEDIATE)
      return nextRip + instr.operands[0].immediate;
    return 0;
  case InstructionType::Jcc:
    return nextRip + instr.operands[0].immediate;
  case InstructionType::Call:
    if (instr.operands[0].type == DecodedInstruction::Operand::Type::IMMEDIATE)
      return nextRip + instr.operands[0].immediate;
    return 0;
  case InstructionType::Ret:
    return 0;
  default:
    return 0;
  }
}

bool Pipeline::HasDataHazard(const DecodedInstruction &instr) const {
  for (const auto &stage : executeStage) {
    if (stage.valid && Conflicts(instr, stage.instr)) {
      spdlog::trace(
          "Data hazard detected with execute stage instruction at 0x{:x}",
          stage.pc);
      return true;
    }
  }
  for (const auto &stage : memoryStage) {
    if (stage.valid && Conflicts(instr, stage.instr)) {
      spdlog::trace(
          "Data hazard detected with memory stage instruction at 0x{:x}",
          stage.pc);
      return true;
    }
  }
  return false;
}

bool Pipeline::HasStructuralHazard() const {
  size_t active_units = 0;
  for (const auto &stage : executeStage) {
    if (stage.valid)
      active_units++;
  }
  return active_units >= execution_units;
}

bool Pipeline::Conflicts(const DecodedInstruction &a,
                         const DecodedInstruction &b) const {
  for (int i = 0; i < a.operandCount; ++i) {
    if (a.operands[i].type != DecodedInstruction::Operand::Type::REGISTER &&
        a.operands[i].type != DecodedInstruction::Operand::Type::XMM)
      continue;
    for (int j = 0; j < b.operandCount; ++j) {
      if (b.operands[j].type == a.operands[i].type &&
          a.operands[i].reg == b.operands[j].reg) {
        spdlog::trace("Register conflict: reg={}",
                      static_cast<int>(a.operands[i].reg));
        return true;
      }
    }
  }
  return false;
}

bool Pipeline::ValidatePipelineState() const {
  if (fetchStage.size() > 16 || decodeStage.size() > 16 ||
      executeStage.size() > 8 || memoryStage.size() > 8 ||
      writeBackStage.size() > 8) {
    spdlog::error("Pipeline stage overflow detected: fetch={}, decode={}, "
                  "execute={}, memory={}, writeback={}",
                  fetchStage.size(), decodeStage.size(), executeStage.size(),
                  memoryStage.size(), writeBackStage.size());
    return false;
  }

  for (const auto &stage : fetchStage) {
    if (stage.valid &&
        (stage.pc == 0 || stage.pc == 0xDEADBEEF || stage.pc == 0xCCCCCCCC)) {
      spdlog::error("Invalid PC in fetch stage: 0x{:x}", stage.pc);
      return false;
    }
  }

  for (const auto &stage : decodeStage) {
    if (stage.valid &&
        (stage.pc == 0 || stage.pc == 0xDEADBEEF || stage.pc == 0xCCCCCCCC)) {
      spdlog::error("Invalid PC in decode stage: 0x{:x}", stage.pc);
      return false;
    }
  }

  for (const auto &stage : executeStage) {
    if (stage.valid &&
        (stage.pc == 0 || stage.pc == 0xDEADBEEF || stage.pc == 0xCCCCCCCC)) {
      spdlog::error("Invalid PC in execute stage: 0x{:x}", stage.pc);
      return false;
    }
  }

  return true;
}

void Pipeline::FlushFromStage(int stage_num) {
  switch (stage_num) {
  case 0:
    fetchStage.clear();
    [[fallthrough]];
  case 1:
    decodeStage.clear();
    [[fallthrough]];
  case 2:
    executeStage.clear();
    [[fallthrough]];
  case 3:
    memoryStage.clear();
    [[fallthrough]];
  case 4:
    writeBackStage.clear();
    break;
  default:
    spdlog::error("Invalid stage number for partial flush: {}", stage_num);
    Flush();
  }
  spdlog::debug("Pipeline flushed from stage {} onwards", stage_num);
}

void Pipeline::UpdateProfiling() {
  ProfilingEntry entry;
  entry.cycle = stats.cycles;
  entry.instructions = stats.instructionsExecuted;
  entry.stalls = stats.stalls;
  entry.timestamp = std::chrono::steady_clock::now();
  profilingData.push_back(entry);
  spdlog::trace(
      "Profiling updated: cycles={}, instructionsExecuted={}, stalls={}",
      entry.cycle, entry.instructions, entry.stalls);
}

std::unordered_map<std::string, uint64_t> Pipeline::GetDiagnostics() const {
  std::lock_guard<std::timed_mutex> lock(mutex);
  std::unordered_map<std::string, uint64_t> diagnostics;
  diagnostics["fetch_stage_occupancy"] = fetchStage.size();
  diagnostics["decode_stage_occupancy"] = decodeStage.size();
  diagnostics["execute_stage_occupancy"] = executeStage.size();
  diagnostics["memory_stage_occupancy"] = memoryStage.size();
  diagnostics["writeback_stage_occupancy"] = writeBackStage.size();
  diagnostics["cycles"] = stats.cycles;
  diagnostics["instructions_executed"] = stats.instructionsExecuted;
  diagnostics["total_stalls"] = stats.stalls;
  diagnostics["data_hazard_stalls"] = stats.data_hazard_stalls;
  diagnostics["structural_hazard_stalls"] = stats.structural_hazard_stalls;
  diagnostics["memory_stalls"] = stats.memory_stalls;
  diagnostics["branch_hits"] = stats.branch_hits;
  diagnostics["branch_mispredictions"] = stats.branch_mispredictions;
  diagnostics["branch_prediction_accuracy"] =
      stats.branch_hits + stats.branch_mispredictions > 0
          ? (stats.branch_hits * 100) /
                (stats.branch_hits + stats.branch_mispredictions)
          : 0;
  diagnostics["avg_instruction_latency"] = stats.avg_instruction_latency;
  spdlog::trace("Pipeline diagnostics retrieved");
  return diagnostics;
}

bool Pipeline::SwitchToFiber(uint64_t fiberId) {
  std::lock_guard<std::timed_mutex> lock(mutex);
  try {
    ps4::FiberManager &fiberManager =
        ps4::PS4Emulator::GetInstance().GetFiberManager();
    if (fiberManager.SwitchToFiber(fiberId)) {
      spdlog::info("Pipeline switched to fiber {}", fiberId);
      Flush();
      return true;
    }
    spdlog::warn("Failed to switch to fiber {}", fiberId);
    return false;
  } catch (const std::exception &e) {
    spdlog::error("Fiber switch error: {}", e.what());
    return false;
  }
}

} // namespace x86_64
