#pragma once

#include "gcn_types.h" // Use GCNShaderType and GCNInstruction from here
#include <cstdint>
#include <functional>
#include <istream>
#include <ostream>
#include <shared_mutex>
#include <string>
#include <unordered_map>
#include <vector>

namespace ps4 {

/**
 * @brief Exception for shader translation errors.
 */
struct ShaderTranslatorException : std::runtime_error {
  explicit ShaderTranslatorException(const std::string &msg)
      : std::runtime_error(msg) {}
};

/**
 * @brief GCN register types.
 */
enum class GCNRegisterType { SCALAR, VECTOR, SPECIAL };

/**
 * @brief Shader parsing state for GCN bytecode.
 */
struct ShaderParseState {
  const std::vector<uint32_t> *bytecode = nullptr; ///< Input bytecode
  size_t position = 0;                             ///< Current parsing position
  GCNShaderType type = GCNShaderType::VERTEX;      ///< Shader type
  std::vector<GCNInstruction> instructions;        ///< Parsed instructions
  std::unordered_map<uint32_t, std::string> labelMap; ///< Label mappings
  uint64_t cacheHits = 0;   ///< Cache hits during parsing
  uint64_t cacheMisses = 0; ///< Cache misses during parsing
};

/**
 * @brief Shader cache entry for translated shaders.
 */
struct ShaderCacheEntry {
  std::vector<uint32_t> spirvCode; ///< Cached SPIR-V code
  std::string glslCode;            ///< Cached GLSL code
  GCNShaderType type;              ///< Shader type
  uint64_t cacheHits = 0;          ///< Cache hits for this entry
  uint64_t cacheMisses = 0;        ///< Cache misses for this entry
};

/**
 * @brief Enhanced GCN instruction types for comprehensive coverage.
 */
enum class GCNInstructionType {
  // Scalar ALU Instructions
  S_MOV,
  S_ADD,
  S_SUB,
  S_MUL,
  S_AND,
  S_OR,
  S_XOR,
  S_NOT,
  S_LSHL,
  S_LSHR,
  S_ASHR,
  S_CMP,
  S_BRANCH,
  S_CBRANCH,

  // Vector ALU Instructions
  V_MOV,
  V_ADD_F32,
  V_SUB_F32,
  V_MUL_F32,
  V_DIV_F32,
  V_MAD_F32,
  V_FMA_F32,
  V_MIN_F32,
  V_MAX_F32,
  V_ADD_I32,
  V_SUB_I32,
  V_MUL_I32,
  V_AND_B32,
  V_OR_B32,

  // Memory Instructions
  BUFFER_LOAD,
  BUFFER_STORE,
  IMAGE_LOAD,
  IMAGE_STORE,
  DS_READ,
  DS_WRITE,
  FLAT_LOAD,
  FLAT_STORE,

  // Control Flow
  S_ENDPGM,
  S_BARRIER,
  S_WAITCNT,
  S_SENDMSG,

  // Texture/Sampling
  IMAGE_SAMPLE,
  IMAGE_GATHER,
  IMAGE_GET_RESINFO,

  // Export Instructions
  EXP_POS,
  EXP_PARAM,
  EXP_MRT,

  UNKNOWN
};

/**
 * @brief Enhanced GCN instruction information for translation.
 */
struct EnhancedGCNInstruction {
  GCNInstructionType type;
  uint32_t opcode;
  std::vector<uint32_t> operands;
  std::string mnemonic;
  bool hasDestination;
  uint32_t destinationMask;
  bool isControlFlow;
  bool isMemoryAccess;
  uint32_t cycleCount; ///< Estimated execution cycles
};

/**
 * @brief Statistics for shader translator operations.
 */
struct ShaderTranslatorStats {
  uint64_t operationCount = 0; ///< Total translation operations
  uint64_t totalLatencyUs = 0; ///< Total latency in microseconds
  uint64_t cacheHits = 0;      ///< Cache hits for translation operations
  uint64_t cacheMisses = 0;    ///< Cache misses for translation operations
  uint64_t errorCount = 0;     ///< Total errors encountered
  uint64_t instructionsCovered =
      0; ///< Number of instructions successfully translated
  uint64_t instructionsSkipped = 0; ///< Number of unsupported instructions
  uint64_t spirvGenerations = 0;    ///< Number of SPIR-V generations
  uint64_t glslGenerations = 0;     ///< Number of GLSL generations
  uint64_t optimizationPasses = 0;  ///< Number of optimization passes performed
};

// Forward declarations to avoid circular dependencies
class PS4GPU;

/**
 * @brief Callback function types for notifying PS4GPU of shader translations.
 */
using ShaderTranslationCallback_SPIRV =
    std::function<void(GCNShaderType type, uint64_t bytecodeHash,
                       const std::vector<uint32_t> &spirvCode)>;
using ShaderTranslationCallback_GLSL = std::function<void(
    GCNShaderType type, uint64_t bytecodeHash, const std::string &glslCode)>;

/**
 * @brief Translates GCN shaders to SPIR-V or GLSL and disassembles for
 * debugging.
 * @details Manages shader translation in a thread-safe manner, with caching to
 *          reduce redundant translations. Integrates with GNMRegisterState for
 *          register data and TileManager for tiled surfaces. Supports
 *          serialization with versioning and multi-core diagnostics.
 */
class GNMShaderTranslator {
public:
  /**
   * @brief Constructs the shader translator.
   * @details Initializes opcode tables and metrics. Thread-safe.
   */
  GNMShaderTranslator();

  /**
   * @brief Destructor, cleaning up resources.
   * @details Thread-safe.
   */
  ~GNMShaderTranslator();

  /**
   * @brief Initializes the shader translator.
   * @return True on success, false on failure.
   * @throws ShaderTranslatorException on initialization errors.
   * @details Populates opcode tables and resets metrics. Thread-safe.
   */
  bool Initialize();

  /**
   * @brief Shuts down the shader translator.
   * @details Clears opcode tables and cache. Thread-safe.
   */
  void Shutdown();

  /**
   * @brief Disassembles GCN bytecode into a human-readable string.
   * @param bytecode Input GCN bytecode.
   * @param type Shader type.
   * @return Disassembled shader string.
   * @throws ShaderTranslatorException on parsing errors.
   * @details Thread-safe. Updates metrics and cache.
   */
  std::string Disassemble(const std::vector<uint32_t> &bytecode,
                          GCNShaderType type);

  /**
   * @brief Translates GCN bytecode to SPIR-V.
   * @param bytecode Input GCN bytecode.
   * @param type Shader type.
   * @return SPIR-V code.
   * @throws ShaderTranslatorException on parsing or translation errors.
   * @details Thread-safe. Checks cache first, updates metrics.
   * @note May use GNMRegisterState for register data.
   */
  std::vector<uint32_t> TranslateToSPIRV(const std::vector<uint32_t> &bytecode,
                                         GCNShaderType type);

  /**
   * @brief Translates GCN bytecode to GLSL.
   * @param bytecode Input GCN bytecode.
   * @param type Shader type.
   * @return GLSL code.
   * @throws ShaderTranslatorException on parsing or translation errors.
   * @details Thread-safe. Checks cache first, updates metrics.
   */
  std::string TranslatetoGLSL(const std::vector<uint32_t> &bytecode,
                              GCNShaderType type);

  /**
   * @brief Translates SPIR-V to GNM binary.
   * @param spirv Input SPIR-V code.
   * @param gnmBinary Output GNM binary.
   * @return True on success, false on failure.
   * @throws ShaderTranslatorException on translation errors.
   * @details Thread-safe. Updates metrics.
   * @note Currently stubbed; full implementation pending.
   */
  bool Translate(const std::vector<uint32_t> &spirv,
                 std::vector<uint32_t> &gnmBinary);

  /**
   * @brief Retrieves a cached shader.
   * @param bytecodeHash Hash of the GCN bytecode.
   * @param type Shader type.
   * @param spirvCode Output SPIR-V code (if cached).
   * @param glslCode Output GLSL code (if cached).
   * @return True if cached, false otherwise.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  bool GetCachedShader(uint64_t bytecodeHash, GCNShaderType type,
                       std::vector<uint32_t> &spirvCode,
                       std::string &glslCode) const;

  /**
   * @brief Clears the shader cache.
   * @details Thread-safe. Resets cache metrics.
   */
  void ClearShaderCache();

  /**
   * @brief Retrieves shader translator statistics.
   * @return Current statistics.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  ShaderTranslatorStats GetStats() const;

  /**
   * @brief Saves the translator state to a stream.
   * @param out Output stream.
   * @details Thread-safe (read-only). Serializes with versioning (version 1).
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the translator state from a stream.
   * @param in Input stream.
   * @details Thread-safe. Expects version 1 serialization format.
   * @throws ShaderTranslatorException on invalid state data.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Sets the callback for shader translation notifications (SPIR-V).
   * @param callback Callback function to call when SPIR-V translation
   * completes.
   */
  void SetShaderTranslationCallback_SPIRV(
      const ShaderTranslationCallback_SPIRV &callback);

  /**
   * @brief Sets the callback for shader translation notifications (GLSL).
   * @param callback Callback function to call when GLSL translation completes.
   */
  void SetShaderTranslationCallback_GLSL(
      const ShaderTranslationCallback_GLSL &callback);

  /**
   * @brief Enhanced shader translation methods.
   */
  bool TranslateGCNInstruction(const EnhancedGCNInstruction &instr,
                               std::string &spirvCode, std::string &glslCode);
  bool OptimizeShader(std::vector<uint32_t> &spirvCode);
  bool ValidateShader(const std::vector<uint32_t> &spirvCode);
  std::string GenerateGLSLFromSPIRV(const std::vector<uint32_t> &spirvCode);
  bool AnalyzeShaderComplexity(const std::vector<uint32_t> &bytecode,
                               uint32_t &instructionCount,
                               uint32_t &cycleEstimate);

private:
  /**
   * @brief Parses GCN shader bytecode.
   * @param state Parsing state.
   * @return True on success, false on failure.
   * @details Thread-safe. Updates parse state and metrics.
   */
  bool ParseShader(ShaderParseState &state);

  /**
   * @brief Parses a single GCN instruction.
   * @param state Parsing state.
   * @param instr Output instruction.
   * @return True on success, false on failure.
   * @details Thread-safe. Advances parse position.
   */
  bool ParseInstruction(ShaderParseState &state, GCNInstruction &instr);

  /**
   * @brief Disassembles a GCN instruction.
   * @param instr Instruction to disassemble.
   * @param address Instruction address.
   * @return Disassembled string.
   * @details Thread-safe. Formats based on instruction type.
   */
  std::string DisassembleInstruction(const GCNInstruction &instr,
                                     uint32_t address);

  /**
   * @brief Gets the name of a GCN register.
   * @param type Register type.
   * @param index Register index.
   * @return Register name.
   * @details Thread-safe (read-only).
   */
  std::string GetRegisterName(GCNRegisterType type, uint32_t index) const;

  /**
   * @brief Gets the name of a GCN opcode.
   * @param opcode Opcode value.
   * @return Opcode name.
   * @details Thread-safe (read-only).
   */
  std::string GetOpcodeName(uint32_t opcode) const;

  /**
   * @brief Formats a scalar instruction for disassembly.
   * @param instr Instruction to format.
   * @return Formatted string.
   * @details Thread-safe (read-only).
   */
  std::string FormatScalarInstruction(const GCNInstruction &instr) const;

  /**
   * @brief Formats a vector instruction for disassembly.
   * @param instr Instruction to format.
   * @return Formatted string.
   * @details Thread-safe (read-only).
   */
  std::string FormatVectorInstruction(const GCNInstruction &instr) const;

  /**
   * @brief Formats a memory instruction for disassembly.
   * @param instr Instruction to format.
   * @return Formatted string.
   * @details Thread-safe (read-only).
   */
  std::string FormatMemoryInstruction(const GCNInstruction &instr) const;

  /**
   * @brief Formats a flow control instruction for disassembly.
   * @param instr Instruction to format.
   * @return Formatted string.
   * @details Thread-safe (read-only).
   */
  std::string FormatFlowControlInstruction(const GCNInstruction &instr) const;

  /**
   * @brief Initializes opcode tables.
   * @details Thread-safe. Populates scalar, vector, memory, and flow control
   * opcodes.
   */
  void InitializeOpcodeTables();

  /**
   * @brief Enhanced GCN instruction processing methods.
   */
  bool ProcessScalarALU(const EnhancedGCNInstruction &instr,
                        std::string &output);
  bool ProcessVectorALU(const EnhancedGCNInstruction &instr,
                        std::string &output);
  bool ProcessMemoryInstruction(const EnhancedGCNInstruction &instr,
                                std::string &output);
  bool ProcessControlFlow(const EnhancedGCNInstruction &instr,
                          std::string &output);
  bool ProcessTextureInstruction(const EnhancedGCNInstruction &instr,
                                 std::string &output);
  bool ProcessExportInstruction(const EnhancedGCNInstruction &instr,
                                std::string &output);

  /**
   * @brief SPIR-V generation helpers.
   */
  std::string GenerateSPIRVHeader();
  std::string GenerateSPIRVDeclarations(GCNShaderType type);
  std::string
  GenerateSPIRVMain(const std::vector<EnhancedGCNInstruction> &instructions);

  /**
   * @brief GLSL generation helpers.
   */
  std::string GenerateGLSLHeader(GCNShaderType type);
  std::string GenerateGLSLDeclarations(GCNShaderType type);
  std::string
  GenerateGLSLMain(const std::vector<EnhancedGCNInstruction> &instructions);

  /**
   * @brief Instruction analysis and optimization.
   */
  bool AnalyzeDataFlow(const std::vector<EnhancedGCNInstruction> &instructions);
  bool OptimizeInstructionSequence(
      std::vector<EnhancedGCNInstruction> &instructions);
  uint32_t EstimateInstructionCycles(const EnhancedGCNInstruction &instr);

  std::unordered_map<uint32_t, std::string>
      m_scalarOpcodes; ///< Scalar opcode names
  std::unordered_map<uint32_t, std::string>
      m_vectorOpcodes; ///< Vector opcode names
  std::unordered_map<uint32_t, std::string>
      m_memoryOpcodes; ///< Memory opcode names
  std::unordered_map<uint32_t, std::string>
      m_flowControlOpcodes; ///< Flow control opcode names

  // Enhanced instruction mapping tables
  std::unordered_map<uint32_t, GCNInstructionType>
      m_opcodeToType; ///< Opcode to instruction type mapping
  std::unordered_map<GCNInstructionType, uint32_t>
      m_instructionCycles; ///< Cycle estimates per instruction
  std::unordered_map<uint32_t, std::string>
      m_textureOpcodes; ///< Texture operation opcodes
  std::unordered_map<uint32_t, std::string>
      m_exportOpcodes; ///< Export operation opcodes
  mutable std::unordered_map<uint64_t, ShaderCacheEntry>
      m_shaderCache;                           ///< Shader cache
  mutable std::shared_mutex m_translatorMutex; ///< Mutex for thread safety
  mutable ShaderTranslatorStats m_stats;       ///< Translator statistics

  // Callback functions for notifying PS4GPU of shader translations
  ShaderTranslationCallback_SPIRV
      m_spirvCallback; ///< SPIR-V translation callback
  ShaderTranslationCallback_GLSL m_glslCallback; ///< GLSL translation callback
};

class GNMRegisterState;
class TileManager;

} // namespace ps4