<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_SPIN_LOCK(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_spin_lock, pthread_spin_trylock - lock a spin lock object
(<B>ADVANCED REALTIME THREADS</B>) 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt; </B>
</P>
<P><B>int pthread_spin_lock(pthread_spinlock_t *</B><I>lock</I><B>);
<BR>int pthread_spin_trylock(pthread_spinlock_t *</B><I>lock</I><B>);
</B>
</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>The <B>pthread_spin_lock</B> function shall lock the spin lock
referenced by <I>lock</I>. The calling thread shall acquire the lock
if it is not held by another thread. Otherwise, the thread shall spin
(that is, shall not return from the <B>pthread_spin_lock</B> call)
until the lock becomes available. The results are undefined if the
calling thread holds the lock at the time the call is made.</P>
<P><B>PThreads4W</B> supports single and multiple processor systems
as well as process CPU affinity masking by checking the mask when the
spin lock is initialized. If the process is using only a single
processor at the time <A HREF="pthread_spin_init.html"><B>pthread_spin_init</B>(3)</A>
is called then the spin lock is initialized as a PTHREAD_MUTEX_NORMAL
mutex object. A thread that calls <B>pthread_spin_lock</B> will block
rather than spin in this case. If the process CPU affinity mask is
altered after the spin lock has been initialised, the spin lock is
not modified, and may no longer be optimal for the number of CPUs
available.</P>
<P>The <B>pthread_spin_trylock</B> function shall lock the spin lock
referenced by <I>lock</I> if it is not held by any thread. Otherwise,
the function shall fail. 
</P>
<P>The results are undefined if any of these functions is called with
an uninitialized spin lock. 
</P>
<H2><A HREF="#toc3" NAME="sect3">Return Value</A></H2>
<P>Upon successful completion, these functions shall return zero;
otherwise, an error number shall be returned to indicate the error. 
</P>
<H2><A HREF="#toc4" NAME="sect4">Errors</A></H2>
<P>These functions may fail if: 
</P>
<DL>
	<DT><B>EINVAL</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	The value specified by <I>lock</I> does not refer to an initialized
	spin lock object. 
	</DD></DL>
<P>
The <B>pthread_spin_trylock</B> function shall fail if: 
</P>
<DL>
	<DT><B>EBUSY</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	A thread currently holds the lock. 
	</DD></DL>
<P>
These functions shall not return an error code of [EINTR]. 
</P>
<P><I>The following sections are informative.</I> 
</P>
<H2><A HREF="#toc5" NAME="sect5">Examples</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc6" NAME="sect6">Application Usage</A></H2>
<P>Applications using this function may be subject to priority
inversion, as discussed in the Base Definitions volume of
IEEE&nbsp;Std&nbsp;1003.1-2001, Section 3.285, Priority Inversion. 
</P>
<H2><A HREF="#toc7" NAME="sect7">Rationale</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc8" NAME="sect8">Future Directions</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc9" NAME="sect9">See Also</A></H2>
<P><A HREF="pthread_spin_init.html"><B>pthread_spin_destroy</B>(3)</A>
<B>,</B> <A HREF="pthread_spin_unlock.html"><B>pthread_spin_unlock</B>(3)</A>
<B>,</B> the Base Definitions volume of IEEE&nbsp;Std&nbsp;1003.1-2001,
<I>&lt;pthread.h&gt;</I> 
</P>
<H2><A HREF="#toc10" NAME="sect10">Copyright</A></H2>
<P>Portions of this text are reprinted and reproduced in electronic
form from IEEE Std 1003.1, 2003 Edition, Standard for Information
Technology -- Portable Operating System Interface (POSIX), The Open
Group Base Specifications Issue 6, Copyright (C) 2001-2003 by the
Institute of Electrical and Electronics Engineers, Inc and The Open
Group. In the event of any discrepancy between this version and the
original IEEE and The Open Group Standard, the original IEEE and The
Open Group Standard is the referee document. The original Standard
can be obtained online at <A HREF="http://www.opengroup.org/unix/online.html">http://www.opengroup.org/unix/online.html</A>
. 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Examples</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect6" NAME="toc6">Application
	Usage</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect7" NAME="toc7">Rationale</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect8" NAME="toc8">Future
	Directions</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect9" NAME="toc9">See
	Also</A> 
	</P>
	<LI><P><A HREF="#sect10" NAME="toc10">Copyright</A> 
	</P>
</UL>
</BODY>
</HTML>
