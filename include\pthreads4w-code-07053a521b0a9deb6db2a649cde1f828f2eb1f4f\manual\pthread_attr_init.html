<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=windows-1252">
	<TITLE>PTHREAD_ATTR_INIT(3) manual page</TITLE>
	<META NAME="GENERATOR" CONTENT="OpenOffice 4.1.1  (Win32)">
	<META NAME="CREATED" CONTENT="0;0">
	<META NAME="CHANGEDBY" CONTENT="Ross Johnson">
	<META NAME="CHANGED" CONTENT="20160229;19521095">
	<STYLE TYPE="text/css">
	<!--
		H3.cjk { font-family: "SimSun" }
		H3.ctl { font-family: "Mangal" }
		H4.cjk { font-family: "SimSun" }
		H4.ctl { font-family: "Mangal" }
		H2.cjk { font-family: "SimSun" }
		H2.ctl { font-family: "Mangal" }
	-->
	</STYLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4 CLASS="western">POSIX Threads for Windows &ndash; REFERENCE &ndash;
<A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2 CLASS="western"><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_attr_init, pthread_attr_destroy,
pthread_attr_setaffinity_np, pthread_attr_setdetachstate,
pthread_attr_getaffinity_np, pthread_attr_getdetachstate,
pthread_attr_setschedparam, pthread_attr_getschedparam,
pthread_attr_setschedpolicy, pthread_attr_getschedpolicy,
pthread_attr_setinheritsched, pthread_attr_getinheritsched,
pthread_attr_setscope, pthread_attr_getscope - thread creation
attributes 
</P>
<H2 CLASS="western"><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> 
</P>
<P><B>int pthread_attr_init(pthread_attr_t *</B><I>attr</I><B>);</B> 
</P>
<P><B>int pthread_attr_destroy(pthread_attr_t *</B><I>attr</I><B>);</B>
</P>
<P><B>int pthread_attr_setaffinity_np(pthread_attr_t *</B><I>attr</I><B>,
size_t </B><SPAN STYLE="font-style: normal"><SPAN STYLE="font-weight: normal">cpusetsize,
</SPAN></SPAN><SPAN STYLE="font-style: normal"><B>cpu_set_t </B></SPAN><SPAN STYLE="font-style: normal"><SPAN STYLE="font-weight: normal">*
</SPAN></SPAN><I><SPAN STYLE="font-weight: normal">cpuset</SPAN></I><SPAN STYLE="font-style: normal"><SPAN STYLE="font-weight: normal">);</SPAN></SPAN>
</P>
<P><B>int pthread_attr_getaffinity_np(const pthread_attr_t *</B><I>attr</I><B>,
size_t </B><SPAN STYLE="font-style: normal"><SPAN STYLE="font-weight: normal">cpusetsize,
</SPAN></SPAN><SPAN STYLE="font-style: normal"><B>cpu_set_t </B></SPAN><SPAN STYLE="font-style: normal"><SPAN STYLE="font-weight: normal">*
</SPAN></SPAN><I><SPAN STYLE="font-weight: normal">cpuset</SPAN></I><SPAN STYLE="font-style: normal"><SPAN STYLE="font-weight: normal">);</SPAN></SPAN>
</P>
<P><B>int pthread_attr_setdetachstate(pthread_attr_t *</B><I>attr</I><B>,
int </B><I>detachstate</I><B>);</B> 
</P>
<P><B>int pthread_attr_getdetachstate(const pthread_attr_t *</B><I>attr</I><B>,
int *</B><I>detachstate</I><B>);</B> 
</P>
<P><B>int pthread_attr_setname_np(const pthread_attr_t *</B><I>attr</I><B>,
const char * </B><I><SPAN STYLE="font-weight: normal">name</SPAN></I><B>,</B>
<B>void</B> <B>*</B> <I><SPAN STYLE="font-weight: normal">arg</SPAN></I><B>);</B></P>
<P><B>int pthread_attr_getname_np(const pthread_attr_t *</B><I>attr</I><B>,
char * </B><I><SPAN STYLE="font-weight: normal">name</SPAN></I><B>,</B>
<I><B>int </B></I><I><SPAN STYLE="font-weight: normal">len</SPAN></I><B>);</B></P>
<P><B>int pthread_attr_setschedpolicy(pthread_attr_t *</B><I>attr</I><B>,
int </B><I>policy</I><B>);</B> 
</P>
<P><B>int pthread_attr_getschedpolicy(const pthread_attr_t *</B><I>attr</I><B>,
int *</B><I>policy</I><B>);</B> 
</P>
<P><B>int pthread_attr_setschedparam(pthread_attr_t *</B><I>attr</I><B>,
const struct sched_param *</B><I>param</I><B>);</B> 
</P>
<P><B>int pthread_attr_getschedparam(const pthread_attr_t *</B><I>attr</I><B>,
struct sched_param *</B><I>param</I><B>);</B> 
</P>
<P><B>int pthread_attr_setinheritsched(pthread_attr_t *</B><I>attr</I><B>,
int </B><I>inherit</I><B>);</B> 
</P>
<P><B>int pthread_attr_getinheritsched(const pthread_attr_t *</B><I>attr</I><B>,
int *</B><I>inherit</I><B>);</B> 
</P>
<P><B>int pthread_attr_setscope(pthread_attr_t *</B><I>attr</I><B>,
int </B><I>scope</I><B>);</B> 
</P>
<P><B>int pthread_attr_getscope(const pthread_attr_t *</B><I>attr</I><B>,
int *</B><I>scope</I><B>);</B> 
</P>
<H2 CLASS="western"><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>Setting attributes for threads is achieved by filling a thread
attribute object <I>attr</I> of type <B>pthread_attr_t</B>, then
passing it as second argument to <A HREF="pthread_create.html"><B>pthread_create</B>(3)</A>
. Passing <B>NULL</B> is equivalent to passing a thread attribute
object with all attributes set to their default values. 
</P>
<P><B>pthread_attr_init</B> initializes the thread attribute object
<I>attr</I> and fills it with default values for the attributes. (The
default values are listed below for each attribute.) 
</P>
<P>Each attribute <I>attrname</I> (see below for a list of all
attributes) can be individually set using the function
<B>pthread_attr_set</B><I>attrname</I> and retrieved using the
function <B>pthread_attr_get</B><I>attrname.</I> 
</P>
<P><B>pthread_attr_destroy</B> destroys a thread attribute object,
which must not then be reused until it is reinitialized. 
</P>
<P>Attribute objects are consulted only when creating a new thread.
The same attribute object can be used for creating several threads.
Modifying an attribute object after a call to <B>pthread_create</B>
does not change the attributes of the thread previously created. 
</P>
<P>The following thread attributes are supported: 
</P>
<H3 CLASS="western"><A HREF="#toc201" NAME="sect201">affinity</A></H3>
<P>Controls which CPUs the thread is eligible to run on. If not set
then the thread will inherit the <I>cpuset</I> from it's parent
[creator thread]. See also: <A HREF="pthread_setaffinity_np.html">pthread_setaffinity_np(3)</A>,
<A HREF="pthread_getaffinity_np.html">pthread_getaffinity_np(3)</A>,
<A HREF="sched_setaffinity.html">sched_setaffinity(3)</A>,
<A HREF="sched_getaffinity.html">sched_getaffinity(3)</A>, <A HREF="cpu_set.html">cpu_set(3)</A></P>
<H3 CLASS="western"><A HREF="#toc3" NAME="sect3">detachstate</A></H3>
<P>Control whether the thread is created in the joinable state (value
<B>PTHREAD_CREATE_JOINABLE</B>) or in the detached state (
<B>PTHREAD_CREATE_DETACHED</B>). 
</P>
<P>Default value: <B>PTHREAD_CREATE_JOINABLE</B>. 
</P>
<P>In the joinable state, another thread can synchronize on the
thread termination and recover its termination code using
<A HREF="pthread_join.html"><B>pthread_join</B>(3)</A> . When a
joinable thread terminates, some of the thread resources are kept
allocated, and released only when another thread performs
<A HREF="pthread_join.html"><B>pthread_join</B>(3)</A> on that
thread. 
</P>
<P>In the detached state, the thread's resources are released
immediately when it terminates. <A HREF="pthread_join.html"><B>pthread_join</B>(3)</A>
cannot be used to synchronize on the thread termination. 
</P>
<P>A thread created in the joinable state can later be put in the
detached thread using <A HREF="pthread_detach.html"><B>pthread_detach</B>(3)</A>
. 
</P>
<H3 CLASS="western"><A HREF="#toc202" NAME="sect202">name</A></H3>
<P>Give threads names to aid in tracing during debugging Threads do
not have a default name. See also: <A HREF="pthread_setname_np.html">pthread_setname_np(3)</A>,
<A HREF="pthread_setname_np.html">pthread_getname_np(3)</A>.</P>
<H3 CLASS="western"><A HREF="#toc4" NAME="sect4">schedpolicy</A></H3>
<P>Select the scheduling policy for the thread: one of <B>SCHED_OTHER</B>
(regular, non-real-time scheduling), <B>SCHED_RR</B> (real-time,
round-robin) or <B>SCHED_FIFO</B> (real-time, first-in first-out). 
</P>
<P><B>PThreads4W</B> only supports <B>SCHED_OTHER</B> - attempting
to set one of the other policies will return an error ENOTSUP.</P>
<P>Default value: <B>SCHED_OTHER</B>. 
</P>
<P><B>PThreads4W</B> only supports <B>SCHED_OTHER</B> - attempting
to set one of the other policies will return an error ENOTSUP.</P>
<P>The scheduling policy of a thread can be changed after creation
with <A HREF="pthread_setschedparam.html"><B>pthread_setschedparam</B>(3)</A>
. 
</P>
<H3 CLASS="western"><A HREF="#toc5" NAME="sect5">schedparam</A></H3>
<P>Contain the scheduling parameters (essentially, the scheduling
priority) for the thread.</P>
<P><B>PThreads4W</B> supports the priority levels defined by the
Windows system it is running on. Under Windows, thread priorities are
relative to the process priority class, which must be set via the
Windows W32 API.</P>
<P>Default value: priority is 0 (Win32 level <B>THREAD_PRIORITY_NORMAL</B>).
</P>
<P>The scheduling priority of a thread can be changed after creation
with <A HREF="pthread_setschedparam.html"><B>pthread_setschedparam</B>(3)</A>
. 
</P>
<H3 CLASS="western"><A HREF="#toc6" NAME="sect6">inheritsched</A></H3>
<P>Indicate whether the scheduling policy and scheduling parameters
for the newly created thread are determined by the values of the
<I>schedpolicy</I> and <I>schedparam</I> attributes (value
<B>PTHREAD_EXPLICIT_SCHED</B>) or are inherited from the parent
thread (value <B>PTHREAD_INHERIT_SCHED</B>). 
</P>
<P>Default value: <B>PTHREAD_EXPLICIT_SCHED</B>. 
</P>
<H3 CLASS="western"><A HREF="#toc7" NAME="sect7">scope</A></H3>
<P>Define the scheduling contention scope for the created thread. The
only value supported in the <B>PThreads4W</B> implementation is
<B>PTHREAD_SCOPE_SYSTEM</B>, meaning that the threads contend for CPU
time with all processes running on the machine. The other value
specified by the standard, <B>PTHREAD_SCOPE_PROCESS</B>, means that
scheduling contention occurs only between the threads of the running
process.</P>
<P><B>PThreads4W</B> only supports <B>PTHREAD_SCOPE_SYSTEM</B>.</P>
<P>Default value: <B>PTHREAD_SCOPE_SYSTEM</B>. 
</P>
<H2 CLASS="western"><A HREF="#toc8" NAME="sect8">Return Value</A></H2>
<P>All functions return 0 on success and a non-zero error code on
error. On success, the <B>pthread_attr_get</B><I>attrname</I>
functions also store the current value of the attribute <I>attrname</I>
in the location pointed to by their second argument. 
</P>
<H2 CLASS="western"><A HREF="#toc9" NAME="sect9">Errors</A></H2>
<P>The <B>pthread_attr_setaffinity</B> function returns the following
error codes on error: 
</P>
<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B></DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		one or both of the specified <I>attribute</I> or <I>cpuset</I>
		argument is invalid.</DD>
</DL>
<P>
The <B>pthread_attr_setdetachstate</B> function returns the following
error codes on error: 
</P>
<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		the specified <I>detachstate</I> is not one of
		<B>PTHREAD_CREATE_JOINABLE</B> or <B>PTHREAD_CREATE_DETACHED</B>. 
		</DD>
</DL>
<P>
The <B>pthread_attr_setschedparam</B> function returns the following
error codes on error: 
</P>
<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		the priority specified in <I>param</I> is outside the range of
		allowed priorities for the scheduling policy currently in <I>attr</I>
		(1 to 99 for <B>SCHED_FIFO</B> and <B>SCHED_RR</B>; 0 for
		<B>SCHED_OTHER</B>). 
		</DD>
</DL>
<P>
The <B>pthread_attr_setschedpolicy</B> function returns the following
error codes on error: 
</P>
<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		the specified <I>policy</I> is not one of <B>SCHED_OTHER</B>,
		<B>SCHED_FIFO</B>, or <B>SCHED_RR</B>. 
		</DD><DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		<B>ENOTSUP</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		<I>policy</I> is not <B>SCHED_OTHER</B>, the only value supported
		by <B>PThreads4W</B>.</DD>
</DL>
<P>
The <B>pthread_attr_setinheritsched</B> function returns the
following error codes on error: 
</P>
<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		the specified <I>inherit</I> is not one of <B>PTHREAD_INHERIT_SCHED</B>
		or <B>PTHREAD_EXPLICIT_SCHED</B>. 
		</DD>
</DL>
<P>
The <B>pthread_attr_setscope</B> function returns the following error
codes on error: 
</P>
<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		the specified <I>scope</I> is not one of <B>PTHREAD_SCOPE_SYSTEM</B>
		or <B>PTHREAD_SCOPE_PROCESS</B>. 
		</DD><DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		<B>ENOTSUP</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		the specified <I>scope</I> is <B>PTHREAD_SCOPE_PROCESS</B> (not
		supported by <B>PThreads4W</B>). 
		</DD>
</DL>
<H2 CLASS="western">
<A HREF="#toc10" NAME="sect10">Author</A></H2>
<P>Xavier Leroy &lt;<EMAIL>&gt; 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<H2 CLASS="western"><A HREF="#toc11" NAME="sect11">See Also</A></H2>
<P><A HREF="pthread_create.html"><B>pthread_create</B>(3)</A> ,
<A HREF="pthread_join.html"><B>pthread_join</B>(3)</A> ,
<A HREF="pthread_detach.html"><B>pthread_detach</B>(3)</A> ,
<A HREF="pthread_setname_np.html">pthread_setname_np(3)</A>,
<A HREF="pthread_setname_np.html">pthread_getname_np(3)</A>,
<A HREF="pthread_setschedparam.html"><B>pthread_setschedparam</B>(3)</A>
, <A HREF="pthread_setaffinity_np.html">pthread_setaffinity_np(3)</A>
, <A HREF="pthread_getaffinity_np.html">pthread_getaffinity_np(3)</A>
, <A HREF="sched_setaffinity.html">sched_setaffinity(3)</A> ,
<A HREF="sched_getaffinity.html">sched_getaffinity(3)</A> ,
<A HREF="cpu_set.html">cpu_set(3)</A> . 
</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<UL>
		<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect201" NAME="toc201">affinity</A></P>
		<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">detachstate</A>
				</P>
		<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect202" NAME="toc202">name</A></P>
		<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">schedpolicy</A>
				</P>
		<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">schedparam</A>
				</P>
		<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect6" NAME="toc6">inheritsched</A>
				</P>
		<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect7" NAME="toc7">scope</A>
				</P>
	</UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect8" NAME="toc8">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect9" NAME="toc9">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect10" NAME="toc10">Author</A>
		</P>
	<LI><P><A HREF="#sect11" NAME="toc11">See Also</A> 
	</P>
</UL>
</BODY>
</HTML>