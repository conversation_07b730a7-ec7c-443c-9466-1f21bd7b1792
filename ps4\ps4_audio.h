// Copyright 2025 <Copyright Owner>

#pragma once

#include "../memory/ps4_mmu.h"
#include <cstddef>
#include <istream>
#include <mutex>
#include <ostream>
#include <portaudio.h>
#include <queue>
#include <shared_mutex>
#include <vector>

namespace ps4 {

/**
 * @brief Exception for audio-related errors.
 */
struct AudioException : std::runtime_error {
  explicit AudioException(const std::string &msg) : std::runtime_error(msg) {}
};

/**
 * @brief Audio configuration constants.
 */
constexpr int MAX_CHANNELS = 8;                  ///< Maximum number of channels
constexpr int SAMPLE_RATE = 48000;               ///< Sample rate (Hz)
constexpr unsigned long FRAMES_PER_BUFFER = 256; ///< Frames per buffer

/**
 * @brief Structure representing an audio stream.
 */
struct AudioStream {
  PaStream *stream = nullptr;             ///< PortAudio stream
  int sampleRate = SAMPLE_RATE;           ///< Sample rate (Hz)
  int numChannels = 2;                    ///< Number of channels
  float volume = 1.0f;                    ///< Volume (0.0 to 1.0)
  bool isPlaying = false;                 ///< Playing state
  std::queue<std::vector<float>> buffers; ///< Audio buffer queue
  uint64_t bufferUnderruns = 0;           ///< Number of buffer underruns
  uint64_t cacheHits = 0;                 ///< Cache hits for buffer access
  uint64_t cacheMisses = 0;               ///< Cache misses for buffer access
};

/**
 * @brief Manages a single audio output stream.
 */
class AudioDevice {
public:
  /**
   * @brief Constructs the audio device.
   */
  AudioDevice();

  /**
   * @brief Destructor, cleaning up resources.
   */
  ~AudioDevice();

  /**
   * @brief PortAudio stream callback.
   * @param input Input buffer (unused).
   * @param output Output buffer.
   * @param frameCount Number of frames.
   * @param timeInfo Timing information.
   * @param statusFlags Status flags.
   * @param userData Pointer to AudioDevice.
   * @return paContinue to continue streaming.
   */
  static int StreamCallback(const void *input, void *output,
                            unsigned long frameCount,
                            const PaStreamCallbackTimeInfo *timeInfo,
                            PaStreamCallbackFlags statusFlags, void *userData);

  /**
   * @brief Initializes the audio device.
   * @param deviceIndex PortAudio device index (-1 for default).
   * @return True on success, false otherwise.
   */
  bool Initialize(PaDeviceIndex deviceIndex);

  /**
   * @brief Starts audio playback.
   */
  void Start();

  /**
   * @brief Stops audio playback.
   */
  void Stop();

  /**
   * @brief Queues an audio buffer for playback.
   * @param buffer Audio data (interleaved floats).
   */
  void QueueBuffer(const std::vector<float> &buffer);

  /**
   * @brief Sets the volume level.
   * @param volume Volume (0.0 to 1.0).
   */
  void SetVolume(float volume);

  /**
   * @brief Reconfigures the stream with new parameters.
   * @param sampleRate New sample rate.
   * @param numChannels New number of channels.
   * @return True on success, false otherwise.
   */
  bool Reconfigure(int sampleRate, int numChannels);

  /**
   * @brief Audio statistics.
   */
  struct Stats {
    uint64_t bufferUnderruns = 0; ///< Number of buffer underruns
    uint64_t totalLatencyUs = 0;  ///< Total latency (microseconds)
    uint64_t cacheHits = 0;       ///< Cache hits for buffer access
    uint64_t cacheMisses = 0;     ///< Cache misses for buffer access
    uint64_t bufferQueueSize = 0; ///< Current buffer queue size
    int numChannels = 0;          ///< Number of channels
  };

  /**
   * @brief Retrieves audio statistics.
   * @return Current statistics.
   */
  Stats &GetStats();

  /**
   * @brief Saves the audio device state to a stream.
   * @param out Output stream.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the audio device state from a stream.
   * @param in Input stream.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Expose channel count without copying Stats
   */
  int GetNumChannels() const { return audioStream_.numChannels; }

private:
  AudioStream audioStream_;               ///< Audio stream configuration
  bool initialized_ = false;              ///< Initialization state
  mutable std::shared_mutex streamMutex_; ///< Mutex for thread safety
  Stats stats_;                           ///< Audio statistics
};

/**
 * @brief Enhanced audio format support for PS4.
 */
enum class PS4AudioFormat {
  PCM_S16 = 0, ///< 16-bit signed PCM
  PCM_S24 = 1, ///< 24-bit signed PCM
  PCM_S32 = 2, ///< 32-bit signed PCM
  PCM_F32 = 3, ///< 32-bit float PCM
  ADPCM = 4,   ///< ADPCM compressed
  AT9 = 5,     ///< ATRAC9 compressed
  MP3 = 6,     ///< MP3 compressed
  AAC = 7,     ///< AAC compressed
  OPUS = 8     ///< Opus compressed
};

/**
 * @brief Audio3D processing modes.
 */
enum class Audio3DMode {
  DISABLED = 0, ///< No 3D processing
  HRTF = 1,     ///< Head-Related Transfer Function
  SURROUND = 2, ///< Surround sound processing
  BINAURAL = 3  ///< Binaural processing
};

/**
 * @brief DSP effect types.
 */
enum class DSPEffectType {
  REVERB = 0,     ///< Reverb effect
  DELAY = 1,      ///< Delay effect
  CHORUS = 2,     ///< Chorus effect
  COMPRESSOR = 3, ///< Dynamic range compressor
  EQUALIZER = 4,  ///< Multi-band equalizer
  LIMITER = 5     ///< Audio limiter
};

/**
 * @brief Enhanced audio buffer structure.
 */
struct EnhancedAudioBuffer {
  std::vector<float> data;
  PS4AudioFormat format;
  uint32_t sampleRate;
  uint32_t channels;
  uint64_t timestamp;
  bool is3D;
  float position[3]; ///< 3D position (x, y, z)
  float velocity[3]; ///< 3D velocity
  uint32_t priority;
  bool looping;
};

/**
 * @brief DSP effect parameters.
 */
struct DSPEffectParams {
  DSPEffectType type;
  float intensity;
  float frequency;
  float feedback;
  bool enabled;
};

/**
 * @brief Emulates the PS4 audio system.
 * @details Manages audio output via AudioDevice, interfacing with MMU for audio
 * data.
 */
class PS4Audio {
public:
  /**
   * @brief Enhanced audio statistics.
   */
  struct Stats {
    uint64_t samplesProcessed = 0;  ///< Total samples processed
    uint64_t totalLatencyUs = 0;    ///< Total latency in microseconds
    uint64_t cacheHits = 0;         ///< Cache hits for audio processing
    uint64_t cacheMisses = 0;       ///< Cache misses for audio processing
    uint64_t errorCount = 0;        ///< Total errors encountered
    uint64_t bufferUnderruns = 0;   ///< Number of buffer underruns
    uint64_t bufferOverruns = 0;    ///< Number of buffer overruns
    uint64_t dspOperations = 0;     ///< DSP operations performed
    uint64_t audio3DOperations = 0; ///< 3D audio operations
    uint64_t compressionOperations =
        0; ///< Audio compression/decompression operations
  };

  /**
   * @brief Constructs the audio system.
   * @param memory Reference to the PS4 MMU.
   */
  explicit PS4Audio(PS4MMU &memory);

  /**
   * @brief Processes audio samples from MMU.
   * @param samples Number of samples to process.
   * @param address MMU address for audio data.
   */
  void ProcessAudio(size_t samples, uint64_t address);

  /**
   * @brief Initializes the audio system.
   * @return True on success, false otherwise.
   */
  bool Initialize();

  /**
   * @brief Shuts down the audio system.
   */
  void Shutdown();

  /**
   * @brief Saves the audio system state.
   * @param out Output stream.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the audio system state.
   * @param in Input stream.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Enables or disables audio output.
   * @param enabled True to enable, false to disable.
   */
  void SetEnabled(bool enabled) {
    if (enabled)
      m_device.Start();
    else
      m_device.Stop();
  }

  /**
   * @brief Enhanced audio processing methods.
   */
  bool ProcessEnhancedAudio(const EnhancedAudioBuffer &buffer);
  bool DecompressAudio(const std::vector<uint8_t> &compressed,
                       PS4AudioFormat format, std::vector<float> &output);
  bool CompressAudio(const std::vector<float> &input, PS4AudioFormat format,
                     std::vector<uint8_t> &output);
  bool ProcessAudio3D(EnhancedAudioBuffer &buffer, const float listenerPos[3],
                      const float listenerOrientation[6], Audio3DMode mode);
  bool ApplyDSPEffect(std::vector<float> &audioData,
                      const DSPEffectParams &params);
  bool
  ProcessMultiChannelAudio(const std::vector<EnhancedAudioBuffer> &channels,
                           std::vector<float> &mixedOutput);

  /**
   * @brief Gets enhanced audio statistics.
   */
  Stats GetStats() const;

private:
  PS4MMU &m_memory;     ///< Reference to MMU
  AudioDevice m_device; ///< Audio device instance

  // Enhanced audio processing members
  mutable Stats m_stats;                     ///< Audio statistics
  std::vector<DSPEffectParams> m_dspEffects; ///< Active DSP effects
  Audio3DMode m_audio3DMode;                 ///< Current 3D audio mode
  float m_listenerPosition[3];               ///< Listener position
  float
      m_listenerOrientation[6]; ///< Listener orientation (forward + up vectors)
  std::vector<EnhancedAudioBuffer>
      m_activeBuffers;                    ///< Currently active audio buffers
  mutable std::shared_mutex m_audioMutex; ///< Mutex for thread safety

  // DSP processing helpers
  bool ProcessReverb(std::vector<float> &audioData, float intensity,
                     float feedback);
  bool ProcessDelay(std::vector<float> &audioData, float delayTime,
                    float feedback);
  bool ProcessChorus(std::vector<float> &audioData, float rate, float depth);
  bool ProcessCompressor(std::vector<float> &audioData, float threshold,
                         float ratio);
  bool ProcessEqualizer(std::vector<float> &audioData,
                        const std::vector<float> &bands);
  bool ProcessLimiter(std::vector<float> &audioData, float threshold);

  // 3D audio processing helpers
  bool ApplyHRTF(std::vector<float> &audioData, const float sourcePos[3]);
  bool ApplySurroundProcessing(std::vector<float> &audioData,
                               uint32_t channels);
  bool ApplyBinauralProcessing(std::vector<float> &audioData);

  // Audio format conversion helpers
  bool ConvertPCMFormat(const std::vector<uint8_t> &input,
                        PS4AudioFormat inputFormat, std::vector<float> &output);
  bool DecompressADPCM(const std::vector<uint8_t> &compressed,
                       std::vector<float> &output);
  bool DecompressAT9(const std::vector<uint8_t> &compressed,
                     std::vector<float> &output);
  bool DecompressMP3(const std::vector<uint8_t> &compressed,
                     std::vector<float> &output);
  bool DecompressAAC(const std::vector<uint8_t> &compressed,
                     std::vector<float> &output);
  bool DecompressOpus(const std::vector<uint8_t> &compressed,
                      std::vector<float> &output);
};

} // namespace ps4