<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_ONCE(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_once - once-only initialization 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> 
</P>
<P><B>pthread_once_t </B><I>once_control</I> <B>= PTHREAD_ONCE_INIT;</B>
</P>
<P><B>int pthread_once(pthread_once_t *</B><I>once_control</I><B>,
void (*</B><I>init_routine</I><B>) (void));</B> 
</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>The purpose of <B>pthread_once</B> is to ensure that a piece of
initialization code is executed at most once. The <I>once_control</I>
argument points to a static or extern variable statically initialized
to <B>PTHREAD_ONCE_INIT</B>. 
</P>
<P>The first time <B>pthread_once</B> is called with a given
<I>once_control</I> argument, it calls <I>init_routine</I> with no
argument and changes the value of the <I>once_control</I> variable to
record that initialization has been performed. Subsequent calls to
<B>pthread_once</B> with the same <B>once_control</B> argument do
nothing. 
</P>
<H2><A HREF="#toc3" NAME="sect3">Cancellation</A></H2>
<P>While <B>pthread_once</B> is not a cancellation point,
<I>init_routine</I> can be. The effect on <I>once_control</I> of a
cancellation inside the <I>init_routine</I> is to leave it as if
<B>pthread_once</B> had not been called by the cancelled thread.</P>
<H2><A HREF="#toc4" NAME="sect4">Return Value</A></H2>
<P STYLE="text-decoration: none"><FONT COLOR="#000000"><B>pthread_once</B>
returns 0 on success, or an error code on failure.</FONT></P>
<H2><A HREF="#toc5" NAME="sect5">Errors</A></H2>
<P>The <B>pthread_once</B> function returns the following error code
on error: 
</P>
<DL>
	<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B> 
		</DT></DL>
</DL>
<P STYLE="margin-left: 2cm">
The once_control or init_routine parameter is NULL.</P>
<H2><A HREF="#toc6" NAME="sect6">Author</A></H2>
<P>Xavier Leroy &lt;<EMAIL>&gt; 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Cancellation</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Errors</A>
		</P>
	<LI><P><A HREF="#sect6" NAME="toc6">Author</A> 
	</P>
</UL>
</BODY>
</HTML>