#pragma once

#include <array>
#include <cstdint>
#include <fstream>
#include <functional>
#include <immintrin.h>
#include <memory>
#include <mutex>
#include <ostream>
#include <queue>
#include <string>
#include <unordered_map>
#include <vector>

#include "cache/cache.h"
#include "cpu/decoded_instruction.h"
#include "cpu/instruction_decoder.h"
#include "cpu/register.h"
#include "cpu/x86_64_pipeline.h"
#include "emulator/apic.h"
#include "jit/x86_64_jit_compiler.h"
#include "memory/ps4_mmu.h"
#include "memory/tlb.h"

namespace ps4 {
class PS4Emulator;
}

namespace x86_64 {

// IMPROVEMENT: Add comprehensive CPU constants
constexpr uint64_t DEFAULT_STACK_POINTER = 0x7FFFFFFF0000ULL;
constexpr uint64_t DEFAULT_RFLAGS = 0x202ULL; // IF=1, bit 1 is always 1
constexpr size_t MAX_INSTRUCTION_LENGTH = 15;
constexpr size_t INSTRUCTION_BUFFER_SIZE = 16;
constexpr size_t XMM_REGISTER_COUNT = 16;
constexpr size_t SIMD_ALIGNMENT = 32;

// Exception vectors
constexpr uint8_t EXC_DE = 0x00;  // Divide Error
constexpr uint8_t EXC_DB = 0x01;  // Debug Exception
constexpr uint8_t EXC_NMI = 0x02; // Non-Maskable Interrupt
constexpr uint8_t EXC_BP = 0x03;  // Breakpoint
constexpr uint8_t EXC_OF = 0x04;  // Overflow
constexpr uint8_t EXC_BR = 0x05;  // Bound Range Exceeded
constexpr uint8_t EXC_UD = 0x06;  // Invalid Opcode
constexpr uint8_t EXC_NM = 0x07;  // Device Not Available
constexpr uint8_t EXC_DF = 0x08;  // Double Fault
constexpr uint8_t EXC_TS = 0x0A;  // Invalid TSS
constexpr uint8_t EXC_NP = 0x0B;  // Segment Not Present
constexpr uint8_t EXC_SS = 0x0C;  // Stack Segment Fault
constexpr uint8_t EXC_GP = 0x0D;  // General Protection Fault
constexpr uint8_t EXC_PF = 0x0E;  // Page Fault
constexpr uint8_t EXC_MF = 0x10;  // Math Fault
constexpr uint8_t EXC_AC = 0x11;  // Alignment Check
constexpr uint8_t EXC_MC = 0x12;  // Machine Check
constexpr uint8_t EXC_XM = 0x13;  // SIMD Floating-Point Exception
constexpr uint8_t EXC_XF = 0x13;  // SIMD Floating-Point Exception (alias)

enum FLAGS : uint64_t {
  FLAG_CF = 1ULL << 0,    // Carry Flag
  FLAG_PF = 1ULL << 2,    // Parity Flag
  FLAG_AF = 1ULL << 4,    // Auxiliary Carry Flag
  FLAG_ZF = 1ULL << 6,    // Zero Flag
  FLAG_SF = 1ULL << 7,    // Sign Flag
  FLAG_TF = 1ULL << 8,    // Trap Flag
  FLAG_IF = 1ULL << 9,    // Interrupt Enable Flag
  FLAG_DF = 1ULL << 10,   // Direction Flag
  FLAG_OF = 1ULL << 11,   // Overflow Flag
  FLAG_IOPL = 3ULL << 12, // I/O Privilege Level
  FLAG_NT = 1ULL << 14,   // Nested Task Flag
  FLAG_RF = 1ULL << 16,   // Resume Flag
  FLAG_VM = 1ULL << 17,   // Virtual 8086 Mode Flag
  FLAG_AC = 1ULL << 18,   // Alignment Check Flag
  FLAG_VIF = 1ULL << 19,  // Virtual Interrupt Flag
  FLAG_VIP = 1ULL << 20,  // Virtual Interrupt Pending Flag
  FLAG_ID = 1ULL << 21,   // ID Flag

  // Commonly used flag masks
  ARITHMETIC_FLAGS_MASK =
      FLAG_CF | FLAG_PF | FLAG_AF | FLAG_ZF | FLAG_SF | FLAG_OF,
  LOGICAL_FLAGS_MASK = FLAG_ZF | FLAG_SF | FLAG_PF | FLAG_CF | FLAG_OF,
  STATUS_FLAGS_MASK =
      FLAG_CF | FLAG_PF | FLAG_AF | FLAG_ZF | FLAG_SF | FLAG_OF | FLAG_DF,
  SYSTEM_FLAGS_MASK = FLAG_TF | FLAG_IF | FLAG_IOPL | FLAG_NT | FLAG_RF |
                      FLAG_VM | FLAG_AC | FLAG_VIF | FLAG_VIP | FLAG_ID
};

struct CPUContext {
  std::array<uint64_t, REGISTER_COUNT> registers;
  uint64_t rflags;
  std::array<__m256i, 16> xmmRegisters;
  uint64_t rip;
  uint64_t rsp;
};

class X86_64CPU {
public:
  X86_64CPU(ps4::PS4Emulator &emulator, ps4::PS4MMU &mmu, uint32_t cpuId);
  ~X86_64CPU();

  bool Initialize();
  void Shutdown();
  void ResetState();
  void QueueInterrupt(uint8_t vector, uint8_t priority);
  void TriggerInterrupt(uint8_t vector, uint64_t errorCode = 0,
                        bool isSoftwareInterrupt = false);
  void ExecuteCycle();
  uint64_t TranslateAddress(uint64_t virtualAddr);
  void Push(uint64_t value, uint8_t sizeInBytes = 8);
  uint64_t Pop(uint8_t sizeInBytes = 8);
  void UpdateFlags(uint64_t op1, uint64_t op2, uint64_t result,
                   uint8_t sizeInBits, bool isSubtract);
  bool CheckCondition(uint8_t conditionCode);
  void SetIDTR(uint64_t base, uint16_t limit);
  void SaveState(std::ostream &out);
  void LoadState(std::istream &in);
  void Execute();
  void SetContext(const CPUContext &ctx);
  uint64_t GetRegister(Register r) const;
  void SetRegister(Register r, uint64_t v);
  void SetRflags(uint64_t f);
  uint64_t GetRflags() const;
  bool GetFlag(uint64_t m) const;
  uint64_t GetCR3() const;
  uint64_t GetCR2() const;
  uint16_t GetCS() const;
  uint16_t GetSS() const;
  uint64_t GetTSSBase() const;
  uint16_t GetKernelSS() const;
  X86_64JITCompiler &GetJITCompiler();
  Pipeline &GetPipeline();
  APIC &GetAPIC() { return apic; }
  uint64_t GetProcessId() const;
  float GetUtilization() const { return utilization; }
  ps4::PS4MMU &GetMMU() { return mmu; }
  ps4::PS4MMU &GetMemory();
  uint32_t GetCPUId() const;
  void InvalidateTLB(uint64_t virtAddr);
  __m256i GetXMMRegister(size_t index) const {
    std::unique_lock<std::recursive_timed_mutex> lock(mutex);
    return xmmRegisters[index];
  }

  void SetXMMRegister(size_t index, __m256i value) {
    std::unique_lock<std::recursive_timed_mutex> lock(mutex);
    xmmRegisters[index] = value;
  }

  std::unordered_map<std::string, uint64_t> GetDiagnostics() const;

  bool SwitchToFiber(uint64_t fiberId);
  bool IsRunning() const;

  void UpdateArithmeticFlags(uint64_t op1, uint64_t op2, uint64_t result,
                             uint8_t sizeInBits, bool isSubtract);

  // Individual flag manipulation
  void SetFlag(uint64_t flag, bool value);
  void UpdateLogicalFlags(uint64_t result, uint8_t sizeInBits);

  uint64_t ReadOperandValue(const DecodedInstruction::Operand &operand) const;
  void WriteOperandValue(const DecodedInstruction::Operand &operand,
                         uint64_t value);

  // Enhanced SIMD operand handling
  __m256i ReadXmmOperandValue(const DecodedInstruction::Operand &operand) const;
  void WriteXmmOperandValue(const DecodedInstruction::Operand &operand,
                            const __m256i &value);

  uint64_t
  CalculateMemoryAddress(const DecodedInstruction::Operand &operand) const;
  void WriteMemoryOperand(const DecodedInstruction::Operand &operand,
                          uint64_t value);
  void WriteXmmOperand(const DecodedInstruction::Operand &operand,
                       const __m256i &value);

private:
  uint64_t _getRegister(Register r) const;
  void _setRegister(Register r, uint64_t v);
  void FetchDecodeExecute();
  bool CalculateParity(uint64_t value);
  void UpdateUtilization(const std::chrono::steady_clock::time_point &start);

  // Member variables ordered for optimal memory layout (largest to smallest
  // alignment)
  std::array<__m256i, 16> xmmRegisters;        // 32-byte alignment
  ps4::PS4Emulator &m_emulator;                // 8-byte alignment (reference)
  ps4::PS4MMU &mmu;                            // 8-byte alignment (reference)
  uint64_t rflags;                             // 8-byte alignment
  std::unique_ptr<X86_64JITCompiler> jit;      // 8-byte alignment
  std::unique_ptr<InstructionDecoder> decoder; // 8-byte alignment
  std::unique_ptr<Pipeline> pipeline;          // 8-byte alignment
  uint64_t idtrBase = 0;                       // 8-byte alignment
  uint64_t cr3 = 0;                            // 8-byte alignment
  uint64_t cr2 = 0;                            // 8-byte alignment
  uint64_t tssBase = 0;                        // 8-byte alignment
  uint64_t processId = 0;                      // 8-byte alignment
  struct InterruptRequest {
    uint8_t vector;
    uint8_t priority;
    bool operator<(const InterruptRequest &other) const {
      return priority < other.priority;
    }
  };
  std::priority_queue<InterruptRequest>
      interruptQueue;                       // 8-byte alignment (container)
  mutable std::recursive_timed_mutex mutex; // 8-byte alignment
  ps4::TLB tlb;                             // 8-byte alignment
  std::array<uint64_t, REGISTER_COUNT> registers; // 8-byte alignment
  APIC apic;                                      // 4-byte alignment
  uint32_t m_cpuId;                               // 4-byte alignment
  float utilization;                              // 4-byte alignment
  uint16_t idtrLimit = 0;                         // 2-byte alignment
  uint16_t cs = 0;                                // 2-byte alignment
  uint16_t ss = 0;                                // 2-byte alignment
  uint16_t kernelSS = 0;                          // 2-byte alignment
  bool running;                                   // 1-byte alignment
};
} // namespace x86_64
