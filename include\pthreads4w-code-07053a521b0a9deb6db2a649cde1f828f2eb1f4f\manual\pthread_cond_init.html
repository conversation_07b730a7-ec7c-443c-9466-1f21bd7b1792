<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_COND_INIT(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_cond_init, pthread_cond_destroy, pthread_cond_signal,
pthread_cond_broadcast, pthread_cond_wait, pthread_cond_timedwait -
operations on conditions 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> 
</P>
<P><B>pthread_cond_t </B><I>cond</I> <B>= PTHREAD_COND_INITIALIZER;</B>
</P>
<P><B>int pthread_cond_init(pthread_cond_t *</B><I>cond</I><B>,
pthread_condattr_t *</B><I>cond_attr</I><B>);</B> 
</P>
<P><B>int pthread_cond_signal(pthread_cond_t *</B><I>cond</I><B>);</B>
</P>
<P><B>int pthread_cond_broadcast(pthread_cond_t *</B><I>cond</I><B>);</B>
</P>
<P><B>int pthread_cond_wait(pthread_cond_t *</B><I>cond</I><B>,
pthread_mutex_t *</B><I>mutex</I><B>);</B> 
</P>
<P><B>int pthread_cond_timedwait(pthread_cond_t *</B><I>cond</I><B>,
pthread_mutex_t *</B><I>mutex</I><B>, const struct timespec
*</B><I>abstime</I><B>);</B> 
</P>
<P><B>int pthread_cond_destroy(pthread_cond_t *</B><I>cond</I><B>);</B>
</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>A condition (short for ‘‘condition variable’’) is a
synchronization device that allows threads to suspend execution and
relinquish the processors until some predicate on shared data is
satisfied. The basic operations on conditions are: signal the
condition (when the predicate becomes true), and wait for the
condition, suspending the thread execution until another thread
signals the condition. 
</P>
<P>A condition variable must always be associated with a mutex, to
avoid the race condition where a thread prepares to wait on a
condition variable and another thread signals the condition just
before the first thread actually waits on it. 
</P>
<P><B>pthread_cond_init</B> initializes the condition variable <I>cond</I>,
using the condition attributes specified in <I>cond_attr</I>, or
default attributes if <I>cond_attr</I> is <B>NULL</B>. 
</P>
<P>Variables of type <B>pthread_cond_t</B> can also be initialized
statically, using the constant <B>PTHREAD_COND_INITIALIZER</B>. In
the <B>PThreads4W</B> implementation, an application should still
call <B>pthread_cond_destroy</B> at some point to ensure that any
resources consumed by the condition variable are released.</P>
<P><B>pthread_cond_signal</B> restarts one of the threads that are
waiting on the condition variable <I>cond</I>. If no threads are
waiting on <I>cond</I>, nothing happens. If several threads are
waiting on <I>cond</I>, exactly one is restarted, but it is not
specified which. 
</P>
<P><B>pthread_cond_broadcast</B> restarts all the threads that are
waiting on the condition variable <I>cond</I>. Nothing happens if no
threads are waiting on <I>cond</I>. 
</P>
<P><B>pthread_cond_wait</B> atomically unlocks the <I>mutex</I> (as
per <B>pthread_unlock_mutex</B>) and waits for the condition variable
<I>cond</I> to be signalled. The thread execution is suspended and
does not consume any CPU time until the condition variable is
signalled. The <I>mutex</I> must be locked by the calling thread on
entrance to <B>pthread_cond_wait</B>. Before returning to the calling
thread, <B>pthread_cond_wait</B> re-acquires <I>mutex</I> (as per
<B>pthread_lock_mutex</B>). 
</P>
<P>Unlocking the mutex and suspending on the condition variable is
done atomically. Thus, if all threads always acquire the mutex before
signalling the condition, this guarantees that the condition cannot
be signalled (and thus ignored) between the time a thread locks the
mutex and the time it waits on the condition variable. 
</P>
<P><B>pthread_cond_timedwait</B> atomically unlocks <I>mutex</I> and
waits on <I>cond</I>, as <B>pthread_cond_wait</B> does, but it also
bounds the duration of the wait. If <I>cond</I> has not been
signalled within the amount of time specified by <I>abstime</I>, the
mutex <I>mutex</I> is re-acquired and <B>pthread_cond_timedwait</B>
returns the error <B>ETIMEDOUT</B>. The <I>abstime</I> parameter
specifies an absolute time, with the same origin as <A HREF="time.html"><B>time</B>(2)</A>
and <A HREF="gettimeofday.html"><B>gettimeofday</B>(2)</A>. 
</P>
<P><B>pthread_cond_destroy</B> destroys a condition variable, freeing
the resources it might hold. No threads must be waiting on the
condition variable on entrance to <B>pthread_cond_destroy</B>.</P>
<H2><A HREF="#toc3" NAME="sect3">Cancellation</A></H2>
<P><B>pthread_cond_wait</B> and <B>pthread_cond_timedwait</B> are
cancellation points. If a thread is cancelled while suspended in one
of these functions, the thread immediately resumes execution, then
locks again the <I>mutex</I> argument to <B>pthread_cond_wait</B> and
<B>pthread_cond_timedwait</B>, and finally executes the cancellation.
Consequently, cleanup handlers are assured that <I>mutex</I> is
locked when they are called. 
</P>
<H2><A HREF="#toc4" NAME="sect4">Async-signal Safety</A></H2>
<P>The condition functions are not async-signal safe, and should not
be called from a signal handler. In particular, calling
<B>pthread_cond_signal</B> or <B>pthread_cond_broadcast</B> from a
signal handler may deadlock the calling thread. 
</P>
<H2><A HREF="#toc5" NAME="sect5">Return Value</A></H2>
<P>All condition variable functions return 0 on success and a
non-zero error code on error. 
</P>
<H2><A HREF="#toc6" NAME="sect6">Errors</A></H2>
<P><B>pthread_cond_init</B>, <B>pthread_cond_signal</B>,
<B>pthread_cond_broadcast</B>, and <B>pthread_cond_wait</B> never
return an error code. 
</P>
<P>The <B>pthread_cond_init</B> function returns the following error
codes on error: 
</P>
<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		The <I>cond</I> argument is invalid. 
		</DD><DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		<B>ENOMEM</B> 
		</DT>
</DL>
<BLOCKQUOTE STYLE="margin-left: 4cm">
There was not enough memory to allocate the condition variable. 
</BLOCKQUOTE>
<P>The <B>pthread_cond_signal</B> function returns the following
error codes on error: 
</P>
<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		The <I>cond</I> argument is invalid. 
		</DD>
</DL>
<P>
The <B>pthread_cond_broadcast</B> function returns the following
error codes on error: 
</P>
<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		The <I>cond</I> argument is invalid. 
		</DD>
</DL>
<P>
The <B>pthread_cond_wait</B> function returns the following error
codes on error: 
</P>
<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		The <I>cond</I> argument is invalid. 
		</DD><DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		<B>ENOMEM</B> 
		</DT>
</DL>
<BLOCKQUOTE STYLE="margin-left: 4cm">
There was not enough memory to allocate the statically initialised
condition variable. Statically initialised condition variables are
dynamically allocated by the first thread to wait on them.</BLOCKQUOTE>
<P>The <B>pthread_cond_timedwait</B> function returns the following
error codes on error: 
</P>
<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B> 
		</DT>
</DL>
<P STYLE="margin-left: 2cm">
The <I>cond</I> argument is invalid. 
</P>
<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>ETIMEDOUT</B>
				</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		The condition variable was not signalled before the timeout
		specified by <I>abstime</I> 
		</DD><DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		<B>ENOMEM</B> 
		</DT>
</DL>
<BLOCKQUOTE STYLE="margin-left: 4cm">
There was not enough memory to allocate the statically initialised
condition variable. Statically initialised condition variables are
dynamically allocated by the first thread to wait on them. 
</BLOCKQUOTE>
<P>The <B>pthread_cond_destroy</B> function returns the following
error code on error: 
</P>
<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B> 
		</DT>
</DL>
<P STYLE="margin-left: 2cm; margin-right: 1cm">
The <I>cond</I> argument is invalid. 
</P>
<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EBUSY</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		Some threads are currently waiting on <I>cond</I>. 
		</DD>
</DL>
<H2>
<A HREF="#toc7" NAME="sect7">Author</A></H2>
<P>Xavier Leroy &lt;<EMAIL>&gt; 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<H2><A HREF="#toc8" NAME="sect8">See Also</A></H2>
<P><A HREF="pthread_condattr_init.html"><B>pthread_condattr_init</B>(3)</A>
, <A HREF="pthread_mutex_lock.html"><B>pthread_mutex_lock</B>(3)</A>
, <A HREF="pthread_mutex_unlock.html"><B>pthread_mutex_unlock</B>(3)</A>
, <A HREF="pthread_cancel.html"><B>pthread_cancel(3)</B></A>. 
</P>
<H2><A HREF="#toc9" NAME="sect9">Example</A></H2>
<P>Consider two shared variables <I>x</I> and <I>y</I>, protected by
the mutex <I>mut</I>, and a condition variable <I>cond</I> that is to
be signaled whenever <I>x</I> becomes greater than <I>y</I>. 
</P>
<PRE STYLE="margin-left: 1cm; margin-right: 1cm">int x,y;
pthread_mutex_t mut = PTHREAD_MUTEX_INITIALIZER;
pthread_cond_t cond = PTHREAD_COND_INITIALIZER;</PRE><BLOCKQUOTE>
Waiting until <I>x</I> is greater than <I>y</I> is performed as
follows: 
</BLOCKQUOTE>
<PRE STYLE="margin-left: 1.01cm">pthread_mutex_lock(&amp;mut);
while (x &lt;= y) {
        pthread_cond_wait(&amp;cond, &amp;mut);
}
/* operate on x and y */
pthread_mutex_unlock(&amp;mut);</PRE><BLOCKQUOTE STYLE="margin-left: 3.01cm">
Modifications on <I>x</I> and <I>y</I> that may cause <I>x</I> to
become greater than <I>y</I> should signal the condition if needed: 
</BLOCKQUOTE>
<PRE STYLE="margin-left: 1.01cm">pthread_mutex_lock(&amp;mut);
/* modify x and y */
if (x &gt; y) pthread_cond_broadcast(&amp;cond);
pthread_mutex_unlock(&amp;mut);</PRE><BLOCKQUOTE STYLE="margin-left: 3.01cm">
If it can be proved that at most one waiting thread needs to be waken
up (for instance, if there are only two threads communicating through
<I>x</I> and <I>y</I>), <B>pthread_cond_signal</B> can be used as a
slightly more efficient alternative to <B>pthread_cond_broadcast</B>.
If in doubt, use <B>pthread_cond_broadcast</B>. 
</BLOCKQUOTE>
<BLOCKQUOTE STYLE="margin-left: 3.01cm">To wait for <I>x</I> to
become greater than <I>y</I> with a timeout of 5 seconds, do: 
</BLOCKQUOTE>
<PRE STYLE="margin-left: 1.01cm">struct timeval now;
struct timespec timeout;
int retcode;
pthread_mutex_lock(&amp;mut);
gettimeofday(&amp;now);
timeout.tv_sec = now.tv_sec + 5;
timeout.tv_nsec = now.tv_usec * 1000;
retcode = 0;
while (x &lt;= y &amp;&amp; retcode != ETIMEDOUT) {
        retcode = pthread_cond_timedwait(&amp;cond, &amp;mut, &amp;timeout);
}
if (retcode == ETIMEDOUT) {
        /* timeout occurred */
} else {
        /* operate on x and y */
}
pthread_mutex_unlock(&amp;mut);</PRE>
<HR>
<BLOCKQUOTE STYLE="margin-left: 0cm; margin-right: 0cm"><A NAME="toc"></A>
<B>Table of Contents</B></BLOCKQUOTE>
<UL>
	<LI><BLOCKQUOTE STYLE="margin-right: 0cm; margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-right: 0cm; margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-right: 0cm; margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-right: 0cm; margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Cancellation</A>
		</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-right: 0cm; margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Async-signal
	Safety</A> 
	</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-right: 0cm; margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Return
	Value</A> 
	</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-right: 0cm; margin-bottom: 0cm"><A HREF="#sect6" NAME="toc6">Errors</A>
		</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-right: 0cm; margin-bottom: 0cm"><A HREF="#sect7" NAME="toc7">Author</A>
		</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-right: 0cm; margin-bottom: 0cm"><A HREF="#sect8" NAME="toc8">See
	Also</A> 
	</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-right: 0cm"><A HREF="#sect9" NAME="toc9">Example</A>
		</BLOCKQUOTE>
</UL>
</BODY>
</HTML>
