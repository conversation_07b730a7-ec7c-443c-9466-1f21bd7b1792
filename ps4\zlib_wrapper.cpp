// Copyright 2025 <Copyright Owner>

#include "zlib_wrapper.h"
#include <algorithm>
#include <chrono>
#include <filesystem>
#include <fstream>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <string>
#include <vector>
#include <zlib.h>

namespace ps4 {

/**
 * @brief Constructs the zlib wrapper.
 */
ZlibWrapper::ZlibWrapper()
    : m_compressedSize(0), m_uncompressedSize(0), m_compressionRatio(0.0f) {
  auto start = std::chrono::steady_clock::now();
  m_stats = Stats();
  spdlog::info("ZlibWrapper constructed");
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Destructor, cleaning up resources.
 */
ZlibWrapper::~ZlibWrapper() noexcept {
  auto start = std::chrono::steady_clock::now();
  spdlog::info("ZlibWrapper destroyed");
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Compresses data in memory.
 * @param input Input data.
 * @param output Output compressed data.
 * @param level Compression level.
 * @param strategy Compression strategy.
 * @return True on success, false otherwise.
 */
bool ZlibWrapper::Compress(const std::vector<uint8_t> &input,
                           std::vector<uint8_t> &output, CompressionLevel level,
                           CompressionStrategy strategy) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_zlibMutex);
  try {
    if (input.empty()) {
      SetError("Input data is empty");
      spdlog::error("ZlibWrapper::Compress: Input data is empty");
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ZlibException("Empty input data");
    }
    z_stream stream = {};
    stream.zalloc = Z_NULL;
    stream.zfree = Z_NULL;
    stream.opaque = Z_NULL;
    int zLevel = static_cast<int>(level);
    int zStrategy = static_cast<int>(strategy);
    if (deflateInit2(&stream, zLevel, Z_DEFLATED, 15, 8, zStrategy) != Z_OK) {
      SetError("Failed to initialize zlib compression");
      spdlog::error("ZlibWrapper::Compress: deflateInit2 failed");
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ZlibException("Zlib compression initialization failed");
    }
    stream.next_in = const_cast<Bytef *>(input.data());
    stream.avail_in = input.size();
    output.resize(compressBound(input.size()));
    stream.next_out = output.data();
    stream.avail_out = output.size();
    int result = deflate(&stream, Z_FINISH);
    if (result != Z_STREAM_END) {
      SetError("Compression failed: " +
               std::string(stream.msg ? stream.msg : "Unknown error"));
      spdlog::error("ZlibWrapper::Compress: deflate failed: {}",
                    stream.msg ? stream.msg : "Unknown error");
      deflateEnd(&stream);
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ZlibException("Zlib compression failed");
    }
    deflateEnd(&stream);
    output.resize(stream.total_out);
    UpdateCompressionStats(input.size(), output.size());
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("ZlibWrapper::Compress: Compressed {} bytes to {} bytes, "
                 "ratio={:.2f}, latency={}us",
                 m_uncompressedSize, m_compressedSize, m_compressionRatio,
                 m_stats.totalLatencyUs);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Compress failed: {}", e.what());
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Decompresses data in memory.
 * @param input Input compressed data.
 * @param output Output decompressed data.
 * @param expectedSize Expected output size (0 for unknown).
 * @return True on success, false otherwise.
 */
bool ZlibWrapper::Decompress(const std::vector<uint8_t> &input,
                             std::vector<uint8_t> &output,
                             size_t expectedSize) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_zlibMutex);
  try {
    if (input.empty()) {
      SetError("Input data is empty");
      spdlog::error("ZlibWrapper::Decompress: Input data is empty");
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ZlibException("Empty input data");
    }

    // Try simple decompression first if expected size is known
    if (expectedSize > 0) {
      if (DecompressSimple(input, output, expectedSize)) {
        UpdateCompressionStats(output.size(), input.size());
        m_stats.operationCount++;
        m_stats.cacheHits++;
        auto end = std::chrono::steady_clock::now();
        m_stats.totalLatencyUs +=
            std::chrono::duration_cast<std::chrono::microseconds>(end - start)
                .count();
        spdlog::info(
            "ZlibWrapper::Decompress: Decompressed {} bytes to {} bytes, "
            "latency={}us",
            input.size(), output.size(), m_stats.totalLatencyUs);
        return true;
      }
    }

    // Fall back to chunked decompression for unknown sizes or if simple method
    // failed
    if (DecompressChunked(input, output)) {
      UpdateCompressionStats(output.size(), input.size());
      m_stats.operationCount++;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::info(
          "ZlibWrapper::Decompress: Chunked decompressed {} bytes to {} bytes, "
          "latency={}us",
          input.size(), output.size(), m_stats.totalLatencyUs);
      return true;
    }

    SetError("All decompression methods failed");
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    return false;
  } catch (const std::exception &e) {
    spdlog::error("Decompress failed: {}", e.what());
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Compresses a file.
 * @param inputPath Input file path.
 * @param outputPath Output file path.
 * @param level Compression level.
 * @param strategy Compression strategy.
 * @return True on success, false otherwise.
 */
bool ZlibWrapper::CompressFile(const std::string &inputPath,
                               const std::string &outputPath,
                               CompressionLevel level,
                               CompressionStrategy strategy) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_zlibMutex);
  try {
    lock.unlock();
    std::ifstream inFile(inputPath, std::ios::binary);
    if (!inFile) {
      SetError("Failed to open input file: " + inputPath);
      spdlog::error("ZlibWrapper::CompressFile: Failed to open input file: {}",
                    inputPath);
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ZlibException("Failed to open input file");
    }
    std::vector<uint8_t> input((std::istreambuf_iterator<char>(inFile)), {});
    inFile.close();
    lock.lock();
    std::vector<uint8_t> output;
    if (!Compress(input, output, level, strategy)) {
      spdlog::error("ZlibWrapper::CompressFile: Compression failed for {}",
                    inputPath);
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ZlibException("File compression failed");
    }
    lock.unlock();
    std::filesystem::create_directories(
        std::filesystem::path(outputPath).parent_path());
    std::ofstream outFile(outputPath, std::ios::binary);
    if (!outFile) {
      SetError("Failed to open output file: " + outputPath);
      spdlog::error("ZlibWrapper::CompressFile: Failed to open output file: {}",
                    outputPath);
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ZlibException("Failed to open output file");
    }
    outFile.write(reinterpret_cast<const char *>(output.data()), output.size());
    if (!outFile.good()) {
      spdlog::error("ZlibWrapper::CompressFile: Failed to write to {}",
                    outputPath);
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ZlibException("Failed to write output file");
    }
    outFile.close();
    lock.lock();
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("ZlibWrapper::CompressFile: Compressed {} to {}, size={} "
                 "bytes, latency={}us",
                 inputPath, outputPath, output.size(), m_stats.totalLatencyUs);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CompressFile failed: {}", e.what());
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Decompresses a file.
 * @param inputPath Input file path.
 * @param outputPath Output file path.
 * @return True on success, false otherwise.
 */
bool ZlibWrapper::DecompressFile(const std::string &inputPath,
                                 const std::string &outputPath) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_zlibMutex);
  try {
    lock.unlock();
    std::ifstream inFile(inputPath, std::ios::binary);
    if (!inFile) {
      SetError("Failed to open input file: " + inputPath);
      spdlog::error(
          "ZlibWrapper::DecompressFile: Failed to open input file: {}",
          inputPath);
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ZlibException("Failed to open input file");
    }
    std::vector<uint8_t> input((std::istreambuf_iterator<char>(inFile)), {});
    inFile.close();
    lock.lock();
    std::vector<uint8_t> output;
    if (!Decompress(input, output)) {
      spdlog::error("ZlibWrapper::DecompressFile: Decompression failed for {}",
                    inputPath);
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ZlibException("File decompression failed");
    }
    lock.unlock();
    std::filesystem::create_directories(
        std::filesystem::path(outputPath).parent_path());
    std::ofstream outFile(outputPath, std::ios::binary);
    if (!outFile) {
      SetError("Failed to open output file: " + outputPath);
      spdlog::error(
          "ZlibWrapper::DecompressFile: Failed to open output file: {}",
          outputPath);
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ZlibException("Failed to open output file");
    }
    outFile.write(reinterpret_cast<const char *>(output.data()), output.size());
    if (!outFile.good()) {
      spdlog::error("ZlibWrapper::DecompressFile: Failed to write to {}",
                    outputPath);
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ZlibException("Failed to write output file");
    }
    outFile.close();
    lock.lock();
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("ZlibWrapper::DecompressFile: Decompressed {} to {}, size={} "
                 "bytes, latency={}us",
                 inputPath, outputPath, output.size(), m_stats.totalLatencyUs);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("DecompressFile failed: {}", e.what());
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Gets the last error message.
 * @return Last error message.
 */
std::string ZlibWrapper::GetLastError() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_zlibMutex);
  try {
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return m_lastError;
  } catch (const std::exception &e) {
    spdlog::error("GetLastError failed: {}", e.what());
    m_stats.cacheMisses++;
    return "";
  }
}

/**
 * @brief Gets the compression ratio.
 * @return Compression ratio.
 */
float ZlibWrapper::GetCompressionRatio() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_zlibMutex);
  try {
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return m_compressionRatio;
  } catch (const std::exception &e) {
    spdlog::error("GetCompressionRatio failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0.0f;
  }
}

/**
 * @brief Gets the compressed size.
 * @return Compressed size in bytes.
 */
size_t ZlibWrapper::GetCompressedSize() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_zlibMutex);
  try {
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return m_compressedSize;
  } catch (const std::exception &e) {
    spdlog::error("GetCompressedSize failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}

/**
 * @brief Gets the uncompressed size.
 * @return Uncompressed size in bytes.
 */
size_t ZlibWrapper::GetUncompressedSize() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_zlibMutex);
  try {
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return m_uncompressedSize;
  } catch (const std::exception &e) {
    spdlog::error("GetUncompressedSize failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}

/**
 * @brief Retrieves zlib wrapper statistics.
 * @return Current statistics.
 */
ZlibWrapper::Stats ZlibWrapper::GetStats() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_zlibMutex);
  try {
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return m_stats;
  } catch (const std::exception &e) {
    spdlog::error("GetStats failed: {}", e.what());
    m_stats.cacheMisses++;
    return m_stats;
  }
}

/**
 * @brief Sets the last error message.
 * @param error Error message.
 */
void ZlibWrapper::SetError(const std::string &error) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_zlibMutex);
  try {
    m_lastError = error;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  } catch (const std::exception &e) {
    spdlog::error("SetError failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Updates compression statistics.
 * @param inputSize Input size.
 * @param outputSize Output size.
 */
void ZlibWrapper::UpdateCompressionStats(size_t inputSize, size_t outputSize) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_zlibMutex);
  try {
    m_uncompressedSize = inputSize;
    m_compressedSize = outputSize;
    m_compressionRatio =
        (inputSize > 0) ? static_cast<float>(outputSize) / inputSize : 0.0f;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  } catch (const std::exception &e) {
    spdlog::error("UpdateCompressionStats failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Simple decompression when expected size is known.
 * @param input Input compressed data.
 * @param output Output decompressed data.
 * @param expectedSize Expected output size.
 * @return True on success, false otherwise.
 */
bool ZlibWrapper::DecompressSimple(const std::vector<uint8_t> &input,
                                   std::vector<uint8_t> &output,
                                   size_t expectedSize) {
  try {
    z_stream stream = {};
    stream.zalloc = Z_NULL;
    stream.zfree = Z_NULL;
    stream.opaque = Z_NULL;

    if (inflateInit(&stream) != Z_OK) {
      SetError("Failed to initialize zlib decompression");
      spdlog::error("ZlibWrapper::DecompressSimple: inflateInit failed");
      return false;
    }

    stream.next_in = const_cast<Bytef *>(input.data());
    stream.avail_in = input.size();

    output.resize(expectedSize);
    stream.next_out = output.data();
    stream.avail_out = output.size();

    int result = inflate(&stream, Z_FINISH);

    if (result == Z_STREAM_END) {
      output.resize(stream.total_out);
      inflateEnd(&stream);
      spdlog::debug("ZlibWrapper::DecompressSimple: Successfully decompressed "
                    "{} bytes to {} bytes",
                    input.size(), output.size());
      return true;
    } else if (result == Z_BUF_ERROR) {
      // Buffer too small, fall back to chunked decompression
      inflateEnd(&stream);
      spdlog::debug("ZlibWrapper::DecompressSimple: Buffer too small, falling "
                    "back to chunked");
      return false;
    } else {
      SetError("Simple decompression failed: " +
               std::string(stream.msg ? stream.msg : "Unknown error"));
      spdlog::error("ZlibWrapper::DecompressSimple: inflate failed: {}",
                    stream.msg ? stream.msg : "Unknown error");
      inflateEnd(&stream);
      return false;
    }
  } catch (const std::exception &e) {
    spdlog::error("DecompressSimple exception: {}", e.what());
    return false;
  }
}

/**
 * @brief Chunked decompression for unknown output sizes.
 * @param input Input compressed data.
 * @param output Output decompressed data.
 * @return True on success, false otherwise.
 */
bool ZlibWrapper::DecompressChunked(const std::vector<uint8_t> &input,
                                    std::vector<uint8_t> &output) {
  try {
    z_stream stream = {};
    stream.zalloc = Z_NULL;
    stream.zfree = Z_NULL;
    stream.opaque = Z_NULL;

    if (inflateInit(&stream) != Z_OK) {
      SetError("Failed to initialize zlib decompression");
      spdlog::error("ZlibWrapper::DecompressChunked: inflateInit failed");
      return false;
    }

    stream.next_in = const_cast<Bytef *>(input.data());
    stream.avail_in = input.size();

    output.clear();
    const size_t chunkSize = 32768; // 32KB chunks
    std::vector<uint8_t> chunk(chunkSize);

    int result;
    do {
      stream.next_out = chunk.data();
      stream.avail_out = chunkSize;

      result = inflate(&stream, Z_NO_FLUSH);

      if (result == Z_STREAM_ERROR || result == Z_DATA_ERROR ||
          result == Z_MEM_ERROR) {
        SetError("Chunked decompression failed: " +
                 std::string(stream.msg ? stream.msg : "Unknown error"));
        spdlog::error("ZlibWrapper::DecompressChunked: inflate failed: {}",
                      stream.msg ? stream.msg : "Unknown error");
        inflateEnd(&stream);
        return false;
      }

      size_t bytesProduced = chunkSize - stream.avail_out;
      output.insert(output.end(), chunk.begin(), chunk.begin() + bytesProduced);

    } while (result != Z_STREAM_END && stream.avail_out == 0);

    inflateEnd(&stream);

    if (result == Z_STREAM_END) {
      spdlog::debug("ZlibWrapper::DecompressChunked: Successfully decompressed "
                    "{} bytes to {} bytes",
                    input.size(), output.size());
      return true;
    } else {
      SetError("Chunked decompression incomplete");
      spdlog::error(
          "ZlibWrapper::DecompressChunked: Incomplete decompression, result={}",
          result);
      return false;
    }
  } catch (const std::exception &e) {
    spdlog::error("DecompressChunked exception: {}", e.what());
    return false;
  }
}

/**
 * @brief Saves the zlib wrapper state to a stream.
 * @param out Output stream.
 */
void ZlibWrapper::SaveState(std::ostream &out) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_zlibMutex);
  try {
    uint32_t version = 1; // Serialization version
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));

    // Save compression statistics
    out.write(reinterpret_cast<const char *>(&m_compressedSize),
              sizeof(m_compressedSize));
    out.write(reinterpret_cast<const char *>(&m_uncompressedSize),
              sizeof(m_uncompressedSize));
    out.write(reinterpret_cast<const char *>(&m_compressionRatio),
              sizeof(m_compressionRatio));

    // Save last error message
    uint32_t errorLen = static_cast<uint32_t>(m_lastError.size());
    out.write(reinterpret_cast<const char *>(&errorLen), sizeof(errorLen));
    out.write(m_lastError.data(), errorLen);

    // Save statistics
    out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));

    if (!out.good()) {
      throw ZlibException("Failed to write ZlibWrapper state");
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("ZlibWrapper state saved");
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ZlibWrapper SaveState failed: {}", e.what());
    throw ZlibException("SaveState failed: " + std::string(e.what()));
  }
}

/**
 * @brief Loads the zlib wrapper state from a stream.
 * @param in Input stream.
 */
void ZlibWrapper::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_zlibMutex);
  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      spdlog::error("Unsupported ZlibWrapper state version: {}", version);
      throw ZlibException("Invalid ZlibWrapper state version");
    }

    // Load compression statistics
    in.read(reinterpret_cast<char *>(&m_compressedSize),
            sizeof(m_compressedSize));
    in.read(reinterpret_cast<char *>(&m_uncompressedSize),
            sizeof(m_uncompressedSize));
    in.read(reinterpret_cast<char *>(&m_compressionRatio),
            sizeof(m_compressionRatio));

    // Load last error message
    uint32_t errorLen;
    in.read(reinterpret_cast<char *>(&errorLen), sizeof(errorLen));
    m_lastError.resize(errorLen);
    in.read(m_lastError.data(), errorLen);

    // Load statistics
    in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));

    if (!in.good()) {
      throw ZlibException("Failed to read ZlibWrapper state");
    }

    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("ZlibWrapper state loaded");
  } catch (const std::exception &e) {
    spdlog::error("ZlibWrapper LoadState failed: {}", e.what());
    // Reset to safe state on error
    m_compressedSize = 0;
    m_uncompressedSize = 0;
    m_compressionRatio = 0.0f;
    m_lastError.clear();
    m_stats = Stats();
    throw ZlibException("LoadState failed: " + std::string(e.what()));
  }
}

extern "C" {
/**
 * @brief Decompresses data using zlib.
 * @param dst Destination buffer.
 * @param dstSize Destination size.
 * @param src Source buffer.
 * @param srcSize Source size.
 * @return 0 on success, -1 on failure.
 */
int sceZlibDecompress(void *dst, size_t *dstSize, const void *src,
                      size_t srcSize) {
  try {
    if (!dst || !dstSize || !src) {
      spdlog::error("sceZlibDecompress: Invalid parameters");
      return -1;
    }
    std::vector<uint8_t> input(static_cast<const uint8_t *>(src),
                               static_cast<const uint8_t *>(src) + srcSize);
    std::vector<uint8_t> output;
    ZlibWrapper wrapper;
    if (!wrapper.Decompress(input, output, *dstSize)) {
      spdlog::error("sceZlibDecompress: Decompression failed");
      return -1;
    }
    if (output.size() > *dstSize) {
      spdlog::error("sceZlibDecompress: Output buffer too small");
      return -1;
    }
    std::memcpy(dst, output.data(), output.size());
    *dstSize = output.size();
    spdlog::trace("sceZlibDecompress: Decompressed {} bytes to {} bytes",
                  srcSize, *dstSize);
    return 0;
  } catch (const std::exception &e) {
    spdlog::error("sceZlibDecompress: Exception: {}", e.what());
    return -1;
  }
}

/**
 * @brief Compresses data using zlib.
 * @param dst Destination buffer.
 * @param dstSize Destination size.
 * @param src Source buffer.
 * @param srcSize Source size.
 * @param level Compression level.
 * @return 0 on success, -1 on failure.
 */
int sceZlibCompress(void *dst, size_t *dstSize, const void *src, size_t srcSize,
                    int level) {
  try {
    if (!dst || !dstSize || !src) {
      spdlog::error("sceZlibCompress: Invalid parameters");
      return -1;
    }
    std::vector<uint8_t> input(static_cast<const uint8_t *>(src),
                               static_cast<const uint8_t *>(src) + srcSize);
    std::vector<uint8_t> output;
    ZlibWrapper wrapper;
    if (!wrapper.Compress(input, output,
                          static_cast<CompressionLevel>(level))) {
      spdlog::error("sceZlibCompress: Compression failed");
      return -1;
    }
    if (output.size() > *dstSize) {
      spdlog::error("sceZlibCompress: Output buffer too small");
      return -1;
    }
    std::memcpy(dst, output.data(), output.size());
    *dstSize = output.size();
    spdlog::trace("sceZlibCompress: Compressed {} bytes to {} bytes", srcSize,
                  *dstSize);
    return 0;
  } catch (const std::exception &e) {
    spdlog::error("sceZlibCompress: Exception: {}", e.what());
    return -1;
  }
}
}

} // namespace ps4
