// Copyright 2025 <Copyright Owner>

#pragma once

#include "../memory/ps4_mmu.h"
#include "elf.h"
#include <cstdint>
#include <fstream>
#include <string>
#include <unordered_map>
#include <vector>
#include <mutex>

namespace ps4 {
class PS4Emulator; // Forward declaration
}

/**
 * @brief Namespace for PS4 emulator components.
 */
namespace ps4 {

/**
 * @brief Structure for loaded ELF metadata.
 */
struct LoadedElf {
  /**
   * @brief Represents a loaded memory segment.
   */
  struct Segment {
    uint64_t address;    ///< Virtual address of the segment
    uint64_t size;       ///< Size of the segment
    int protection;      ///< Protection flags (read/write/execute)
  };

  std::vector<Segment> loadedSegments;        ///< Loaded memory segments
  uint64_t entryPoint = 0;                    ///< Entry point address
  uint64_t baseLoadAddress = 0;               ///< Base load address for DYN ELF
  uint64_t phdrAddress = 0;                   ///< Program header table address
  uint16_t phdrEntrySize = 0;                 ///< Program header entry size
  uint16_t phdrNum = 0;                       ///< Number of program headers
  uint64_t dynamicAddress = 0;                ///< Dynamic section address
  uint64_t dynamicSize = 0;                   ///< Dynamic section size
  uint64_t dynSymTableAddr = 0;               ///< Dynamic symbol table address
  uint64_t dynStrTableAddr = 0;               ///< Dynamic string table address
  uint64_t dynStrTableSize = 0;               ///< Dynamic string table size
  uint64_t relaDynAddr = 0;                   ///< .rela.dyn relocation address
  uint64_t relaDynSize = 0;                   ///< .rela.dyn relocation size
  uint64_t relaPltAddr = 0;                   ///< .rela.plt relocation address
  uint64_t relaPltSize = 0;                   ///< .rela.plt relocation size
  uint64_t pltGotAddr = 0;                    ///< PLT GOT address
  std::vector<elf::Elf64_Sym> dynSymbols;     ///< Dynamic symbols
  std::vector<char> dynStringTable;           ///< Dynamic string table
  std::unordered_map<std::string, uint64_t> resolvedSymbols; ///< Resolved symbols
  uint64_t metadataAddr = 0;                  ///< PS4 metadata section address
  uint64_t metadataSize = 0;                  ///< PS4 metadata section size

  /**
   * @brief Saves the loaded ELF metadata to a stream.
   * @param out Output stream.
   */
  void Save(std::ostream &out) const;

  /**
   * @brief Loads the loaded ELF metadata from a stream.
   * @param in Input stream.
   */
  void Load(std::istream &in);
};

/**
 * @brief ELF loader for PS4 executables and shared libraries.
 */
class ElfLoader {
public:
  /**
   * @brief Constructs the ELF loader.
   * @param emulator Reference to the PS4Emulator instance.
   */
  explicit ElfLoader(PS4Emulator &emulator);

  /**
   * @brief Destructs the ELF loader, cleaning up resources.
   */
  ~ElfLoader() noexcept;

  /**
   * @brief Loads an ELF file into the emulator.
   * @param filename Path to the ELF file.
   * @param loadedElf Output metadata for the loaded ELF.
   * @param isSharedObject True if the ELF is a shared object.
   * @param processId Process ID for memory allocation.
   * @return True on success, false otherwise.
   */
  bool Load(const std::string &filename, LoadedElf &loadedElf,
            bool isSharedObject = false, uint64_t processId = 1);

  /**
   * @brief Retrieves loader statistics.
   * @return Map of filenames to load counts and other metrics.
   */
  const std::unordered_map<std::string, uint64_t> &GetStats() const {
    std::lock_guard<std::mutex> lock(m_mutex);
    return m_stats;
  }

private:
  /**
   * @brief Loads ELF segments into memory.
   * @param file Input file stream.
   * @param elfHeader ELF header.
   * @param loadedElf Output metadata.
   * @return True on success, false otherwise.
   */
  bool LoadSegments(std::ifstream &file, const elf::Elf64_Ehdr &elfHeader,
                    LoadedElf &loadedElf);

  /**
   * @brief Parses the dynamic section of the ELF.
   * @param loadedElf Output metadata.
   * @return True on success, false otherwise.
   */
  bool ParseDynamicSection(LoadedElf &loadedElf);

  /**
   * @brief Reads dynamic symbol and string tables.
   * @param loadedElf Output metadata.
   * @return True on success, false otherwise.
   */
  bool ReadDynamicTables(LoadedElf &loadedElf);

  /**
   * @brief Processes ELF relocations.
   * @param loadedElf ELF metadata.
   * @param relaAddr Relocation table address.
   * @param relaSize Relocation table size.
   * @param isPlt True if PLT relocations.
   * @return True on success, false otherwise.
   */
  bool ProcessRelocations(LoadedElf &loadedElf, uint64_t relaAddr,
                          uint64_t relaSize, bool isPlt);

  /**
   * @brief Resolves a symbol by name.
   * @param name Symbol name.
   * @param currentElf Current ELF metadata.
   * @return Resolved symbol address, or 0 if unresolved.
   */
  uint64_t ResolveSymbol(const std::string &name, LoadedElf &currentElf);

  PS4Emulator &m_emulator;                              ///< Reference to the emulator
  uint64_t m_currentProcessId;                          ///< Current process ID
  std::unordered_map<std::string, uint64_t> m_stats;    ///< Loader statistics
  mutable std::mutex m_mutex;                           ///< Thread safety mutex
};

} // namespace ps4