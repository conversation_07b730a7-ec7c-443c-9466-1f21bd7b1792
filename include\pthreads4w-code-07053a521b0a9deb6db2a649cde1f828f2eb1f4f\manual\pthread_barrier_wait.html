<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_BARRIER_WAIT(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_barrier_wait - synchronize at a barrier (<B>ADVANCED
REALTIME THREADS</B>) 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt; </B>
</P>
<P><B>int pthread_barrier_wait(pthread_barrier_t *</B><I>barrier</I><B>);
</B>
</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>The <B>pthread_barrier_wait</B> function shall synchronize
participating threads at the barrier referenced by <I>barrier</I>.
The calling thread shall block until the required number of threads
have called <B>pthread_barrier_wait</B> specifying the barrier. 
</P>
<P>When the required number of threads have called
<B>pthread_barrier_wait</B> specifying the barrier, the constant
<B>PTHREAD_BARRIER_SERIAL_THREAD</B> shall be returned to one
unspecified thread and zero shall be returned to each of the
remaining threads. At this point, the barrier shall be reset to the
state it had as a result of the most recent <A HREF="pthread_barrier_init.html"><B>pthread_barrier_init</B>(3)</A>
function that referenced it. 
</P>
<P>The constant <B>PTHREAD_BARRIER_SERIAL_THREAD</B> is defined in
<I>&lt;pthread.h&gt;</I> and its value shall be distinct from any
other value returned by <B>pthread_barrier_wait</B> . 
</P>
<P>The results are undefined if this function is called with an
uninitialized barrier. 
</P>
<P>If a signal is delivered to a thread blocked on a barrier, upon
return from the signal handler the thread shall resume waiting at the
barrier if the barrier wait has not completed (that is, if the
required number of threads have not arrived at the barrier during the
execution of the signal handler); otherwise, the thread shall
continue as normal from the completed barrier wait. Until the thread
in the signal handler returns from it, it is unspecified whether
other threads may proceed past the barrier once they have all reached
it. 
</P>
<P>A thread that has blocked on a barrier shall not prevent any
unblocked thread that is eligible to use the same processing
resources from eventually making forward progress in its execution.
Eligibility for processing resources shall be determined by the
scheduling policy. 
</P>
<H2><A HREF="#toc3" NAME="sect3">Return Value</A></H2>
<P>Upon successful completion, the <B>pthread_barrier_wait</B>
function shall return <B>PTHREAD_BARRIER_SERIAL_THREAD</B> for a
single (arbitrary) thread synchronized at the barrier and zero for
each of the other threads. Otherwise, an error number shall be
returned to indicate the error. 
</P>
<H2><A HREF="#toc4" NAME="sect4">Errors</A></H2>
<P>The <B>pthread_barrier_wait</B> function may fail if: 
</P>
<DL>
	<DT><B>EINVAL</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	The value specified by <I>barrier</I> does not refer to an
	initialized barrier object. 
	</DD></DL>
<P>
This function shall not return an error code of [EINTR]. 
</P>
<P><I>The following sections are informative.</I> 
</P>
<H2><A HREF="#toc5" NAME="sect5">Examples</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc6" NAME="sect6">Application Usage</A></H2>
<P>Applications using this function may be subject to priority
inversion, as discussed in the Base Definitions volume of
IEEE&nbsp;Std&nbsp;1003.1-2001, Section 3.285, Priority Inversion. 
</P>
<P>The <B>pthread_barrier_wait</B> function is part of the Barriers
option and need not be provided on all implementations. 
</P>
<P><B>PThreads4W</B> defines <B>_POSIX_BARRIERS</B> to indicate
that this routine is implemented and may be used.</P>
<H2><A HREF="#toc7" NAME="sect7">Rationale</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc8" NAME="sect8">Future Directions</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc11" NAME="sect11">Known Bugs</A></H2>
<DL>
	None.</DL>
<H2>
<A HREF="#toc9" NAME="sect9">See Also</A></H2>
<P><A HREF="pthread_barrier_init.html"><B>pthread_barrier_destroy</B>(3)</A>,
<A HREF="pthread_barrier_init.html"><B>pthread_barrier_init(3)</B></A>,
the Base Definitions volume of IEEE&nbsp;Std&nbsp;1003.1-2001,
<I>&lt;pthread.h&gt;</I> 
</P>
<H2><A HREF="#toc10" NAME="sect10">Copyright</A></H2>
<P>Portions of this text are reprinted and reproduced in electronic
form from IEEE Std 1003.1, 2003 Edition, Standard for Information
Technology -- Portable Operating System Interface (POSIX), The Open
Group Base Specifications Issue 6, Copyright (C) 2001-2003 by the
Institute of Electrical and Electronics Engineers, Inc and The Open
Group. In the event of any discrepancy between this version and the
original IEEE and The Open Group Standard, the original IEEE and The
Open Group Standard is the referee document. The original Standard
can be obtained online at <A HREF="http://www.opengroup.org/unix/online.html">http://www.opengroup.org/unix/online.html</A>
. 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Examples</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect6" NAME="toc6">Application
	Usage</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect7" NAME="toc7">Rationale</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect8" NAME="toc8">Future
	Directions</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect11" NAME="toc11">Known
	Bugs</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect9" NAME="toc9">See
	Also</A> 
	</P>
	<LI><P><A HREF="#sect10" NAME="toc10">Copyright</A> 
	</P>
</UL>
</BODY>
</HTML>