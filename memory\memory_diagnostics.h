#pragma once
#include "ps4_mmu.h"
#include <chrono>
#include <cstdint>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>


namespace ps4 {
/**
 * @brief Manages memory diagnostics for the PS4 emulator.
 * @details Provides tools for detecting memory leaks, analyzing usage patterns,
 *          and profiling memory performance, integrated with the PS4MMU.
 */
class MemoryDiagnostics {
public:
  /**
   * @brief Structure representing a memory allocation record.
   */
  struct AllocationRecord {
    uint64_t virtAddr;  ///< Virtual address of the allocation
    uint64_t size;      ///< Size of the allocation
    uint64_t processId; ///< ID of the process
    std::chrono::steady_clock::time_point timestamp; ///< Time of allocation
  };

  /**
   * @brief Structure representing memory usage statistics.
   */
  struct UsageStats {
    uint64_t totalAllocated;   ///< Total memory allocated
    uint64_t totalFreed;       ///< Total memory freed
    uint64_t currentAllocated; ///< Currently allocated memory
    uint64_t peakAllocated;    ///< Peak memory allocated
    uint64_t allocationCount;  ///< Number of allocations
    uint64_t freeCount;        ///< Number of frees
  };

  /**
   * @brief Constructs a MemoryDiagnostics instance.
   * @param mmu Reference to the PS4MMU for integration.
   */
  MemoryDiagnostics();
  void ResetMetrics();
  static MemoryDiagnostics &GetInstance();
  void UpdateMetrics();
  const std::unordered_map<std::string, uint64_t> &GetMetrics() const;
  void LogDiagnostics();

  /**
   * @brief Records a memory allocation.
   * @param virtAddr The virtual address of the allocation.
   * @param size The size of the allocation.
   * @param processId The ID of the process.
   */
  void RecordAllocation(uint64_t virtAddr, uint64_t size, uint64_t processId);

  /**
   * @brief Records a memory deallocation.
   * @param virtAddr The virtual address of the deallocation.
   * @param processId The ID of the process.
   */
  void RecordDeallocation(uint64_t virtAddr, uint64_t processId);

  /**
   * @brief Detects potential memory leaks.
   * @return A vector of allocation records that may indicate leaks.
   */
  std::vector<AllocationRecord> DetectLeaks() const;

  /**
   * @brief Gets memory usage statistics.
   * @return The current usage statistics.
   */
  UsageStats GetUsageStats() const;

  /**
   * @brief Exports diagnostics data to a JSON file.
   * @param filePath The path to the output JSON file.
   * @return True if export succeeds, false otherwise.
   */
  bool ExportDiagnostics(const std::string &filePath) const;

private:
  PS4MMU &mmu_;               ///< Reference to the PS4MMU
  mutable std::mutex m_mutex; ///< Mutex for thread safety
  std::unordered_map<uint64_t, std::unordered_map<uint64_t, AllocationRecord>>
      allocations_;                                    ///< Allocation records
  UsageStats stats_;                                   ///< Usage statistics
  std::unordered_map<std::string, uint64_t> m_metrics; ///< Internal metrics map
};

} // namespace ps4