﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{BA46E6C6-487F-3146-AF7E-C990F68BCA07}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <VcpkgEnabled>false</VcpkgEnabled>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>PS4Emulator</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="do_not_import_user.props" Condition="exists('do_not_import_user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\sss\src\build\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PS4Emulator.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">PS4Emulator</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\sss\src\build\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PS4Emulator.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">PS4Emulator</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\sss\src\build\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">PS4Emulator.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">PS4Emulator</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\sss\src\build\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">PS4Emulator.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">PS4Emulator</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\sss\src;D:\sss\src\cache;D:\sss\src\cpu;D:\sss\src\emulator;D:\sss\src\gui\backends;D:\sss\src\gui;D:\sss\src\jit;D:\sss\src\loader;D:\sss\src\memory;D:\sss\src\ps4;D:\sss\src\common;D:\sss\src\syscall;D:\sss\src\video_core;D:\sss\src\build;C:\Program Files (x86)\Intel\oneAPI\mkl\latest\include;C:\Program Files\Microsoft Visual Studio\2022\Community\DIA SDK\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/vcpkg/installed/x64-windows/include" /external:I "D:/vcpkg/installed/x64-windows/include/SDL2" /external:I "D:/VulkanSDK/1.4.309.0/Include" /external:I "D:/vcpkg/installed/x64-windows/include/spirv-reflect" /external:I "D:/vcpkg/installed/x64-windows/include/xbyak" /external:I "D:/vcpkg/installed/x64-windows/include/plutosvg" /external:I "D:/vcpkg/installed/x64-windows/include/plutovg" /Qopenmp /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4251;4275;4201</DisableSpecificWarnings>
      <EnableParallelCodeGeneration>true</EnableParallelCodeGeneration>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <UseStandardPreprocessor>true</UseStandardPreprocessor>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;PS4EMU_DEFAULT_PHYSICAL_MEMORY_SIZE=0x200000000;_M_X64;_AMD64_;WIN64;_WIN64;SPDLOG_NO_COMPILE_TIME_FMT;IMGUI_ENABLE_FREETYPE;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CAPSTONE_SHARED;TBB_USE_DEBUG;TRACY_ENABLE;TRACY_IMPORTS;XXH_EXPORT;ZLIBNG_NATIVE_API;PLUTOSVG_HAS_FREETYPE;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;PS4EMU_DEFAULT_PHYSICAL_MEMORY_SIZE=0x200000000;_M_X64;_AMD64_;WIN64;_WIN64;SPDLOG_NO_COMPILE_TIME_FMT;IMGUI_ENABLE_FREETYPE;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CAPSTONE_SHARED;TBB_USE_DEBUG;TRACY_ENABLE;TRACY_IMPORTS;XXH_EXPORT;ZLIBNG_NATIVE_API;PLUTOSVG_HAS_FREETYPE;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\sss\src;D:\sss\src\cache;D:\sss\src\cpu;D:\sss\src\emulator;D:\sss\src\gui\backends;D:\sss\src\gui;D:\sss\src\jit;D:\sss\src\loader;D:\sss\src\memory;D:\sss\src\ps4;D:\sss\src\common;D:\sss\src\syscall;D:\sss\src\video_core;D:\sss\src\build;C:\Program Files (x86)\Intel\oneAPI\mkl\latest\include;C:\Program Files\Microsoft Visual Studio\2022\Community\DIA SDK\include;D:\vcpkg\installed\x64-windows\include;D:\vcpkg\installed\x64-windows\include\SDL2;D:\VulkanSDK\1.4.309.0\Include;D:\vcpkg\installed\x64-windows\include\spirv-reflect;D:\vcpkg\installed\x64-windows\include\xbyak;D:\vcpkg\installed\x64-windows\include\plutosvg;D:\vcpkg\installed\x64-windows\include\plutovg;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\sss\src;D:\sss\src\cache;D:\sss\src\cpu;D:\sss\src\emulator;D:\sss\src\gui\backends;D:\sss\src\gui;D:\sss\src\jit;D:\sss\src\loader;D:\sss\src\memory;D:\sss\src\ps4;D:\sss\src\common;D:\sss\src\syscall;D:\sss\src\video_core;D:\sss\src\build;C:\Program Files (x86)\Intel\oneAPI\mkl\latest\include;C:\Program Files\Microsoft Visual Studio\2022\Community\DIA SDK\include;D:\vcpkg\installed\x64-windows\include;D:\vcpkg\installed\x64-windows\include\SDL2;D:\VulkanSDK\1.4.309.0\Include;D:\vcpkg\installed\x64-windows\include\spirv-reflect;D:\vcpkg\installed\x64-windows\include\xbyak;D:\vcpkg\installed\x64-windows\include\plutosvg;D:\vcpkg\installed\x64-windows\include\plutovg;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file D:/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary D:/sss/src/build/Debug/PS4Emulator.exe -installedDir D:/vcpkg/installed/x64-windows/debug/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>D:\vcpkg\installed\x64-windows\debug\lib\manual-link\SDL2maind.lib;D:\vcpkg\installed\x64-windows\debug\lib\SDL2d.lib;D:\VulkanSDK\1.4.309.0\Lib\vulkan-1.lib;D:\vcpkg\installed\x64-windows\debug\lib\imguid.lib;D:\vcpkg\installed\x64-windows\debug\lib\spdlogd.lib;D:\vcpkg\installed\x64-windows\debug\lib\portaudio.lib;D:\vcpkg\installed\x64-windows\debug\lib\fmtd.lib;D:\vcpkg\installed\x64-windows\debug\lib\zlibd.lib;D:\vcpkg\installed\x64-windows\debug\lib\spirv-reflect-static.lib;D:\vcpkg\installed\x64-windows\debug\lib\capstone.lib;D:\vcpkg\installed\x64-windows\debug\lib\tbb12_debug.lib;D:\vcpkg\installed\x64-windows\debug\lib\tbbmalloc_proxy_debug.lib;D:\vcpkg\installed\x64-windows\debug\lib\TracyClient.lib;D:\vcpkg\installed\x64-windows\debug\lib\xxhash.lib;D:\vcpkg\installed\x64-windows\debug\lib\zlib-ngd.lib;D:\vcpkg\installed\x64-windows\debug\lib\Zydis.lib;D:\vcpkg\installed\x64-windows\debug\lib\libpng16d.lib;D:\vcpkg\installed\x64-windows\debug\lib\glslangd.lib;D:\vcpkg\installed\x64-windows\debug\lib\glslang-default-resource-limitsd.lib;D:\vcpkg\installed\x64-windows\debug\lib\SPIRVd.lib;D:\vcpkg\installed\x64-windows\debug\lib\SPVRemapperd.lib;D:\vcpkg\installed\x64-windows\debug\lib\plutosvg.lib;diaguids.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib;D:\VulkanSDK\1.4.309.0\Lib\vulkan-1.lib;winmm.lib;dsound.lib;setupapi.lib;D:\vcpkg\installed\x64-windows\debug\lib\freetyped.lib;D:\vcpkg\installed\x64-windows\debug\lib\tbbmalloc_debug.lib;D:\vcpkg\installed\x64-windows\debug\lib\pugixml.lib;D:\vcpkg\installed\x64-windows\debug\lib\Zycore.lib;D:\vcpkg\installed\x64-windows\debug\lib\zlibd.lib;D:\vcpkg\installed\x64-windows\debug\lib\glslangd.lib;D:\vcpkg\installed\x64-windows\debug\lib\plutovg.lib;D:\vcpkg\installed\x64-windows\debug\lib\freetyped.lib;ole32.lib;uuid.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:/Program Files/Microsoft Visual Studio/2022/Community/DIA SDK/lib/amd64;C:/Program Files/Microsoft Visual Studio/2022/Community/DIA SDK/lib/amd64/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/sss/src/build/Debug/PS4Emulator.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/sss/src/build/Debug/PS4Emulator.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\sss\src;D:\sss\src\cache;D:\sss\src\cpu;D:\sss\src\emulator;D:\sss\src\gui\backends;D:\sss\src\gui;D:\sss\src\jit;D:\sss\src\loader;D:\sss\src\memory;D:\sss\src\ps4;D:\sss\src\common;D:\sss\src\syscall;D:\sss\src\video_core;D:\sss\src\build;C:\Program Files (x86)\Intel\oneAPI\mkl\latest\include;C:\Program Files\Microsoft Visual Studio\2022\Community\DIA SDK\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/vcpkg/installed/x64-windows/include" /external:I "D:/vcpkg/installed/x64-windows/include/SDL2" /external:I "D:/VulkanSDK/1.4.309.0/Include" /external:I "D:/vcpkg/installed/x64-windows/include/spirv-reflect" /external:I "D:/vcpkg/installed/x64-windows/include/xbyak" /external:I "D:/vcpkg/installed/x64-windows/include/plutosvg" /external:I "D:/vcpkg/installed/x64-windows/include/plutovg" /Qopenmp /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4251;4275;4201</DisableSpecificWarnings>
      <EnableParallelCodeGeneration>true</EnableParallelCodeGeneration>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <UseStandardPreprocessor>true</UseStandardPreprocessor>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;PS4EMU_DEFAULT_PHYSICAL_MEMORY_SIZE=0x200000000;_M_X64;_AMD64_;WIN64;_WIN64;SPDLOG_NO_COMPILE_TIME_FMT;IMGUI_ENABLE_FREETYPE;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CAPSTONE_SHARED;TRACY_ENABLE;TRACY_IMPORTS;XXH_EXPORT;ZLIBNG_NATIVE_API;PLUTOSVG_HAS_FREETYPE;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;PS4EMU_DEFAULT_PHYSICAL_MEMORY_SIZE=0x200000000;_M_X64;_AMD64_;WIN64;_WIN64;SPDLOG_NO_COMPILE_TIME_FMT;IMGUI_ENABLE_FREETYPE;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CAPSTONE_SHARED;TRACY_ENABLE;TRACY_IMPORTS;XXH_EXPORT;ZLIBNG_NATIVE_API;PLUTOSVG_HAS_FREETYPE;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\sss\src;D:\sss\src\cache;D:\sss\src\cpu;D:\sss\src\emulator;D:\sss\src\gui\backends;D:\sss\src\gui;D:\sss\src\jit;D:\sss\src\loader;D:\sss\src\memory;D:\sss\src\ps4;D:\sss\src\common;D:\sss\src\syscall;D:\sss\src\video_core;D:\sss\src\build;C:\Program Files (x86)\Intel\oneAPI\mkl\latest\include;C:\Program Files\Microsoft Visual Studio\2022\Community\DIA SDK\include;D:\vcpkg\installed\x64-windows\include;D:\vcpkg\installed\x64-windows\include\SDL2;D:\VulkanSDK\1.4.309.0\Include;D:\vcpkg\installed\x64-windows\include\spirv-reflect;D:\vcpkg\installed\x64-windows\include\xbyak;D:\vcpkg\installed\x64-windows\include\plutosvg;D:\vcpkg\installed\x64-windows\include\plutovg;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\sss\src;D:\sss\src\cache;D:\sss\src\cpu;D:\sss\src\emulator;D:\sss\src\gui\backends;D:\sss\src\gui;D:\sss\src\jit;D:\sss\src\loader;D:\sss\src\memory;D:\sss\src\ps4;D:\sss\src\common;D:\sss\src\syscall;D:\sss\src\video_core;D:\sss\src\build;C:\Program Files (x86)\Intel\oneAPI\mkl\latest\include;C:\Program Files\Microsoft Visual Studio\2022\Community\DIA SDK\include;D:\vcpkg\installed\x64-windows\include;D:\vcpkg\installed\x64-windows\include\SDL2;D:\VulkanSDK\1.4.309.0\Include;D:\vcpkg\installed\x64-windows\include\spirv-reflect;D:\vcpkg\installed\x64-windows\include\xbyak;D:\vcpkg\installed\x64-windows\include\plutosvg;D:\vcpkg\installed\x64-windows\include\plutovg;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file D:/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary D:/sss/src/build/Release/PS4Emulator.exe -installedDir D:/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>D:\vcpkg\installed\x64-windows\lib\manual-link\SDL2main.lib;D:\vcpkg\installed\x64-windows\lib\SDL2.lib;D:\VulkanSDK\1.4.309.0\Lib\vulkan-1.lib;D:\vcpkg\installed\x64-windows\lib\imgui.lib;D:\vcpkg\installed\x64-windows\lib\spdlog.lib;D:\vcpkg\installed\x64-windows\lib\portaudio.lib;D:\vcpkg\installed\x64-windows\lib\fmt.lib;D:\vcpkg\installed\x64-windows\lib\zlib.lib;D:\vcpkg\installed\x64-windows\lib\spirv-reflect-static.lib;D:\vcpkg\installed\x64-windows\lib\capstone.lib;D:\vcpkg\installed\x64-windows\lib\tbb12.lib;D:\vcpkg\installed\x64-windows\lib\tbbmalloc_proxy.lib;D:\vcpkg\installed\x64-windows\lib\TracyClient.lib;D:\vcpkg\installed\x64-windows\lib\xxhash.lib;D:\vcpkg\installed\x64-windows\lib\zlib-ng.lib;D:\vcpkg\installed\x64-windows\lib\Zydis.lib;D:\vcpkg\installed\x64-windows\lib\libpng16.lib;D:\vcpkg\installed\x64-windows\lib\glslang.lib;D:\vcpkg\installed\x64-windows\lib\glslang-default-resource-limits.lib;D:\vcpkg\installed\x64-windows\lib\SPIRV.lib;D:\vcpkg\installed\x64-windows\lib\SPVRemapper.lib;D:\vcpkg\installed\x64-windows\lib\plutosvg.lib;diaguids.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib;D:\VulkanSDK\1.4.309.0\Lib\vulkan-1.lib;winmm.lib;dsound.lib;setupapi.lib;D:\vcpkg\installed\x64-windows\lib\freetype.lib;D:\vcpkg\installed\x64-windows\lib\tbbmalloc.lib;D:\vcpkg\installed\x64-windows\lib\pugixml.lib;D:\vcpkg\installed\x64-windows\lib\Zycore.lib;D:\vcpkg\installed\x64-windows\lib\zlib.lib;D:\vcpkg\installed\x64-windows\lib\glslang.lib;D:\vcpkg\installed\x64-windows\lib\plutovg.lib;D:\vcpkg\installed\x64-windows\lib\freetype.lib;ole32.lib;uuid.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:/Program Files/Microsoft Visual Studio/2022/Community/DIA SDK/lib/amd64;C:/Program Files/Microsoft Visual Studio/2022/Community/DIA SDK/lib/amd64/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/sss/src/build/Release/PS4Emulator.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/sss/src/build/Release/PS4Emulator.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\sss\src;D:\sss\src\cache;D:\sss\src\cpu;D:\sss\src\emulator;D:\sss\src\gui\backends;D:\sss\src\gui;D:\sss\src\jit;D:\sss\src\loader;D:\sss\src\memory;D:\sss\src\ps4;D:\sss\src\common;D:\sss\src\syscall;D:\sss\src\video_core;D:\sss\src\build;C:\Program Files (x86)\Intel\oneAPI\mkl\latest\include;C:\Program Files\Microsoft Visual Studio\2022\Community\DIA SDK\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/vcpkg/installed/x64-windows/include" /external:I "D:/vcpkg/installed/x64-windows/include/SDL2" /external:I "D:/VulkanSDK/1.4.309.0/Include" /external:I "D:/vcpkg/installed/x64-windows/include/spirv-reflect" /external:I "D:/vcpkg/installed/x64-windows/include/xbyak" /external:I "D:/vcpkg/installed/x64-windows/include/plutosvg" /external:I "D:/vcpkg/installed/x64-windows/include/plutovg" /Qopenmp /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4251;4275;4201</DisableSpecificWarnings>
      <EnableParallelCodeGeneration>true</EnableParallelCodeGeneration>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <UseStandardPreprocessor>true</UseStandardPreprocessor>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;PS4EMU_DEFAULT_PHYSICAL_MEMORY_SIZE=0x200000000;_M_X64;_AMD64_;WIN64;_WIN64;SPDLOG_NO_COMPILE_TIME_FMT;IMGUI_ENABLE_FREETYPE;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CAPSTONE_SHARED;TRACY_ENABLE;TRACY_IMPORTS;XXH_EXPORT;ZLIBNG_NATIVE_API;PLUTOSVG_HAS_FREETYPE;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;PS4EMU_DEFAULT_PHYSICAL_MEMORY_SIZE=0x200000000;_M_X64;_AMD64_;WIN64;_WIN64;SPDLOG_NO_COMPILE_TIME_FMT;IMGUI_ENABLE_FREETYPE;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CAPSTONE_SHARED;TRACY_ENABLE;TRACY_IMPORTS;XXH_EXPORT;ZLIBNG_NATIVE_API;PLUTOSVG_HAS_FREETYPE;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\sss\src;D:\sss\src\cache;D:\sss\src\cpu;D:\sss\src\emulator;D:\sss\src\gui\backends;D:\sss\src\gui;D:\sss\src\jit;D:\sss\src\loader;D:\sss\src\memory;D:\sss\src\ps4;D:\sss\src\common;D:\sss\src\syscall;D:\sss\src\video_core;D:\sss\src\build;C:\Program Files (x86)\Intel\oneAPI\mkl\latest\include;C:\Program Files\Microsoft Visual Studio\2022\Community\DIA SDK\include;D:\vcpkg\installed\x64-windows\include;D:\vcpkg\installed\x64-windows\include\SDL2;D:\VulkanSDK\1.4.309.0\Include;D:\vcpkg\installed\x64-windows\include\spirv-reflect;D:\vcpkg\installed\x64-windows\include\xbyak;D:\vcpkg\installed\x64-windows\include\plutosvg;D:\vcpkg\installed\x64-windows\include\plutovg;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\sss\src;D:\sss\src\cache;D:\sss\src\cpu;D:\sss\src\emulator;D:\sss\src\gui\backends;D:\sss\src\gui;D:\sss\src\jit;D:\sss\src\loader;D:\sss\src\memory;D:\sss\src\ps4;D:\sss\src\common;D:\sss\src\syscall;D:\sss\src\video_core;D:\sss\src\build;C:\Program Files (x86)\Intel\oneAPI\mkl\latest\include;C:\Program Files\Microsoft Visual Studio\2022\Community\DIA SDK\include;D:\vcpkg\installed\x64-windows\include;D:\vcpkg\installed\x64-windows\include\SDL2;D:\VulkanSDK\1.4.309.0\Include;D:\vcpkg\installed\x64-windows\include\spirv-reflect;D:\vcpkg\installed\x64-windows\include\xbyak;D:\vcpkg\installed\x64-windows\include\plutosvg;D:\vcpkg\installed\x64-windows\include\plutovg;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file D:/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary D:/sss/src/build/MinSizeRel/PS4Emulator.exe -installedDir D:/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>D:\vcpkg\installed\x64-windows\lib\manual-link\SDL2main.lib;D:\vcpkg\installed\x64-windows\lib\SDL2.lib;D:\VulkanSDK\1.4.309.0\Lib\vulkan-1.lib;D:\vcpkg\installed\x64-windows\lib\imgui.lib;D:\vcpkg\installed\x64-windows\lib\spdlog.lib;D:\vcpkg\installed\x64-windows\lib\portaudio.lib;D:\vcpkg\installed\x64-windows\lib\fmt.lib;D:\vcpkg\installed\x64-windows\lib\zlib.lib;D:\vcpkg\installed\x64-windows\lib\spirv-reflect-static.lib;D:\vcpkg\installed\x64-windows\lib\capstone.lib;D:\vcpkg\installed\x64-windows\lib\tbb12.lib;D:\vcpkg\installed\x64-windows\lib\tbbmalloc_proxy.lib;D:\vcpkg\installed\x64-windows\lib\TracyClient.lib;D:\vcpkg\installed\x64-windows\lib\xxhash.lib;D:\vcpkg\installed\x64-windows\lib\zlib-ng.lib;D:\vcpkg\installed\x64-windows\lib\Zydis.lib;D:\vcpkg\installed\x64-windows\lib\libpng16.lib;D:\vcpkg\installed\x64-windows\lib\glslang.lib;D:\vcpkg\installed\x64-windows\lib\glslang-default-resource-limits.lib;D:\vcpkg\installed\x64-windows\lib\SPIRV.lib;D:\vcpkg\installed\x64-windows\lib\SPVRemapper.lib;D:\vcpkg\installed\x64-windows\lib\plutosvg.lib;diaguids.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib;D:\VulkanSDK\1.4.309.0\Lib\vulkan-1.lib;winmm.lib;dsound.lib;setupapi.lib;D:\vcpkg\installed\x64-windows\lib\freetype.lib;D:\vcpkg\installed\x64-windows\lib\tbbmalloc.lib;D:\vcpkg\installed\x64-windows\lib\pugixml.lib;D:\vcpkg\installed\x64-windows\lib\Zycore.lib;D:\vcpkg\installed\x64-windows\lib\zlib.lib;D:\vcpkg\installed\x64-windows\lib\glslang.lib;D:\vcpkg\installed\x64-windows\lib\plutovg.lib;D:\vcpkg\installed\x64-windows\lib\freetype.lib;ole32.lib;uuid.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:/Program Files/Microsoft Visual Studio/2022/Community/DIA SDK/lib/amd64;C:/Program Files/Microsoft Visual Studio/2022/Community/DIA SDK/lib/amd64/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/sss/src/build/MinSizeRel/PS4Emulator.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/sss/src/build/MinSizeRel/PS4Emulator.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\sss\src;D:\sss\src\cache;D:\sss\src\cpu;D:\sss\src\emulator;D:\sss\src\gui\backends;D:\sss\src\gui;D:\sss\src\jit;D:\sss\src\loader;D:\sss\src\memory;D:\sss\src\ps4;D:\sss\src\common;D:\sss\src\syscall;D:\sss\src\video_core;D:\sss\src\build;C:\Program Files (x86)\Intel\oneAPI\mkl\latest\include;C:\Program Files\Microsoft Visual Studio\2022\Community\DIA SDK\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "D:/vcpkg/installed/x64-windows/include" /external:I "D:/vcpkg/installed/x64-windows/include/SDL2" /external:I "D:/VulkanSDK/1.4.309.0/Include" /external:I "D:/vcpkg/installed/x64-windows/include/spirv-reflect" /external:I "D:/vcpkg/installed/x64-windows/include/xbyak" /external:I "D:/vcpkg/installed/x64-windows/include/plutosvg" /external:I "D:/vcpkg/installed/x64-windows/include/plutovg" /Qopenmp /utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4251;4275;4201</DisableSpecificWarnings>
      <EnableParallelCodeGeneration>true</EnableParallelCodeGeneration>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpplatest</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <UseStandardPreprocessor>true</UseStandardPreprocessor>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;PS4EMU_DEFAULT_PHYSICAL_MEMORY_SIZE=0x200000000;_M_X64;_AMD64_;WIN64;_WIN64;SPDLOG_NO_COMPILE_TIME_FMT;IMGUI_ENABLE_FREETYPE;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CAPSTONE_SHARED;TRACY_ENABLE;TRACY_IMPORTS;XXH_EXPORT;ZLIBNG_NATIVE_API;PLUTOSVG_HAS_FREETYPE;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;PS4EMU_DEFAULT_PHYSICAL_MEMORY_SIZE=0x200000000;_M_X64;_AMD64_;WIN64;_WIN64;SPDLOG_NO_COMPILE_TIME_FMT;IMGUI_ENABLE_FREETYPE;SPDLOG_SHARED_LIB;SPDLOG_COMPILED_LIB;SPDLOG_FMT_EXTERNAL;FMT_SHARED;CAPSTONE_SHARED;TRACY_ENABLE;TRACY_IMPORTS;XXH_EXPORT;ZLIBNG_NATIVE_API;PLUTOSVG_HAS_FREETYPE;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\sss\src;D:\sss\src\cache;D:\sss\src\cpu;D:\sss\src\emulator;D:\sss\src\gui\backends;D:\sss\src\gui;D:\sss\src\jit;D:\sss\src\loader;D:\sss\src\memory;D:\sss\src\ps4;D:\sss\src\common;D:\sss\src\syscall;D:\sss\src\video_core;D:\sss\src\build;C:\Program Files (x86)\Intel\oneAPI\mkl\latest\include;C:\Program Files\Microsoft Visual Studio\2022\Community\DIA SDK\include;D:\vcpkg\installed\x64-windows\include;D:\vcpkg\installed\x64-windows\include\SDL2;D:\VulkanSDK\1.4.309.0\Include;D:\vcpkg\installed\x64-windows\include\spirv-reflect;D:\vcpkg\installed\x64-windows\include\xbyak;D:\vcpkg\installed\x64-windows\include\plutosvg;D:\vcpkg\installed\x64-windows\include\plutovg;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\sss\src;D:\sss\src\cache;D:\sss\src\cpu;D:\sss\src\emulator;D:\sss\src\gui\backends;D:\sss\src\gui;D:\sss\src\jit;D:\sss\src\loader;D:\sss\src\memory;D:\sss\src\ps4;D:\sss\src\common;D:\sss\src\syscall;D:\sss\src\video_core;D:\sss\src\build;C:\Program Files (x86)\Intel\oneAPI\mkl\latest\include;C:\Program Files\Microsoft Visual Studio\2022\Community\DIA SDK\include;D:\vcpkg\installed\x64-windows\include;D:\vcpkg\installed\x64-windows\include\SDL2;D:\VulkanSDK\1.4.309.0\Include;D:\vcpkg\installed\x64-windows\include\spirv-reflect;D:\vcpkg\installed\x64-windows\include\xbyak;D:\vcpkg\installed\x64-windows\include\plutosvg;D:\vcpkg\installed\x64-windows\include\plutovg;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file D:/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary D:/sss/src/build/RelWithDebInfo/PS4Emulator.exe -installedDir D:/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>D:\vcpkg\installed\x64-windows\lib\manual-link\SDL2main.lib;D:\vcpkg\installed\x64-windows\lib\SDL2.lib;D:\VulkanSDK\1.4.309.0\Lib\vulkan-1.lib;D:\vcpkg\installed\x64-windows\lib\imgui.lib;D:\vcpkg\installed\x64-windows\lib\spdlog.lib;D:\vcpkg\installed\x64-windows\lib\portaudio.lib;D:\vcpkg\installed\x64-windows\lib\fmt.lib;D:\vcpkg\installed\x64-windows\lib\zlib.lib;D:\vcpkg\installed\x64-windows\lib\spirv-reflect-static.lib;D:\vcpkg\installed\x64-windows\lib\capstone.lib;D:\vcpkg\installed\x64-windows\lib\tbb12.lib;D:\vcpkg\installed\x64-windows\lib\tbbmalloc_proxy.lib;D:\vcpkg\installed\x64-windows\lib\TracyClient.lib;D:\vcpkg\installed\x64-windows\lib\xxhash.lib;D:\vcpkg\installed\x64-windows\lib\zlib-ng.lib;D:\vcpkg\installed\x64-windows\lib\Zydis.lib;D:\vcpkg\installed\x64-windows\lib\libpng16.lib;D:\vcpkg\installed\x64-windows\lib\glslang.lib;D:\vcpkg\installed\x64-windows\lib\glslang-default-resource-limits.lib;D:\vcpkg\installed\x64-windows\lib\SPIRV.lib;D:\vcpkg\installed\x64-windows\lib\SPVRemapper.lib;D:\vcpkg\installed\x64-windows\lib\plutosvg.lib;diaguids.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib;D:\VulkanSDK\1.4.309.0\Lib\vulkan-1.lib;winmm.lib;dsound.lib;setupapi.lib;D:\vcpkg\installed\x64-windows\lib\freetype.lib;D:\vcpkg\installed\x64-windows\lib\tbbmalloc.lib;D:\vcpkg\installed\x64-windows\lib\pugixml.lib;D:\vcpkg\installed\x64-windows\lib\Zycore.lib;D:\vcpkg\installed\x64-windows\lib\zlib.lib;D:\vcpkg\installed\x64-windows\lib\glslang.lib;D:\vcpkg\installed\x64-windows\lib\plutovg.lib;D:\vcpkg\installed\x64-windows\lib\freetype.lib;ole32.lib;uuid.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:/Program Files/Microsoft Visual Studio/2022/Community/DIA SDK/lib/amd64;C:/Program Files/Microsoft Visual Studio/2022/Community/DIA SDK/lib/amd64/$(Configuration);%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/sss/src/build/RelWithDebInfo/PS4Emulator.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/sss/src/build/RelWithDebInfo/PS4Emulator.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\sss\src\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/sss/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/sss/src -BD:/sss/src/build --check-stamp-file D:/sss/src/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCCompilerABI.c;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystem.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FeatureSummary.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPNG.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindThreads.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindVulkan.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;D:\bin\llvm-project-main\llvm\cmake\modules\LLVM-Config.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeCCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeCXXCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeRCCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeSystem.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyConfig.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-config-version.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-config.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-config-version.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-config.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets.cmake;D:\vcpkg\installed\x64-windows\share\freetype\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-config-version.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-config.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfig.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonTargets.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgConfig.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgConfig.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets.cmake;D:\vcpkg\installed\x64-windows\share\png\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioConfig.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-config-version.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-config.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Config.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2ConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-release.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\sdlfind.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBConfig.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11Config.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11ConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11Targets.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapConfig.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapTargets.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config-debug.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config-release.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-config-version.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-config.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-targets.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashConfig.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-config.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-debug.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-release.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng.cmake;D:\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zyan-functions.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-config.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-config.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets.cmake;D:\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\sss\src\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/sss/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/sss/src -BD:/sss/src/build --check-stamp-file D:/sss/src/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCCompilerABI.c;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystem.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FeatureSummary.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPNG.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindThreads.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindVulkan.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;D:\bin\llvm-project-main\llvm\cmake\modules\LLVM-Config.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeCCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeCXXCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeRCCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeSystem.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyConfig.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-config-version.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-config.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-config-version.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-config.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets.cmake;D:\vcpkg\installed\x64-windows\share\freetype\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-config-version.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-config.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfig.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonTargets.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgConfig.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgConfig.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets.cmake;D:\vcpkg\installed\x64-windows\share\png\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioConfig.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-config-version.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-config.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Config.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2ConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-release.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\sdlfind.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBConfig.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11Config.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11ConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11Targets.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapConfig.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapTargets.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config-debug.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config-release.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-config-version.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-config.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-targets.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashConfig.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-config.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-debug.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-release.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng.cmake;D:\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zyan-functions.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-config.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-config.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets.cmake;D:\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\sss\src\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/sss/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/sss/src -BD:/sss/src/build --check-stamp-file D:/sss/src/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCCompilerABI.c;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystem.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FeatureSummary.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPNG.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindThreads.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindVulkan.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;D:\bin\llvm-project-main\llvm\cmake\modules\LLVM-Config.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeCCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeCXXCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeRCCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeSystem.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyConfig.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-config-version.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-config.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-config-version.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-config.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets.cmake;D:\vcpkg\installed\x64-windows\share\freetype\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-config-version.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-config.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfig.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonTargets.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgConfig.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgConfig.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets.cmake;D:\vcpkg\installed\x64-windows\share\png\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioConfig.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-config-version.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-config.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Config.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2ConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-release.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\sdlfind.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBConfig.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11Config.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11ConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11Targets.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapConfig.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapTargets.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config-debug.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config-release.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-config-version.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-config.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-targets.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashConfig.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-config.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-debug.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-release.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng.cmake;D:\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zyan-functions.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-config.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-config.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets.cmake;D:\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\sss\src\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/sss/src/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/sss/src -BD:/sss/src/build --check-stamp-file D:/sss/src/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCCompilerABI.c;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDependentOption.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeDetermineSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeFindBinUtils.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeRCInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystem.cmake.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeTestCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckCSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckIncludeFile.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CheckLibraryExists.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Bruce-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Compaq-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\GNU-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\HP-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IBMCPP-C-DetermineVersionInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IBMClang-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\LCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\SDCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\SunPro-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\TinyCC-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\VisualAge-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\XL-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\XLClang-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\zOS-C-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FeatureSummary.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPNG.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindPackageMessage.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindThreads.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindVulkan.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\FindZLIB.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Internal\FeatureTesting.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\Windows.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.30\Modules\SelectLibraryConfigurations.cmake;D:\bin\llvm-project-main\llvm\cmake\modules\LLVM-Config.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeCCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeCXXCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeRCCompiler.cmake;D:\sss\src\build\CMakeFiles\3.30.5-msvc23\CMakeSystem.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyConfig.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\Tracy\TracyTargets.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-config-version.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-config.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\capstone\capstone-targets.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-config-version.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-config.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\freetype\freetype-targets.cmake;D:\vcpkg\installed\x64-windows\share\freetype\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-config-version.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-config.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\glslang\glslang-targets.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-config.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\imgui\imgui-targets.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfig.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_jsonTargets.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgConfig.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\plutosvg\plutosvgTargets.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgConfig.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\plutovg\plutovgTargets.cmake;D:\vcpkg\installed\x64-windows\share\png\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioConfig.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\portaudio\portaudioTargets.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-config-version.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-config.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\pugixml\pugixml-targets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Config.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2ConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets-release.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2Targets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\SDL2mainTargets.cmake;D:\vcpkg\installed\x64-windows\share\sdl2\sdlfind.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfig.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigTargets.cmake;D:\vcpkg\installed\x64-windows\share\spdlog\spdlogConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBConfig.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\tbb\TBBTargets.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11Config.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11ConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\toml11\toml11Targets.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapConfig.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\tsl-robin-map\tsl-robin-mapTargets.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config-debug.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config-release.cmake;D:\vcpkg\installed\x64-windows\share\unofficial-spirv-reflect\unofficial-spirv-reflect-config.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-config-version.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-config.cmake;D:\vcpkg\installed\x64-windows\share\xbyak\xbyak-targets.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashConfig.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashConfigVersion.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets-debug.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets-release.cmake;D:\vcpkg\installed\x64-windows\share\xxhash\xxHashTargets.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-config.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-debug.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng-release.cmake;D:\vcpkg\installed\x64-windows\share\zlib-ng\zlib-ng.cmake;D:\vcpkg\installed\x64-windows\share\zlib\vcpkg-cmake-wrapper.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zyan-functions.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-config.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\zycore\zycore-targets.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-config-version.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-config.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets-debug.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets-release.cmake;D:\vcpkg\installed\x64-windows\share\zydis\zydis-targets.cmake;D:\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\sss\src\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\sss\src\cache\cache.cpp" />
    <ClCompile Include="D:\sss\src\common\lock_ordering.cpp" />
    <ClCompile Include="D:\sss\src\cpu\cpu_diagnostics.cpp" />
    <ClCompile Include="D:\sss\src\cpu\instruction_decoder.cpp" />
    <ClCompile Include="D:\sss\src\cpu\thunk_manager.cpp" />
    <ClCompile Include="D:\sss\src\cpu\x86_64_cpu.cpp" />
    <ClCompile Include="D:\sss\src\cpu\x86_64_pipeline.cpp" />
    <ClCompile Include="D:\sss\src\emulator\adaptive_emulation_orchestrator.cpp" />
    <ClCompile Include="D:\sss\src\emulator\apic.cpp" />
    <ClCompile Include="D:\sss\src\emulator\interrupt_handler.cpp" />
    <ClCompile Include="D:\sss\src\emulator\io_manager.cpp" />
    <ClCompile Include="D:\sss\src\gui\backends\imgui_impl_sdl2.cpp" />
    <ClCompile Include="D:\sss\src\gui\backends\imgui_impl_vulkan.cpp" />
    <ClCompile Include="D:\sss\src\gui\imgui.cpp" />
    <ClCompile Include="D:\sss\src\gui\imgui_demo.cpp" />
    <ClCompile Include="D:\sss\src\gui\imgui_draw.cpp" />
    <ClCompile Include="D:\sss\src\gui\imgui_freetype.cpp" />
    <ClCompile Include="D:\sss\src\gui\imgui_stdlib.cpp" />
    <ClCompile Include="D:\sss\src\gui\imgui_tables.cpp" />
    <ClCompile Include="D:\sss\src\gui\imgui_widgets.cpp" />
    <ClCompile Include="D:\sss\src\gui\input_manager.cpp" />
    <ClCompile Include="D:\sss\src\jit\jit_diagnostics.cpp" />
    <ClCompile Include="D:\sss\src\jit\x86_64_jit_compiler.cpp" />
    <ClCompile Include="D:\sss\src\jit\x86_64_jit_helpers.cpp" />
    <ClCompile Include="D:\sss\src\loader\elf_loader.cpp" />
    <ClCompile Include="D:\sss\src\loader\pkg_installer.cpp" />
    <ClCompile Include="D:\sss\src\main.cpp" />
    <ClCompile Include="D:\sss\src\memory\memory_compressor.cpp" />
    <ClCompile Include="D:\sss\src\memory\memory_diagnostics.cpp" />
    <ClCompile Include="D:\sss\src\memory\memory_prefetcher.cpp" />
    <ClCompile Include="D:\sss\src\memory\physical_memory_allocator.cpp" />
    <ClCompile Include="D:\sss\src\memory\ps4_mmu.cpp" />
    <ClCompile Include="D:\sss\src\memory\swap_manager.cpp" />
    <ClCompile Include="D:\sss\src\memory\tlb.cpp" />
    <ClCompile Include="D:\sss\src\ps4\cache_clear.cpp" />
    <ClCompile Include="D:\sss\src\ps4\fiber_manager.cpp" />
    <ClCompile Include="D:\sss\src\ps4\orbis_os.cpp" />
    <ClCompile Include="D:\sss\src\ps4\ps4_audio.cpp" />
    <ClCompile Include="D:\sss\src\ps4\ps4_controllers.cpp" />
    <ClCompile Include="D:\sss\src\ps4\ps4_emulator.cpp" />
    <ClCompile Include="D:\sss\src\ps4\ps4_filesystem.cpp" />
    <ClCompile Include="D:\sss\src\ps4\ps4_gpu.cpp" />
    <ClCompile Include="D:\sss\src\ps4\ps4_tsc.cpp" />
    <ClCompile Include="D:\sss\src\ps4\trophy_manager.cpp" />
    <ClCompile Include="D:\sss\src\ps4\zlib_wrapper.cpp" />
    <ClCompile Include="D:\sss\src\syscall\syscall_handler.cpp" />
    <ClCompile Include="D:\sss\src\video_core\command_processor.cpp" />
    <ClCompile Include="D:\sss\src\video_core\gnm_shader_translator.cpp" />
    <ClCompile Include="D:\sss\src\video_core\gnm_state.cpp" />
    <ClCompile Include="D:\sss\src\video_core\shader_emulator.cpp" />
    <ClCompile Include="D:\sss\src\video_core\tile_manager.cpp" />
    <Natvis Include="D:\vcpkg\installed\x64-windows\share\nlohmann_json\nlohmann_json.natvis">
    </Natvis>
    <Natvis Include="D:\vcpkg\installed\x64-windows\share\tsl-robin-map.natvis">
    </Natvis>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\sss\src\build\ZERO_CHECK.vcxproj">
      <Project>{48EB4516-62B6-3237-9D7B-260E05A6716B}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>