#include <algorithm>
#include <array>
#include <cstring>
#include <stdexcept>
#include <string>
#include <unordered_map>

#include "cpu/instruction_decoder.h"
#include <spdlog/spdlog.h>

namespace x86_64 {
struct DecodeException : std::runtime_error {
  explicit DecodeException(const std::string &msg) : std::runtime_error(msg) {}
};

InstructionDecoder::InstructionDecoder() {
  spdlog::info("Constructing InstructionDecoder...");

  // Initialize all x86-64 single-byte opcodes
  InitializeSingleByteOpcodes();

  // Initialize all x86-64 two-byte opcodes (0x0F prefix)
  InitializeTwoByteOpcodes();

  // Initialize three-byte opcodes (0x0F 0x38 and 0x0F 0x3A)
  InitializeThreeByteOpcodes();

  // Initialize VEX opcodes
  InitializeVEXOpcodes();

  // Initialize EVEX opcodes (AVX-512)
  InitializeEVEXOpcodes();

  // CRITICAL FIX: Initialize FPU opcodes (0xD8-0xDF prefix)
  InitializeFPUOpcodes();
  // CRITICAL FIX: Initialize Group opcodes (ModR/M extension opcodes)
  InitializeGroupOpcodes();

  spdlog::info(
      "InstructionDecoder initialized with {} single-byte, {} "
      "two-byte, {} three-byte, {} VEX, {} EVEX, {} FPU, and {} Group opcodes",
      static_cast<size_t>(singleByteOpcodes.size()),
      static_cast<size_t>(twoByteOpcodes.size()),
      static_cast<size_t>(threeByteOpcodes.size()),
      static_cast<size_t>(vexOpcodes.size()),
      static_cast<size_t>(evexOpcodes.size()),
      static_cast<size_t>(fpuOpcodes.size()),
      static_cast<size_t>(groupOpcodes.size()));
}

void InstructionDecoder::InitializeSingleByteOpcodes() {
  // 0x00-0x0F: Basic arithmetic and data movement
  AddOpcode(0x00, InstructionType::Add, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x01, InstructionType::Add, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x02, InstructionType::Add, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x03, InstructionType::Add, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x04, InstructionType::Add, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x05, InstructionType::Add, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x06, InstructionType::Push, 1,
            {DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x07, InstructionType::Pop, 1,
            {DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x08, InstructionType::Or, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x09, InstructionType::Or, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x0A, InstructionType::Or, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x0B, InstructionType::Or, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x0C, InstructionType::Or, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x0D, InstructionType::Or, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x0E, InstructionType::Push, 1,
            {DecodedInstruction::Operand::Type::REGISTER});
  // 0x0F is two-byte escape

  // 0x10-0x1F: ADC and SBB
  AddOpcode(0x10, InstructionType::Adc, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x11, InstructionType::Adc, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x12, InstructionType::Adc, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x13, InstructionType::Adc, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x14, InstructionType::Adc, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x15, InstructionType::Adc, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x16, InstructionType::Push, 1,
            {DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x17, InstructionType::Pop, 1,
            {DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x18, InstructionType::Sbb, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x19, InstructionType::Sbb, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x1A, InstructionType::Sbb, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x1B, InstructionType::Sbb, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x1C, InstructionType::Sbb, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x1D, InstructionType::Sbb, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x1E, InstructionType::Push, 1,
            {DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x1F, InstructionType::Pop, 1,
            {DecodedInstruction::Operand::Type::REGISTER});

  // 0x20-0x2F: AND and SUB
  AddOpcode(0x20, InstructionType::And, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x21, InstructionType::And, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x22, InstructionType::And, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x23, InstructionType::And, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x24, InstructionType::And, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x25, InstructionType::And, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  // 0x26: ES segment override prefix
  AddOpcode(0x27, InstructionType::Unknown, 0,
            {}); // DAA (not valid in 64-bit mode)
  AddOpcode(0x28, InstructionType::Sub, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x29, InstructionType::Sub, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x2A, InstructionType::Sub, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x2B, InstructionType::Sub, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x2C, InstructionType::Sub, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x2D, InstructionType::Sub, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  // 0x2E: CS segment override prefix
  AddOpcode(0x2F, InstructionType::Unknown, 0,
            {}); // DAS (not valid in 64-bit mode)

  // 0x30-0x3F: XOR and CMP
  AddOpcode(0x30, InstructionType::Xor, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x31, InstructionType::Xor, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x32, InstructionType::Xor, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x33, InstructionType::Xor, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x34, InstructionType::Xor, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x35, InstructionType::Xor, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  // 0x36: SS segment override prefix
  AddOpcode(0x37, InstructionType::Unknown, 0,
            {}); // AAA (not valid in 64-bit mode)
  AddOpcode(0x38, InstructionType::Cmp, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x39, InstructionType::Cmp, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x3A, InstructionType::Cmp, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x3B, InstructionType::Cmp, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x3C, InstructionType::Cmp, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x3D, InstructionType::Cmp, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  // 0x3E: DS segment override prefix
  AddOpcode(0x3F, InstructionType::Unknown, 0,
            {}); // AAS (not valid in 64-bit mode)

  // 0x40-0x4F: REX prefixes in 64-bit mode, INC/DEC in 32-bit mode
  for (uint8_t i = 0x40; i <= 0x4F; ++i) {
    // In 64-bit mode, these are REX prefixes, handled in prefix parsing
    // In 32-bit mode, these would be INC/DEC, but we're focusing on 64-bit
    AddOpcode(i, InstructionType::Unknown, 0, {});
  }

  // 0x50-0x5F: PUSH/POP registers
  for (uint8_t i = 0x50; i <= 0x57; ++i) {
    AddOpcode(i, InstructionType::Push, 1,
              {DecodedInstruction::Operand::Type::REGISTER});
  }
  for (uint8_t i = 0x58; i <= 0x5F; ++i) {
    AddOpcode(i, InstructionType::Pop, 1,
              {DecodedInstruction::Operand::Type::REGISTER});
  }

  // 0x60-0x6F: Various instructions
  AddOpcode(0x60, InstructionType::Pusha, 0, {}); // Invalid in 64-bit mode
  AddOpcode(0x61, InstructionType::Popa, 0, {});  // Invalid in 64-bit mode
  AddOpcode(
      0x62, InstructionType::Bound, 2,
      {DecodedInstruction::Operand::Type::REGISTER,
       DecodedInstruction::Operand::Type::MEMORY}); // Invalid in 64-bit mode
  AddOpcode(0x63, InstructionType::Movsxd, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  // 0x64: FS segment override prefix
  // 0x65: GS segment override prefix
  // 0x66: Operand size override prefix
  // 0x67: Address size override prefix
  AddOpcode(0x68, InstructionType::Push, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x69, InstructionType::Imul, 3,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x6A, InstructionType::Push, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x6B, InstructionType::Imul, 3,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0x6C, InstructionType::Ins, 0, {});
  AddOpcode(0x6D, InstructionType::Ins, 0, {});
  AddOpcode(0x6E, InstructionType::Outs, 0, {});
  AddOpcode(0x6F, InstructionType::Outs, 0, {});

  // 0x70-0x7F: Short conditional jumps
  for (uint8_t i = 0x70; i <= 0x7F; ++i) {
    AddOpcode(i, InstructionType::Jcc, 1,
              {DecodedInstruction::Operand::Type::IMMEDIATE});
  }

  // 0x80-0x8F: Immediate arithmetic and MOV
  AddOpcode(0x80, InstructionType::Unknown, 0,
            {}); // Group 1 - handled specially
  AddOpcode(0x81, InstructionType::Unknown, 0,
            {}); // Group 1 - handled specially
  AddOpcode(0x82, InstructionType::Unknown, 0,
            {}); // Group 1 - invalid in 64-bit mode
  AddOpcode(0x83, InstructionType::Unknown, 0,
            {}); // Group 1 - handled specially
  AddOpcode(0x84, InstructionType::Test, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x85, InstructionType::Test, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x86, InstructionType::Xchg, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x87, InstructionType::Xchg, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x88, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x89, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0x8A, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x8B, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x8C, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER}); // Segment register
  AddOpcode(0x8D, InstructionType::Lea, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0x8E, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY}); // Segment register
  AddOpcode(0x8F, InstructionType::Pop, 1,
            {DecodedInstruction::Operand::Type::MEMORY}); // Group 1A

  // 0x90-0x9F: NOP, XCHG, and string operations
  AddOpcode(0x90, InstructionType::Nop, 0, {});
  for (uint8_t i = 0x91; i <= 0x97; ++i) {
    AddOpcode(i, InstructionType::Xchg, 2,
              {DecodedInstruction::Operand::Type::REGISTER,
               DecodedInstruction::Operand::Type::REGISTER});
  }
  AddOpcode(0x98, InstructionType::Movsx, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::REGISTER}); // CWDE/CDQE
  AddOpcode(0x99, InstructionType::Unknown, 0, {});         // CDQ/CQO
  AddOpcode(
      0x9A, InstructionType::Call_far, 1,
      {DecodedInstruction::Operand::Type::IMMEDIATE}); // Invalid in 64-bit mode
  AddOpcode(0x9B, InstructionType::Fwait, 0, {});
  AddOpcode(0x9C, InstructionType::Pushf, 0, {});
  AddOpcode(0x9D, InstructionType::Popf, 0, {});
  AddOpcode(0x9E, InstructionType::Sahf, 0, {});
  AddOpcode(0x9F, InstructionType::Lahf, 0, {});

  // 0xA0-0xAF: String operations and TEST
  AddOpcode(0xA0, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0xA1, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::MEMORY});
  AddOpcode(0xA2, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0xA3, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0xA4, InstructionType::Movsb, 0, {});
  AddOpcode(0xA5, InstructionType::Movsd, 0, {});
  AddOpcode(0xA6, InstructionType::Cmpsb, 0, {});
  AddOpcode(0xA7, InstructionType::Cmpsd, 0, {});
  AddOpcode(0xA8, InstructionType::Test, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xA9, InstructionType::Test, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xAA, InstructionType::Stosb, 0, {});
  AddOpcode(0xAB, InstructionType::Stosd, 0, {});
  AddOpcode(0xAC, InstructionType::Lodsb, 0, {});
  AddOpcode(0xAD, InstructionType::Lodsd, 0, {});
  AddOpcode(0xAE, InstructionType::Scasb, 0, {});
  AddOpcode(0xAF, InstructionType::Scasd, 0, {});

  // 0xB0-0xBF: MOV immediate to register
  for (uint8_t i = 0xB0; i <= 0xB7; ++i) {
    AddOpcode(i, InstructionType::Mov, 2,
              {DecodedInstruction::Operand::Type::REGISTER,
               DecodedInstruction::Operand::Type::IMMEDIATE});
  }
  for (uint8_t i = 0xB8; i <= 0xBF; ++i) {
    AddOpcode(i, InstructionType::Mov, 2,
              {DecodedInstruction::Operand::Type::REGISTER,
               DecodedInstruction::Operand::Type::IMMEDIATE});
  }

  // 0xC0-0xCF: Shift/rotate and control flow
  AddOpcode(0xC0, InstructionType::Unknown, 0,
            {}); // Group 2 - handled specially
  AddOpcode(0xC1, InstructionType::Unknown, 0,
            {}); // Group 2 - handled specially
  AddOpcode(0xC2, InstructionType::Ret, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xC3, InstructionType::Ret, 0, {});
  AddOpcode(0xC4, InstructionType::Unknown, 0,
            {}); // LES - invalid in 64-bit mode / VEX prefix
  AddOpcode(0xC5, InstructionType::Unknown, 0,
            {}); // LDS - invalid in 64-bit mode / VEX prefix
  AddOpcode(0xC6, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::IMMEDIATE}); // Group 11
  AddOpcode(0xC7, InstructionType::Mov, 2,
            {DecodedInstruction::Operand::Type::MEMORY,
             DecodedInstruction::Operand::Type::IMMEDIATE}); // Group 11
  AddOpcode(0xC8, InstructionType::Enter, 2,
            {DecodedInstruction::Operand::Type::IMMEDIATE,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xC9, InstructionType::Leave, 0, {});
  AddOpcode(0xCA, InstructionType::Ret_far, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xCB, InstructionType::Ret_far, 0, {});
  AddOpcode(0xCC, InstructionType::Int3, 0, {});
  AddOpcode(0xCD, InstructionType::Int, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xCE, InstructionType::Into, 0, {}); // Invalid in 64-bit mode
  AddOpcode(0xCF, InstructionType::Iret, 0, {});

  // 0xD0-0xDF: Shift/rotate and FPU escape
  AddOpcode(0xD0, InstructionType::Unknown, 0,
            {}); // Group 2 - handled specially
  AddOpcode(0xD1, InstructionType::Unknown, 0,
            {}); // Group 2 - handled specially
  AddOpcode(0xD2, InstructionType::Unknown, 0,
            {}); // Group 2 - handled specially
  AddOpcode(0xD3, InstructionType::Unknown, 0,
            {}); // Group 2 - handled specially
  AddOpcode(0xD4, InstructionType::Unknown, 0,
            {}); // AAM - invalid in 64-bit mode
  AddOpcode(0xD5, InstructionType::Unknown, 0,
            {}); // AAD - invalid in 64-bit mode
  AddOpcode(0xD6, InstructionType::Unknown, 0, {}); // SALC - undocumented
  AddOpcode(0xD7, InstructionType::Unknown, 0, {}); // XLAT
  // 0xD8-0xDF: FPU instructions - handled specially
  for (uint8_t i = 0xD8; i <= 0xDF; ++i) {
    AddOpcode(i, InstructionType::Unknown, 0,
              {}); // FPU escape - handled specially
  }

  // 0xE0-0xEF: Loop and I/O instructions
  AddOpcode(0xE0, InstructionType::Loopne, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xE1, InstructionType::Loope, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xE2, InstructionType::Loop, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xE3, InstructionType::Jrcxz, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xE4, InstructionType::In, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xE5, InstructionType::In, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xE6, InstructionType::Out, 2,
            {DecodedInstruction::Operand::Type::IMMEDIATE,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0xE7, InstructionType::Out, 2,
            {DecodedInstruction::Operand::Type::IMMEDIATE,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0xE8, InstructionType::Call, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xE9, InstructionType::Jump, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(
      0xEA, InstructionType::Jmp_far, 1,
      {DecodedInstruction::Operand::Type::IMMEDIATE}); // Invalid in 64-bit mode
  AddOpcode(0xEB, InstructionType::Jump, 1,
            {DecodedInstruction::Operand::Type::IMMEDIATE});
  AddOpcode(0xEC, InstructionType::In, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0xED, InstructionType::In, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0xEE, InstructionType::Out, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::REGISTER});
  AddOpcode(0xEF, InstructionType::Out, 2,
            {DecodedInstruction::Operand::Type::REGISTER,
             DecodedInstruction::Operand::Type::REGISTER});

  // 0xF0-0xFF: Prefixes and miscellaneous
  // 0xF0: LOCK prefix
  AddOpcode(0xF1, InstructionType::Int, 0, {}); // INT1/ICEBP
  // 0xF2: REPNE prefix
  // 0xF3: REP/REPE prefix
  AddOpcode(0xF4, InstructionType::Hlt, 0, {});
  AddOpcode(0xF5, InstructionType::Cmc, 0, {});
  AddOpcode(0xF6, InstructionType::Unknown, 0,
            {}); // Group 3 - handled specially
  AddOpcode(0xF7, InstructionType::Unknown, 0,
            {}); // Group 3 - handled specially
  AddOpcode(0xF8, InstructionType::Clc, 0, {});
  AddOpcode(0xF9, InstructionType::Stc, 0, {});
  AddOpcode(0xFA, InstructionType::Cli, 0, {});
  AddOpcode(0xFB, InstructionType::Sti, 0, {});
  AddOpcode(0xFC, InstructionType::Cld, 0, {});
  AddOpcode(0xFD, InstructionType::Std, 0, {});
  AddOpcode(0xFE, InstructionType::Unknown, 0,
            {}); // Group 4 - handled specially
  AddOpcode(0xFF, InstructionType::Unknown, 0,
            {}); // Group 5 - handled specially
}

void InstructionDecoder::InitializeTwoByteOpcodes() {
  // 0x0F 0x00-0x0F: System instructions
  AddTwoByteOpcode(0x00, InstructionType::Unknown, 0,
                   {}); // Group 6 - SLDT, STR, LLDT, LTR, VERR, VERW
  AddTwoByteOpcode(
      0x01, InstructionType::Unknown, 0,
      {}); // Group 7 - SGDT, SIDT, LGDT, LIDT, SMSW, LMSW, INVLPG, SWAPGS
  AddTwoByteOpcode(0x02, InstructionType::Lar, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x03, InstructionType::Lsl, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x05, InstructionType::Syscall, 0, {});
  AddTwoByteOpcode(0x06, InstructionType::Unknown, 0, {}); // CLTS
  AddTwoByteOpcode(0x07, InstructionType::Sysret, 0, {});
  AddTwoByteOpcode(0x08, InstructionType::Invd, 0, {});
  AddTwoByteOpcode(0x09, InstructionType::Wbinvd, 0, {});
  AddTwoByteOpcode(0x0B, InstructionType::Ud2, 0, {});
  AddTwoByteOpcode(
      0x0D, InstructionType::Prefetch, 1,
      {DecodedInstruction::Operand::Type::MEMORY}); // Group P - NOP/PREFETCH
  AddTwoByteOpcode(0x0E, InstructionType::Unknown, 0, {}); // FEMMS
  AddTwoByteOpcode(0x0F, InstructionType::Unknown, 0, {}); // 3DNow! prefix

  // 0x0F 0x10-0x1F: SSE move instructions
  AddTwoByteOpcode(0x10, InstructionType::Movups, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x11, InstructionType::Movups, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::XMM});
  AddTwoByteOpcode(0x12, InstructionType::Movlps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x13, InstructionType::Movlps, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::XMM});
  AddTwoByteOpcode(0x14, InstructionType::Unpcklps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x15, InstructionType::Unpckhps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x16, InstructionType::Movhps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x17, InstructionType::Movhps, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::XMM});
  AddTwoByteOpcode(
      0x18, InstructionType::Prefetcht0, 1,
      {DecodedInstruction::Operand::Type::MEMORY}); // Group 16 - PREFETCH
  AddTwoByteOpcode(
      0x19, InstructionType::Nop2, 1,
      {DecodedInstruction::Operand::Type::MEMORY}); // Multi-byte NOP
  AddTwoByteOpcode(
      0x1A, InstructionType::Nop2, 1,
      {DecodedInstruction::Operand::Type::MEMORY}); // Multi-byte NOP
  AddTwoByteOpcode(
      0x1B, InstructionType::Nop2, 1,
      {DecodedInstruction::Operand::Type::MEMORY}); // Multi-byte NOP
  AddTwoByteOpcode(
      0x1C, InstructionType::Nop2, 1,
      {DecodedInstruction::Operand::Type::MEMORY}); // Multi-byte NOP
  AddTwoByteOpcode(
      0x1D, InstructionType::Nop2, 1,
      {DecodedInstruction::Operand::Type::MEMORY}); // Multi-byte NOP
  AddTwoByteOpcode(
      0x1E, InstructionType::Nop2, 1,
      {DecodedInstruction::Operand::Type::MEMORY}); // Multi-byte NOP
  AddTwoByteOpcode(
      0x1F, InstructionType::Nop2, 1,
      {DecodedInstruction::Operand::Type::MEMORY}); // Multi-byte NOP

  // 0x0F 0x20-0x2F: Control register and debug register moves
  AddTwoByteOpcode(
      0x20, InstructionType::Mov, 2,
      {DecodedInstruction::Operand::Type::REGISTER,
       DecodedInstruction::Operand::Type::REGISTER}); // MOV r32, CRn
  AddTwoByteOpcode(
      0x21, InstructionType::Mov, 2,
      {DecodedInstruction::Operand::Type::REGISTER,
       DecodedInstruction::Operand::Type::REGISTER}); // MOV r32, DRn
  AddTwoByteOpcode(
      0x22, InstructionType::Mov, 2,
      {DecodedInstruction::Operand::Type::REGISTER,
       DecodedInstruction::Operand::Type::REGISTER}); // MOV CRn, r32
  AddTwoByteOpcode(
      0x23, InstructionType::Mov, 2,
      {DecodedInstruction::Operand::Type::REGISTER,
       DecodedInstruction::Operand::Type::REGISTER}); // MOV DRn, r32
  AddTwoByteOpcode(0x28, InstructionType::Movaps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x29, InstructionType::Movaps, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::XMM});
  AddTwoByteOpcode(0x2A, InstructionType::Cvtpi2ps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x2B, InstructionType::Movntps, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::XMM});
  AddTwoByteOpcode(0x2C, InstructionType::Cvttps2pi, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::XMM});
  AddTwoByteOpcode(0x2D, InstructionType::Cvtps2pi, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::XMM});
  AddTwoByteOpcode(0x2E, InstructionType::Ucomiss, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x2F, InstructionType::Comiss, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});

  // 0x0F 0x30-0x3F: System instructions and WRMSR/RDMSR
  AddTwoByteOpcode(0x30, InstructionType::Wrmsr, 0, {});
  AddTwoByteOpcode(0x31, InstructionType::Rdtsc, 0, {});
  AddTwoByteOpcode(0x32, InstructionType::Rdmsr, 0, {});
  AddTwoByteOpcode(0x33, InstructionType::Rdpmc, 0, {});
  AddTwoByteOpcode(0x34, InstructionType::Sysenter, 0, {});
  AddTwoByteOpcode(0x35, InstructionType::Sysexit, 0, {});
  AddTwoByteOpcode(0x37, InstructionType::Getsec, 0, {});
  // 0x38: Three-byte escape
  // 0x3A: Three-byte escape

  // 0x0F 0x40-0x4F: Conditional moves
  for (uint8_t i = 0x40; i <= 0x4F; ++i) {
    AddTwoByteOpcode(i, InstructionType::Cmovcc, 2,
                     {DecodedInstruction::Operand::Type::REGISTER,
                      DecodedInstruction::Operand::Type::MEMORY});
  }

  // 0x0F 0x50-0x5F: SSE arithmetic and logical
  AddTwoByteOpcode(0x50, InstructionType::Movmskps, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::XMM});
  AddTwoByteOpcode(0x51, InstructionType::Sqrtps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x52, InstructionType::Rsqrtps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x53, InstructionType::Rcpps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x54, InstructionType::Andps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x55, InstructionType::Andnps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x56, InstructionType::Orps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x57, InstructionType::Xorps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x58, InstructionType::Addps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x59, InstructionType::Mulps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x5A, InstructionType::Cvtps2pd, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x5B, InstructionType::Cvtdq2ps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x5C, InstructionType::Subps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x5D, InstructionType::Minps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x5E, InstructionType::Divps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x5F, InstructionType::Maxps, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});

  // 0x0F 0x60-0x6F: MMX/SSE2 packed integer operations
  AddTwoByteOpcode(0x60, InstructionType::Punpcklbw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x61, InstructionType::Punpcklwd, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x62, InstructionType::Punpckldq, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x63, InstructionType::Packsswb, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x64, InstructionType::Pcmpgtb, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x65, InstructionType::Pcmpgtw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x66, InstructionType::Pcmpgtd, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x67, InstructionType::Packuswb, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x68, InstructionType::Punpckhbw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x69, InstructionType::Punpckhwd, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x6A, InstructionType::Punpckhdq, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x6B, InstructionType::Packssdw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x6C, InstructionType::Punpcklqdq, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x6D, InstructionType::Punpckhqdq, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x6E, InstructionType::Movd, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0x6F, InstructionType::Movq, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});

  // 0x0F 0x70-0x7F: MMX/SSE2 shift and shuffle operations
  AddTwoByteOpcode(0x70, InstructionType::Pshufw, 3,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::IMMEDIATE});
  AddTwoByteOpcode(0x71, InstructionType::Unknown, 0,
                   {}); // Group 12 - PSRLW/PSRAW/PSLLW
  AddTwoByteOpcode(0x72, InstructionType::Unknown, 0,
                   {}); // Group 13 - PSRLD/PSRAD/PSLLD
  AddTwoByteOpcode(0x73, InstructionType::Unknown, 0,
                   {}); // Group 14 - PSRLQ/PSLLQ
  AddTwoByteOpcode(0x74, InstructionType::Pcmpeqb, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x75, InstructionType::Pcmpeqw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x76, InstructionType::Pcmpeqd, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x77, InstructionType::Unknown, 0, {}); // EMMS
  AddTwoByteOpcode(0x78, InstructionType::Vmread, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0x79, InstructionType::Vmwrite, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x7C, InstructionType::Haddpd, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x7D, InstructionType::Hsubpd, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x7E, InstructionType::Movd, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0x7F, InstructionType::Movq, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});

  // 0x0F 0x80-0x8F: Long conditional jumps
  for (uint8_t i = 0x80; i <= 0x8F; ++i) {
    AddTwoByteOpcode(i, InstructionType::Jcc, 1,
                     {DecodedInstruction::Operand::Type::IMMEDIATE});
  }

  // 0x0F 0x90-0x9F: Byte set on condition
  for (uint8_t i = 0x90; i <= 0x9F; ++i) {
    AddTwoByteOpcode(i, InstructionType::Setcc, 1,
                     {DecodedInstruction::Operand::Type::MEMORY});
  }

  // 0x0F 0xA0-0xAF: Push/Pop FS/GS, CPUID, BT operations
  AddTwoByteOpcode(0xA0, InstructionType::Push, 1,
                   {DecodedInstruction::Operand::Type::REGISTER}); // PUSH FS
  AddTwoByteOpcode(0xA1, InstructionType::Pop, 1,
                   {DecodedInstruction::Operand::Type::REGISTER}); // POP FS
  AddTwoByteOpcode(0xA2, InstructionType::Cpuid, 0, {});
  AddTwoByteOpcode(0xA3, InstructionType::Bt, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xA4, InstructionType::Shld, 3,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::IMMEDIATE});
  AddTwoByteOpcode(0xA5, InstructionType::Shld, 3,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xA8, InstructionType::Push, 1,
                   {DecodedInstruction::Operand::Type::REGISTER}); // PUSH GS
  AddTwoByteOpcode(0xA9, InstructionType::Pop, 1,
                   {DecodedInstruction::Operand::Type::REGISTER}); // POP GS
  AddTwoByteOpcode(0xAA, InstructionType::Rsm, 0, {});
  AddTwoByteOpcode(0xAB, InstructionType::Bts, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xAC, InstructionType::Shrd, 3,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::IMMEDIATE});
  AddTwoByteOpcode(0xAD, InstructionType::Shrd, 3,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xAE, InstructionType::Unknown, 0,
                   {}); // Group 15 - FXSAVE, FXRSTOR, LDMXCSR, STMXCSR, XSAVE,
                        // XRSTOR, XSAVEOPT, CLFLUSH
  AddTwoByteOpcode(0xAF, InstructionType::Imul, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});

  // 0x0F 0xB0-0xBF: CMPXCHG, LSS, BTR, LFS, LGS, MOVZX, MOVSX
  AddTwoByteOpcode(0xB0, InstructionType::Cmpxchg, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xB1, InstructionType::Cmpxchg, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xB2, InstructionType::Lss, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xB3, InstructionType::Btr, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xB4, InstructionType::Lfs, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xB5, InstructionType::Lgs, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xB6, InstructionType::Movzx, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xB7, InstructionType::Movzx, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xB8, InstructionType::Popcnt, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY}); // F3 prefix
  AddTwoByteOpcode(0xB9, InstructionType::Ud1, 0, {}); // Group 10 - UD1
  AddTwoByteOpcode(0xBA, InstructionType::Unknown, 0,
                   {}); // Group 8 - BT, BTS, BTR, BTC
  AddTwoByteOpcode(0xBB, InstructionType::Btc, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xBC, InstructionType::Bsf, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xBD, InstructionType::Bsr, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xBE, InstructionType::Movsx, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xBF, InstructionType::Movsx, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});

  // 0x0F 0xC0-0xCF: XADD, BSWAP, and SSE compare/shuffle
  AddTwoByteOpcode(0xC0, InstructionType::Xadd, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xC1, InstructionType::Xadd, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xC2, InstructionType::Cmpps, 3,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::IMMEDIATE});
  AddTwoByteOpcode(0xC3, InstructionType::Movnti, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER});
  AddTwoByteOpcode(0xC4, InstructionType::Pinsrw, 3,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::IMMEDIATE});
  AddTwoByteOpcode(0xC5, InstructionType::Pextrw, 3,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::IMMEDIATE});
  AddTwoByteOpcode(0xC6, InstructionType::Shufps, 3,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::IMMEDIATE});
  AddTwoByteOpcode(0xC7, InstructionType::Unknown, 0,
                   {}); // Group 9 - CMPXCHG8B, CMPXCHG16B, VMPTRLD, VMPTRST,
                        // VMCLEAR, VMXON, RDRAND, RDSEED
  for (uint8_t i = 0xC8; i <= 0xCF; ++i) {
    AddTwoByteOpcode(i, InstructionType::Bswap, 1,
                     {DecodedInstruction::Operand::Type::REGISTER});
  }

  // 0x0F 0xD0-0xDF: SSE2 and MMX operations
  AddTwoByteOpcode(0xD0, InstructionType::Addsubpd, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xD1, InstructionType::Psrlw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xD2, InstructionType::Psrld, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xD3, InstructionType::Psrlq, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xD4, InstructionType::Paddq, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xD5, InstructionType::Pmullw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xD6, InstructionType::Movq, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::XMM});
  AddTwoByteOpcode(0xD7, InstructionType::Pmovmskb, 2,
                   {DecodedInstruction::Operand::Type::REGISTER,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xD8, InstructionType::Psubusb, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xD9, InstructionType::Psubusw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xDA, InstructionType::Pminub, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xDB, InstructionType::Pand, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xDC, InstructionType::Paddusb, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xDD, InstructionType::Paddusw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xDE, InstructionType::Pmaxub, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xDF, InstructionType::Pandn, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});

  // 0x0F 0xE0-0xEF: MMX average and arithmetic operations
  AddTwoByteOpcode(0xE0, InstructionType::Pavgb, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xE1, InstructionType::Psraw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xE2, InstructionType::Psrad, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xE3, InstructionType::Pavgw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xE4, InstructionType::Pmulhuw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xE5, InstructionType::Pmulhw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xE6, InstructionType::Cvttpd2dq, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xE7, InstructionType::Movntq, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xE8, InstructionType::Psubsb, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xE9, InstructionType::Psubsw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xEA, InstructionType::Pminsw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xEB, InstructionType::Por, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xEC, InstructionType::Paddsb, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xED, InstructionType::Paddsw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xEE, InstructionType::Pmaxsw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xEF, InstructionType::Pxor, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});

  // 0x0F 0xF0-0xFF: MMX arithmetic and logical operations
  AddTwoByteOpcode(0xF0, InstructionType::Lddqu, 2,
                   {DecodedInstruction::Operand::Type::XMM,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xF1, InstructionType::Psllw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xF2, InstructionType::Pslld, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xF3, InstructionType::Psllq, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xF4, InstructionType::Pmuludq, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xF5, InstructionType::Pmaddwd, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xF6, InstructionType::Psadbw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xF7, InstructionType::Maskmovq, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xF8, InstructionType::Psubb, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xF9, InstructionType::Psubw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xFA, InstructionType::Psubd, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xFB, InstructionType::Psubq, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xFC, InstructionType::Paddb, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xFD, InstructionType::Paddw, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xFE, InstructionType::Paddd, 2,
                   {DecodedInstruction::Operand::Type::MEMORY,
                    DecodedInstruction::Operand::Type::MEMORY});
  AddTwoByteOpcode(0xFF, InstructionType::Ud0, 0, {});
}

void InstructionDecoder::InitializeThreeByteOpcodes() {
  // 0x0F 0x38 prefix - SSSE3, SSE4.1, SSE4.2, AES, PCLMULQDQ
  AddThreeByteOpcode(0x3800, InstructionType::Pshufb, 2,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3801, InstructionType::Phaddw, 2,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3802, InstructionType::Phaddd, 2,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3803, InstructionType::Phaddsw, 2,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3804, InstructionType::Pmaddubsw, 2,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3805, InstructionType::Phsubw, 2,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3806, InstructionType::Phsubd, 2,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3807, InstructionType::Phsubsw, 2,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3808, InstructionType::Psignb, 2,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3809, InstructionType::Psignw, 2,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x380A, InstructionType::Psignd, 2,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x380B, InstructionType::Pmulhrsw, 2,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::MEMORY});

  // SSE4.1 instructions
  AddThreeByteOpcode(0x3810, InstructionType::Pblendvb, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3814, InstructionType::Blendvps, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3815, InstructionType::Blendvpd, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3817, InstructionType::Ptest, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x381C, InstructionType::Pabsb, 2,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x381D, InstructionType::Pabsw, 2,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x381E, InstructionType::Pabsd, 2,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::MEMORY});

  AddThreeByteOpcode(0x3820, InstructionType::Pmovsxbw, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3821, InstructionType::Pmovsxbd, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3822, InstructionType::Pmovsxbq, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3823, InstructionType::Pmovsxwd, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3824, InstructionType::Pmovsxwq, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3825, InstructionType::Pmovsxdq, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});

  AddThreeByteOpcode(0x3828, InstructionType::Pmuldq, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3829, InstructionType::Pcmpeqq, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x382A, InstructionType::Movntdqa, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x382B, InstructionType::Packusdw, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});

  AddThreeByteOpcode(0x3830, InstructionType::Pmovzxbw, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3831, InstructionType::Pmovzxbd, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3832, InstructionType::Pmovzxbq, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3833, InstructionType::Pmovzxwd, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3834, InstructionType::Pmovzxwq, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3835, InstructionType::Pmovzxdq, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});

  AddThreeByteOpcode(0x3837, InstructionType::Pcmpgtq, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3838, InstructionType::Pminsb, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3839, InstructionType::Pminsd, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x383A, InstructionType::Pminuw, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x383B, InstructionType::Pminud, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x383C, InstructionType::Pmaxsb, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x383D, InstructionType::Pmaxsd, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x383E, InstructionType::Pmaxuw, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x383F, InstructionType::Pmaxud, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});

  AddThreeByteOpcode(0x3840, InstructionType::Pmulld, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x3841, InstructionType::Phminposuw, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});

  // AES instructions
  AddThreeByteOpcode(0x38DB, InstructionType::Aesimc, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x38DC, InstructionType::Aesenc, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x38DD, InstructionType::Aesenclast, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x38DE, InstructionType::Aesdec, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x38DF, InstructionType::Aesdeclast, 2,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY});

  // CRC32
  AddThreeByteOpcode(0x38F0, InstructionType::Crc32, 2,
                     {DecodedInstruction::Operand::Type::REGISTER,
                      DecodedInstruction::Operand::Type::MEMORY});
  AddThreeByteOpcode(0x38F1, InstructionType::Crc32, 2,
                     {DecodedInstruction::Operand::Type::REGISTER,
                      DecodedInstruction::Operand::Type::MEMORY});

  // 0x0F 0x3A prefix - SSE4.1 immediate instructions
  AddThreeByteOpcode(0x3A08, InstructionType::Roundps, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A09, InstructionType::Roundpd, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A0A, InstructionType::Roundss, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A0B, InstructionType::Roundsd, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A0C, InstructionType::Blendps, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A0D, InstructionType::Blendpd, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A0E, InstructionType::Pblendw, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A0F, InstructionType::Palignr, 3,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});

  AddThreeByteOpcode(0x3A14, InstructionType::Pextrb, 3,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A15, InstructionType::Pextrw, 3,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A16, InstructionType::Pextrd, 3,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A17, InstructionType::Extractps, 3,
                     {DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::IMMEDIATE});

  AddThreeByteOpcode(0x3A20, InstructionType::Pinsrb, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A21, InstructionType::Insertps, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A22, InstructionType::Pinsrd, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});

  AddThreeByteOpcode(0x3A40, InstructionType::Dpps, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A41, InstructionType::Dppd, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A42, InstructionType::Mpsadbw, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});

  AddThreeByteOpcode(0x3A60, InstructionType::Pcmpestrm, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A61, InstructionType::Pcmpestri, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A62, InstructionType::Pcmpistrm, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
  AddThreeByteOpcode(0x3A63, InstructionType::Pcmpistri, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});

  // AES key generation
  AddThreeByteOpcode(0x3ADF, InstructionType::Aeskeygenassist, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});

  // PCLMULQDQ
  AddThreeByteOpcode(0x3A44, InstructionType::Pclmulqdq, 3,
                     {DecodedInstruction::Operand::Type::XMM,
                      DecodedInstruction::Operand::Type::MEMORY,
                      DecodedInstruction::Operand::Type::IMMEDIATE});
}

void InstructionDecoder::InitializeVEXOpcodes() {
  // VEX-encoded AVX instructions
  // VEX.0F prefix space (most common AVX instructions)

  // VEX.0F.10-1F: AVX move instructions
  vexOpcodes[0x0F10] = {InstructionType::Vmovups,
                        2,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F11] = {InstructionType::Vmovups,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::XMM}};
  vexOpcodes[0x0F12] = {InstructionType::Vmovlps,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F13] = {InstructionType::Vmovlps,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::XMM}};
  vexOpcodes[0x0F14] = {InstructionType::Vunpcklps,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F15] = {InstructionType::Vunpckhps,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F16] = {InstructionType::Vmovhps,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F17] = {InstructionType::Vmovhps,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::XMM}};

  vexOpcodes[0x0F28] = {InstructionType::Vmovaps,
                        2,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F29] = {InstructionType::Vmovaps,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::XMM}};
  vexOpcodes[0x0F2A] = {InstructionType::Vcvtpi2ps,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F2B] = {InstructionType::Vmovntps,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::XMM}};
  vexOpcodes[0x0F2C] = {InstructionType::Vcvttps2pi,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::XMM}};
  vexOpcodes[0x0F2D] = {InstructionType::Vcvtps2pi,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::XMM}};
  vexOpcodes[0x0F2E] = {InstructionType::Vucomiss,
                        2,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F2F] = {InstructionType::Vcomiss,
                        2,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};

  // VEX.0F.50-5F: AVX arithmetic and logical
  vexOpcodes[0x0F50] = {InstructionType::Vmovmskps,
                        2,
                        {DecodedInstruction::Operand::Type::REGISTER,
                         DecodedInstruction::Operand::Type::XMM}};
  vexOpcodes[0x0F51] = {InstructionType::Vsqrtps,
                        2,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F52] = {InstructionType::Vrsqrtps,
                        2,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F53] = {InstructionType::Vrcpps,
                        2,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F54] = {InstructionType::Vandps,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F55] = {InstructionType::Vandnps,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F56] = {InstructionType::Vorps,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F57] = {InstructionType::Vxorps,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F58] = {InstructionType::Vaddps,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F59] = {InstructionType::Vmulps,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F5A] = {InstructionType::Vcvtps2pd,
                        2,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F5B] = {InstructionType::Vcvtdq2ps,
                        2,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F5C] = {InstructionType::Vsubps,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F5D] = {InstructionType::Vminps,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F5E] = {InstructionType::Vdivps,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F5F] = {InstructionType::Vmaxps,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};

  // VEX.0F.60-6F: AVX packed integer operations
  vexOpcodes[0x0F60] = {InstructionType::Vpunpcklbw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F61] = {InstructionType::Vpunpcklwd,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F62] = {InstructionType::Vpunpckldq,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F63] = {InstructionType::Vpacksswb,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F64] = {InstructionType::Vpcmpgtb,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F65] = {InstructionType::Vpcmpgtw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F66] = {InstructionType::Vpcmpgtd,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F67] = {InstructionType::Vpackuswb,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F68] = {InstructionType::Vpunpckhbw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F69] = {InstructionType::Vpunpckhwd,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F6A] = {InstructionType::Vpunpckhdq,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F6B] = {InstructionType::Vpackssdw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F6C] = {InstructionType::Vpunpcklqdq,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F6D] = {InstructionType::Vpunpckhqdq,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F6E] = {InstructionType::Vmovd,
                        2,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F6F] = {InstructionType::Vmovdqa,
                        2,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};

  // VEX.0F.70-7F: AVX shuffle and conversion
  vexOpcodes[0x0F70] = {InstructionType::Vpshufd,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::IMMEDIATE}};
  vexOpcodes[0x0F74] = {InstructionType::Vpcmpeqb,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F75] = {InstructionType::Vpcmpeqw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F76] = {InstructionType::Vpcmpeqd,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F77] = {InstructionType::Vzeroupper, 0, {}};
  vexOpcodes[0x0F7C] = {InstructionType::Vhaddpd,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F7D] = {InstructionType::Vhsubpd,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F7E] = {InstructionType::Vmovd,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::XMM}};
  vexOpcodes[0x0F7F] = {InstructionType::Vmovdqa,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::XMM}};

  // VEX.0F.C0-CF: AVX compare and shuffle
  vexOpcodes[0x0FC2] = {InstructionType::Vcmpps,
                        4,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::IMMEDIATE}};
  vexOpcodes[0x0FC4] = {InstructionType::Vpinsrw,
                        4,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::IMMEDIATE}};
  vexOpcodes[0x0FC5] = {InstructionType::Vpextrw,
                        3,
                        {DecodedInstruction::Operand::Type::REGISTER,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::IMMEDIATE}};
  vexOpcodes[0x0FC6] = {InstructionType::Vshufps,
                        4,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::IMMEDIATE}};

  // VEX.0F.D0-DF: More AVX operations
  vexOpcodes[0x0FD0] = {InstructionType::Vaddsubpd,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FD1] = {InstructionType::Vpsrlw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FD2] = {InstructionType::Vpsrld,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FD3] = {InstructionType::Vpsrlq,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FD4] = {InstructionType::Vpaddq,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FD5] = {InstructionType::Vpmullw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FD6] = {InstructionType::Vmovq,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::XMM}};
  vexOpcodes[0x0FD7] = {InstructionType::Vpmovmskb,
                        2,
                        {DecodedInstruction::Operand::Type::REGISTER,
                         DecodedInstruction::Operand::Type::XMM}};
  vexOpcodes[0x0FD8] = {InstructionType::Vpsubusb,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FD9] = {InstructionType::Vpsubusw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FDA] = {InstructionType::Vpminub,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FDB] = {InstructionType::Vpand,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FDC] = {InstructionType::Vpaddusb,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FDD] = {InstructionType::Vpaddusw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FDE] = {InstructionType::Vpmaxub,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FDF] = {InstructionType::Vpandn,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};

  // VEX.0F.E0-EF: More AVX operations
  vexOpcodes[0x0FE0] = {InstructionType::Vpavgb,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FE1] = {InstructionType::Vpsraw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FE2] = {InstructionType::Vpsrad,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FE3] = {InstructionType::Vpavgw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FE4] = {InstructionType::Vpmulhuw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FE5] = {InstructionType::Vpmulhw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FE6] = {InstructionType::Vcvttpd2dq,
                        2,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FE7] = {InstructionType::Vmovntdq,
                        2,
                        {DecodedInstruction::Operand::Type::MEMORY,
                         DecodedInstruction::Operand::Type::XMM}};
  vexOpcodes[0x0FE8] = {InstructionType::Vpsubsb,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FE9] = {InstructionType::Vpsubsw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FEA] = {InstructionType::Vpminsw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FEB] = {InstructionType::Vpor,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FEC] = {InstructionType::Vpaddsb,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FED] = {InstructionType::Vpaddsw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FEE] = {InstructionType::Vpmaxsw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FEF] = {InstructionType::Vpxor,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};

  // VEX.0F.F0-FF: Final AVX operations
  vexOpcodes[0x0FF0] = {InstructionType::Vlddqu,
                        2,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FF1] = {InstructionType::Vpsllw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FF2] = {InstructionType::Vpslld,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FF3] = {InstructionType::Vpsllq,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FF4] = {InstructionType::Vpmuludq,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FF5] = {InstructionType::Vpmaddwd,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FF6] = {InstructionType::Vpsadbw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FF7] = {InstructionType::Vmaskmovdqu,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FF8] = {InstructionType::Vpsubb,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FF9] = {InstructionType::Vpsubw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FFA] = {InstructionType::Vpsubd,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FFB] = {InstructionType::Vpsubq,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FFC] = {InstructionType::Vpaddb,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FFD] = {InstructionType::Vpaddw,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0FFE] = {InstructionType::Vpaddd,
                        3,
                        {DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::XMM,
                         DecodedInstruction::Operand::Type::MEMORY}};

  // VEX.0F38 prefix space - AVX2 and other extensions
  vexOpcodes[0x0F3800] = {InstructionType::Vpshufb,
                          3,
                          {DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F3801] = {InstructionType::Vphaddw,
                          3,
                          {DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F3802] = {InstructionType::Vphaddd,
                          3,
                          {DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F3803] = {InstructionType::Vphaddsw,
                          3,
                          {DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F3804] = {InstructionType::Vpmaddubsw,
                          3,
                          {DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F3805] = {InstructionType::Vphsubw,
                          3,
                          {DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F3806] = {InstructionType::Vphsubd,
                          3,
                          {DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F3807] = {InstructionType::Vphsubsw,
                          3,
                          {DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F3808] = {InstructionType::Vpsignb,
                          3,
                          {DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F3809] = {InstructionType::Vpsignw,
                          3,
                          {DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F380A] = {InstructionType::Vpsignd,
                          3,
                          {DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F380B] = {InstructionType::Vpmulhrsw,
                          3,
                          {DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F380C] = {InstructionType::Vpermilps,
                          3,
                          {DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F380D] = {InstructionType::Vpermilpd,
                          3,
                          {DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F380E] = {InstructionType::Vtestps,
                          2,
                          {DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
  vexOpcodes[0x0F380F] = {InstructionType::Vtestpd,
                          2,
                          {DecodedInstruction::Operand::Type::XMM,
                           DecodedInstruction::Operand::Type::MEMORY}};
}

void InstructionDecoder::InitializeEVEXOpcodes() {
  // EVEX-encoded AVX-512 instructions
  // EVEX.0F prefix space

  // EVEX.0F.10-1F: AVX-512 move instructions
  evexOpcodes[0x0F10] = {InstructionType::Vmovups_512,
                         2,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F11] = {InstructionType::Vmovups_512,
                         2,
                         {DecodedInstruction::Operand::Type::MEMORY,
                          DecodedInstruction::Operand::Type::ZMM}};
  evexOpcodes[0x0F14] = {InstructionType::Vunpcklps,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F15] = {InstructionType::Vunpckhps,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};

  evexOpcodes[0x0F28] = {InstructionType::Vmovaps_512,
                         2,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F29] = {InstructionType::Vmovaps_512,
                         2,
                         {DecodedInstruction::Operand::Type::MEMORY,
                          DecodedInstruction::Operand::Type::ZMM}};
  evexOpcodes[0x0F2E] = {InstructionType::Vucomiss,
                         2,
                         {DecodedInstruction::Operand::Type::XMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F2F] = {InstructionType::Vcomiss,
                         2,
                         {DecodedInstruction::Operand::Type::XMM,
                          DecodedInstruction::Operand::Type::MEMORY}};

  // EVEX.0F.50-5F: AVX-512 arithmetic
  evexOpcodes[0x0F51] = {InstructionType::Vsqrtps_512,
                         2,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F54] = {InstructionType::Vandps,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F55] = {InstructionType::Vandnps,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F56] = {InstructionType::Vorps,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F57] = {InstructionType::Vxorps,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F58] = {InstructionType::Vaddps_512,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F59] = {InstructionType::Vmulps_512,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F5A] = {InstructionType::Vcvtps2pd,
                         2,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F5B] = {InstructionType::Vcvtdq2ps,
                         2,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F5C] = {InstructionType::Vsubps_512,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F5D] = {InstructionType::Vminps,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F5E] = {InstructionType::Vdivps_512,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F5F] = {InstructionType::Vmaxps,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};

  // More EVEX.0F opcodes for AVX-512
  evexOpcodes[0x0F60] = {InstructionType::Vpunpcklbw,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F61] = {InstructionType::Vpunpcklwd,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F62] = {InstructionType::Vpunpckldq,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F63] = {InstructionType::Vpacksswb,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F64] = {InstructionType::Vpcmpgtb,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F65] = {InstructionType::Vpcmpgtw,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F66] = {InstructionType::Vpcmpgtd,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F67] = {InstructionType::Vpackuswb,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F68] = {InstructionType::Vpunpckhbw,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F69] = {InstructionType::Vpunpckhwd,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F6A] = {InstructionType::Vpunpckhdq,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F6B] = {InstructionType::Vpackssdw,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F6C] = {InstructionType::Vpunpcklqdq,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F6D] = {InstructionType::Vpunpckhqdq,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F6E] = {InstructionType::Vmovd,
                         2,
                         {DecodedInstruction::Operand::Type::XMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F6F] = {InstructionType::Vmovdqa,
                         2,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};

  // EVEX.0F.70-7F
  evexOpcodes[0x0F70] = {InstructionType::Vpshufd,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY,
                          DecodedInstruction::Operand::Type::IMMEDIATE}};
  evexOpcodes[0x0F74] = {InstructionType::Vpcmpeqb,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F75] = {InstructionType::Vpcmpeqw,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F76] = {InstructionType::Vpcmpeqd,
                         3,
                         {DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::ZMM,
                          DecodedInstruction::Operand::Type::MEMORY}};
  evexOpcodes[0x0F7E] = {InstructionType::Vmovd,
                         2,
                         {DecodedInstruction::Operand::Type::MEMORY,
                          DecodedInstruction::Operand::Type::XMM}};
  evexOpcodes[0x0F7F] = {InstructionType::Vmovdqa,
                         2,
                         {DecodedInstruction::Operand::Type::MEMORY,
                          DecodedInstruction::Operand::Type::ZMM}};
}

void InstructionDecoder::InitializeFPUOpcodes() {
  // x87 FPU instructions (0xD8-0xDF prefix)

  // 0xD8: FPU arithmetic (single precision)
  fpuOpcodes[0xD800] = {
      InstructionType::Fadd, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD801] = {
      InstructionType::Fmul, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD802] = {
      InstructionType::Fcom, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD803] = {
      InstructionType::Fcomp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD804] = {
      InstructionType::Fsub, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD805] = {
      InstructionType::Fsubr, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD806] = {
      InstructionType::Fdiv, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD807] = {
      InstructionType::Fdivr, 1, {DecodedInstruction::Operand::Type::MEMORY}};

  // 0xD9: FPU load/store and transcendental
  fpuOpcodes[0xD900] = {
      InstructionType::Fld, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD902] = {
      InstructionType::Fst, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD903] = {
      InstructionType::Fstp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD904] = {
      InstructionType::Fldenv, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD905] = {
      InstructionType::Fldcw, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD906] = {
      InstructionType::Fnstenv, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xD907] = {
      InstructionType::Fnstcw, 1, {DecodedInstruction::Operand::Type::MEMORY}};

  // 0xDA: FPU integer operations
  fpuOpcodes[0xDA00] = {
      InstructionType::Fiadd, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDA01] = {
      InstructionType::Fimul, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDA02] = {
      InstructionType::Ficom, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDA03] = {
      InstructionType::Ficomp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDA04] = {
      InstructionType::Fisub, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDA05] = {
      InstructionType::Fisubr, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDA06] = {
      InstructionType::Fidiv, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDA07] = {
      InstructionType::Fidivr, 1, {DecodedInstruction::Operand::Type::MEMORY}};

  // 0xDB: FPU integer load/store
  fpuOpcodes[0xDB00] = {
      InstructionType::Fild, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDB01] = {
      InstructionType::Fisttp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDB02] = {
      InstructionType::Fist, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDB03] = {
      InstructionType::Fistp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDB05] = {
      InstructionType::Fld, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDB07] = {
      InstructionType::Fstp, 1, {DecodedInstruction::Operand::Type::MEMORY}};

  // 0xDC: FPU arithmetic (double precision)
  fpuOpcodes[0xDC00] = {
      InstructionType::Fadd, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDC01] = {
      InstructionType::Fmul, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDC02] = {
      InstructionType::Fcom, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDC03] = {
      InstructionType::Fcomp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDC04] = {
      InstructionType::Fsub, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDC05] = {
      InstructionType::Fsubr, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDC06] = {
      InstructionType::Fdiv, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDC07] = {
      InstructionType::Fdivr, 1, {DecodedInstruction::Operand::Type::MEMORY}};

  // 0xDD: FPU load/store (double precision)
  fpuOpcodes[0xDD00] = {
      InstructionType::Fld, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDD01] = {
      InstructionType::Fisttp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDD02] = {
      InstructionType::Fst, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDD03] = {
      InstructionType::Fstp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDD04] = {
      InstructionType::Frstor, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDD06] = {
      InstructionType::Fnsave, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDD07] = {
      InstructionType::Fnstsw, 1, {DecodedInstruction::Operand::Type::MEMORY}};

  // 0xDE: FPU arithmetic (word integer)
  fpuOpcodes[0xDE00] = {
      InstructionType::Fiadd, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDE01] = {
      InstructionType::Fimul, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDE02] = {
      InstructionType::Ficom, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDE03] = {
      InstructionType::Ficomp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDE04] = {
      InstructionType::Fisub, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDE05] = {
      InstructionType::Fisubr, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDE06] = {
      InstructionType::Fidiv, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDE07] = {
      InstructionType::Fidivr, 1, {DecodedInstruction::Operand::Type::MEMORY}};

  // 0xDF: FPU integer load/store (word)
  fpuOpcodes[0xDF00] = {
      InstructionType::Fild, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDF01] = {
      InstructionType::Fisttp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDF02] = {
      InstructionType::Fist, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDF03] = {
      InstructionType::Fistp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDF04] = {
      InstructionType::Fbld, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDF05] = {
      InstructionType::Fild, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDF06] = {
      InstructionType::Fbstp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  fpuOpcodes[0xDF07] = {
      InstructionType::Fistp, 1, {DecodedInstruction::Operand::Type::MEMORY}};
}

void InstructionDecoder::InitializeGroupOpcodes() {
  // Group opcodes (instructions that use ModR/M extension)

  // Group 1: Immediate arithmetic (0x80, 0x81, 0x83)
  groupOpcodes[0x8000] = {InstructionType::Add,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0x8001] = {InstructionType::Or,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0x8002] = {InstructionType::Adc,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0x8003] = {InstructionType::Sbb,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0x8004] = {InstructionType::And,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0x8005] = {InstructionType::Sub,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0x8006] = {InstructionType::Xor,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0x8007] = {InstructionType::Cmp,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};

  // Group 2: Shift/rotate (0xC0, 0xC1, 0xD0, 0xD1, 0xD2, 0xD3)
  groupOpcodes[0xC000] = {InstructionType::Rol,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xC001] = {InstructionType::Ror,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xC002] = {InstructionType::Rcl,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xC003] = {InstructionType::Rcr,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xC004] = {InstructionType::Shl,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xC005] = {InstructionType::Shr,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xC007] = {InstructionType::Sar,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};

  // Group 2 with 1 as operand (0xD0, 0xD1)
  groupOpcodes[0xD000] = {InstructionType::Rol,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xD001] = {InstructionType::Ror,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xD002] = {InstructionType::Rcl,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xD003] = {InstructionType::Rcr,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xD004] = {InstructionType::Shl,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xD005] = {InstructionType::Shr,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xD007] = {InstructionType::Sar,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};

  // Group 2 with CL register as operand (0xD2, 0xD3)
  groupOpcodes[0xD200] = {InstructionType::Rol,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::REGISTER}};
  groupOpcodes[0xD201] = {InstructionType::Ror,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::REGISTER}};
  groupOpcodes[0xD202] = {InstructionType::Rcl,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::REGISTER}};
  groupOpcodes[0xD203] = {InstructionType::Rcr,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::REGISTER}};
  groupOpcodes[0xD204] = {InstructionType::Shl,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::REGISTER}};
  groupOpcodes[0xD205] = {InstructionType::Shr,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::REGISTER}};
  groupOpcodes[0xD207] = {InstructionType::Sar,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::REGISTER}};

  // Group 3: TEST, NOT, NEG, MUL, IMUL, DIV, IDIV (0xF6, 0xF7)
  groupOpcodes[0xF600] = {InstructionType::Test,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xF602] = {
      InstructionType::Not, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xF603] = {
      InstructionType::Neg, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xF604] = {
      InstructionType::Mul, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xF605] = {
      InstructionType::Imul, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xF606] = {
      InstructionType::Div, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xF607] = {
      InstructionType::Idiv, 1, {DecodedInstruction::Operand::Type::MEMORY}};

  // Group 4: INC/DEC byte (0xFE)
  groupOpcodes[0xFE00] = {
      InstructionType::Inc, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xFE01] = {
      InstructionType::Dec, 1, {DecodedInstruction::Operand::Type::MEMORY}};

  // Group 5: INC/DEC/CALL/JMP/PUSH (0xFF)
  groupOpcodes[0xFF00] = {
      InstructionType::Inc, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xFF01] = {
      InstructionType::Dec, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xFF02] = {
      InstructionType::Call, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xFF03] = {InstructionType::Call_far,
                          1,
                          {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xFF04] = {
      InstructionType::Jump, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xFF05] = {
      InstructionType::Jmp_far, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xFF06] = {
      InstructionType::Push, 1, {DecodedInstruction::Operand::Type::MEMORY}};

  // Group 6: SLDT, STR, LLDT, LTR, VERR, VERW (0x0F 0x00)
  groupOpcodes[0x0000] = {
      InstructionType::Sldt, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0001] = {
      InstructionType::Str, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0002] = {
      InstructionType::Lldt, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0003] = {
      InstructionType::Ltr, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0004] = {
      InstructionType::Verr, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0005] = {
      InstructionType::Verw, 1, {DecodedInstruction::Operand::Type::MEMORY}};

  // Group 7: SGDT, SIDT, LGDT, LIDT, SMSW, LMSW, INVLPG, SWAPGS (0x0F 0x01)
  groupOpcodes[0x0100] = {
      InstructionType::Sgdt, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0101] = {
      InstructionType::Sidt, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0102] = {
      InstructionType::Lgdt, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0103] = {
      InstructionType::Lidt, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0104] = {
      InstructionType::Smsw, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0106] = {
      InstructionType::Lmsw, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0x0107] = {
      InstructionType::Invlpg, 1, {DecodedInstruction::Operand::Type::MEMORY}};

  // Group 8: BT, BTS, BTR, BTC (0x0F 0xBA)
  groupOpcodes[0xBA04] = {InstructionType::Bt,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xBA05] = {InstructionType::Bts,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xBA06] = {InstructionType::Btr,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};
  groupOpcodes[0xBA07] = {InstructionType::Btc,
                          2,
                          {DecodedInstruction::Operand::Type::MEMORY,
                           DecodedInstruction::Operand::Type::IMMEDIATE}};

  // Group 9: CMPXCHG8B, CMPXCHG16B, VMPTRLD, VMPTRST, VMCLEAR, VMXON, RDRAND,
  // RDSEED (0x0F 0xC7)
  groupOpcodes[0xC701] = {InstructionType::Cmpxchg8b,
                          1,
                          {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xC702] = {InstructionType::Cmpxchg16b,
                          1,
                          {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xC706] = {
      InstructionType::Vmptrld, 1, {DecodedInstruction::Operand::Type::MEMORY}};
  groupOpcodes[0xC707] = {
      InstructionType::Vmptrst, 1, {DecodedInstruction::Operand::Type::MEMORY}};
}

void InstructionDecoder::AddOpcode(
    uint8_t opcode, InstructionType type, uint8_t operandCount,
    std::array<DecodedInstruction::Operand::Type, 4> types) {
  OpcodeInfo info{type, operandCount, types};
  singleByteOpcodes[opcode] = info;
  spdlog::trace("Added opcode 0x{:x}: type={}, operands={}", opcode,
                static_cast<int>(type), operandCount);
}

void InstructionDecoder::AddTwoByteOpcode(
    uint8_t opcode, InstructionType type, uint8_t operandCount,
    std::array<DecodedInstruction::Operand::Type, 4> types) {
  OpcodeInfo info{type, operandCount, types};
  twoByteOpcodes[opcode] = info;
  spdlog::trace("Added two-byte opcode 0x0F{:02x}: type={}, operands={}",
                opcode, static_cast<int>(type), operandCount);
}

void InstructionDecoder::AddThreeByteOpcode(
    uint16_t opcode, InstructionType type, uint8_t operandCount,
    std::array<DecodedInstruction::Operand::Type, 4> types) {
  OpcodeInfo info{type, operandCount, types};
  threeByteOpcodes[opcode] = info;
  spdlog::trace("Added three-byte opcode 0x{:04x}: type={}, operands={}",
                opcode, static_cast<int>(type), operandCount);
}

Register InstructionDecoder::ResolveRegister(uint8_t baseRegField,
                                             uint8_t rexExtBit) {
  uint8_t reg = baseRegField & 0x7;
  Register resolved = static_cast<Register>((rexExtBit ? 8 : 0) + reg);
  if (!IsValidRegister(resolved)) {
    spdlog::warn("Invalid register resolved: base=0x{:x}, rexExt=0x{:x}",
                 baseRegField, rexExtBit);
    return Register::NONE;
  }
  return resolved;
}

DecoderErrorInfo InstructionDecoder::Decode(uint64_t addr,
                                            const uint8_t *buffer,
                                            size_t bufferSize,
                                            DecodedInstruction &instr) {
  if (!buffer || bufferSize == 0) {
    spdlog::error("Decode failed at 0x{:x}: null or empty buffer", addr);
    return {DecoderError::BufferOverflow,
            "Null or empty buffer at 0x" + std::to_string(addr)};
  }

  const uint8_t *startBuffer = buffer;
  size_t initialSize = bufferSize;
  instr.reset();

  spdlog::trace("Decoding instruction at 0x{:x}, buffer size={}", addr,
                bufferSize);
  ParsePrefixes(buffer, bufferSize, instr);
  if (instr.isVex) {
    ParseVEX(buffer, bufferSize, instr);
  }

  if (bufferSize == 0) {
    instr.length = static_cast<uint8_t>(buffer - startBuffer);
    spdlog::error("Decode failed at 0x{:x}: buffer empty after prefixes", addr);
    return {DecoderError::IncompleteInstruction,
            "Buffer empty after prefixes at 0x" + std::to_string(addr)};
  }

  ParseOpcode(buffer, bufferSize, instr);
  instr.length = static_cast<uint8_t>(buffer - startBuffer);
  if (instr.length > initialSize) {
    spdlog::error(
        "Decode failed at 0x{:x}: instruction length {} exceeds buffer size {}",
        addr, instr.length, initialSize);
    return {DecoderError::BufferOverflow,
            "Instruction length " + std::to_string(instr.length) +
                " exceeds buffer size " + std::to_string(initialSize)};
  }
  if (instr.instType == InstructionType::Unknown) {
    spdlog::error("Decode failed at 0x{:x}: unknown instruction, opcode=0x{:x}",
                  addr, instr.opcode);
    return {DecoderError::InvalidInstruction,
            "Unknown instruction at 0x" + std::to_string(addr) + ", opcode=0x" +
                std::to_string(instr.opcode)};
  }

  if (!instr.validate()) {
    spdlog::error("Decode failed at 0x{:x}: invalid operands", addr);
    return {DecoderError::InvalidInstruction,
            "Invalid operands at 0x" + std::to_string(addr)};
  }

  m_stats[instr.instType]++;
  spdlog::debug("Decoded instruction at 0x{:x}:\n{}", addr, instr.to_string());
  return {DecoderError::Success, ""};
}

void InstructionDecoder::ParsePrefixes(const uint8_t *&buffer,
                                       size_t &remaining,
                                       DecodedInstruction &instr) {
  int prefixIndex = 0;
  bool rexFound = false;
  const uint8_t *initialBuffer = buffer;
  spdlog::trace("Parsing prefixes at buffer offset 0x{:x}, remaining={}",
                (uint64_t)(buffer - initialBuffer), remaining);

  while (remaining > 0 && prefixIndex < 4) {
    uint8_t byte = *buffer;
    if (byte >= 0x40 && byte <= 0x4F) {
      if (prefixIndex > 0) {
        spdlog::warn("REX prefix (0x{:x}) after legacy prefix at offset 0x{:x}",
                     byte, (uint64_t)(buffer - initialBuffer));
        instr.instType = InstructionType::Unknown;
        return;
      }
      if (rexFound) {
        spdlog::warn("Multiple REX prefixes at offset 0x{:x}",
                     (uint64_t)(buffer - initialBuffer));
        instr.instType = InstructionType::Unknown;
        return;
      }
      instr.rex = byte;
      rexFound = true;
      spdlog::trace("Found REX prefix 0x{:x}", byte);
      buffer++;
      remaining--;
    } else if (byte == 0xC4 || byte == 0xC5) {
      instr.isVex = true;
      spdlog::trace("Found VEX prefix 0x{:x}", byte);
      return;
    } else if (byte == 0xF0 || byte == 0xF2 || byte == 0xF3 || byte == 0x66 ||
               byte == 0x67 || byte == 0x2E || byte == 0x3E || byte == 0x26 ||
               byte == 0x64 || byte == 0x65) {
      if (byte == 0xF3) {
        instr.repPrefix = instr.repePrefix = true;
        spdlog::trace("Found REP/REPE prefix 0xF3");
      } else if (byte == 0xF2) {
        instr.repnePrefix = true;
        spdlog::trace("Found REPNE prefix 0xF2");
      } else if (byte == 0x66) {
        instr.operandSizeOverride = true;
        spdlog::trace("Found operand size override prefix 0x66");
      } else if (byte == 0x67) {
        instr.addressSizeOverride = true;
        spdlog::trace("Found address size override prefix 0x67");
      }
      if (prefixIndex < 4) {
        instr.prefixes[prefixIndex++] = byte;
      } else {
        spdlog::warn("Too many legacy prefixes at offset 0x{:x}",
                     (uint64_t)(buffer - initialBuffer));
        instr.instType = InstructionType::Unknown;
        return;
      }
      buffer++;
      remaining--;
    } else {
      break;
    }
  }
  spdlog::trace("Parsed {} prefixes, remaining bytes={}", prefixIndex,
                remaining);
}

void InstructionDecoder::ParseVEX(const uint8_t *&buffer, size_t &remaining,
                                  DecodedInstruction &instr) {
  if (remaining < 2) {
    spdlog::error("Incomplete VEX prefix, remaining={}", remaining);
    instr.instType = InstructionType::Unknown;
    return;
  }
  uint8_t vex1 = *buffer++;
  uint8_t vex2 = *buffer++;
  remaining -= 2;

  if (vex1 == 0xC4) {
    if (remaining < 1) {
      spdlog::error("Incomplete 3-byte VEX prefix, remaining={}", remaining);
      instr.instType = InstructionType::Unknown;
      return;
    }
    uint8_t vex3 = *buffer++;
    remaining--;
    instr.vex.set_3byte((vex2 >> 6) & 0x3, (vex2 >> 5) & 1, vex2 & 0x3,
                        (vex3 >> 3) & 0xF, (vex3 >> 7) & 1);
    instr.opcode = *buffer++;
    remaining--;
    spdlog::trace("Parsed 3-byte VEX: m=0x{:x}, L=0x{:x}, pp=0x{:x}, "
                  "vvvv=0x{:x}, W=0x{:x}, "
                  "opcode=0x{:x}",
                  instr.vex.m(), instr.vex.L(), instr.vex.pp(),
                  instr.vex.vvvv(), instr.vex.W(), instr.opcode);
  } else {
    instr.vex.set_2byte((vex2 >> 5) & 1, vex2 & 0x3, (vex2 >> 3) & 0xF);
    instr.opcode = *buffer++;
    remaining--;
    spdlog::trace("Parsed 2-byte VEX: L=0x{:x}, pp=0x{:x}, "
                  "vvvv=0x{:x}, opcode=0x{:x}",
                  instr.vex.L(), instr.vex.pp(), instr.vex.vvvv(),
                  instr.opcode);
  }

  if (remaining < 1) {
    spdlog::error("Incomplete instruction after VEX, remaining={}", remaining);
    instr.instType = InstructionType::Unknown;
    return;
  }
  ParseModRM(buffer, remaining, instr, *buffer);
}

void InstructionDecoder::ParseModRM(const uint8_t *&buffer, size_t &remaining,
                                    DecodedInstruction &instr,
                                    uint8_t modrmByte) {
  if (remaining < 1) {
    spdlog::error("Incomplete ModRM byte, remaining={}", remaining);
    instr.instType = InstructionType::Unknown;
    return;
  }
  buffer++;
  remaining--;

  uint8_t mod = (modrmByte >> 6) & 0x3;
  uint8_t reg = (modrmByte >> 3) & 0x7;
  uint8_t rm = modrmByte & 0x7;

  instr.operandCount = 1;
  instr.operands[0].type = instr.isVex
                               ? DecodedInstruction::Operand::Type::XMM
                               : DecodedInstruction::Operand::Type::REGISTER;
  instr.operands[0].reg = ResolveRegister(reg, (instr.rex & REX_R) >> 2);
  instr.operands[0].size =
      instr.HasRexW() ? 64 : (instr.operandSizeOverride ? 16 : 32);
  spdlog::trace("ModRM reg operand: type={}, reg={}, size={}",
                static_cast<int>(instr.operands[0].type),
                static_cast<int>(instr.operands[0].reg),
                instr.operands[0].size);

  if (mod == 0b11) {
    instr.operandCount = 2;
    instr.operands[1].type = instr.isVex
                                 ? DecodedInstruction::Operand::Type::XMM
                                 : DecodedInstruction::Operand::Type::REGISTER;
    instr.operands[1].reg = ResolveRegister(rm, (instr.rex & REX_B));
    instr.operands[1].size =
        instr.HasRexW() ? 64 : (instr.operandSizeOverride ? 16 : 32);
    spdlog::trace("ModRM r/m register operand: type={}, reg={}, size={}",
                  static_cast<int>(instr.operands[1].type),
                  static_cast<int>(instr.operands[1].reg),
                  instr.operands[1].size);
  } else {
    instr.operandCount = 2;
    instr.operands[1].type = DecodedInstruction::Operand::Type::MEMORY;
    instr.operands[1].size =
        instr.HasRexW() ? 64 : (instr.operandSizeOverride ? 16 : 32);

    if (mod == 0b00 && rm == 0b101) {
      if (remaining < 4) {
        spdlog::error("Incomplete RIP-relative displacement, remaining={}",
                      remaining);
        instr.instType = InstructionType::Unknown;
        return;
      }
      instr.operands[1].memory.base = Register::RIP;
      instr.operands[1].memory.displacement =
          *reinterpret_cast<const int32_t *>(buffer);
      buffer += 4;
      remaining -= 4;
      spdlog::trace("ModRM RIP-relative: base=RIP, displacement=0x{:x}",
                    instr.operands[1].memory.displacement);
    } else if (rm == 0b100) {
      if (remaining < 1) {
        spdlog::error("Incomplete SIB byte, remaining={}", remaining);
        instr.instType = InstructionType::Unknown;
        return;
      }
      uint8_t sib = *buffer;
      buffer++;
      remaining--;
      uint8_t scale = (sib >> 6) & 0x3;
      uint8_t index = (sib >> 3) & 0x7;
      uint8_t base = sib & 0x7;

      instr.operands[1].memory.scale = 1 << scale;
      instr.operands[1].memory.index =
          (index == 0b100) ? Register::NONE
                           : ResolveRegister(index, (instr.rex & REX_X) >> 1);
      instr.operands[1].memory.base =
          ResolveRegister(base, (instr.rex & REX_B));

      if (mod == 0b00 && base == 0b101) {
        if (remaining < 4) {
          spdlog::error("Incomplete SIB disp32, remaining={}", remaining);
          instr.instType = InstructionType::Unknown;
          return;
        }
        instr.operands[1].memory.base = Register::NONE;
        instr.operands[1].memory.displacement =
            *reinterpret_cast<const int32_t *>(buffer);
        buffer += 4;
        remaining -= 4;
        spdlog::trace("ModRM SIB: scale={}, index={}, base=NONE, "
                      "displacement=0x{:x}",
                      instr.operands[1].memory.scale,
                      static_cast<int>(instr.operands[1].memory.index),
                      instr.operands[1].memory.displacement);
      } else {
        spdlog::trace("ModRM SIB: scale={}, index={}, base={}",
                      instr.operands[1].memory.scale,
                      static_cast<int>(instr.operands[1].memory.index),
                      static_cast<int>(instr.operands[1].memory.base));
      }
    } else {
      instr.operands[1].memory.base = ResolveRegister(rm, (instr.rex & REX_B));
      spdlog::trace("ModRM memory: base={}",
                    static_cast<int>(instr.operands[1].memory.base));
    }

    if (mod == 0b01) {
      if (remaining < 1) {
        spdlog::error("Incomplete disp8, remaining={}", remaining);
        instr.instType = InstructionType::Unknown;
        return;
      }
      instr.operands[1].memory.displacement = static_cast<int8_t>(*buffer);
      buffer++;
      remaining--;
      spdlog::trace("ModRM disp8: displacement=0x{:x}",
                    instr.operands[1].memory.displacement);
    } else if (mod == 0b10) {
      if (remaining < 4) {
        spdlog::error("Incomplete disp32, remaining={}", remaining);
        instr.instType = InstructionType::Unknown;
        return;
      }
      instr.operands[1].memory.displacement =
          *reinterpret_cast<const int32_t *>(buffer);
      buffer += 4;
      remaining -= 4;
      spdlog::trace("ModRM disp32: displacement=0x{:x}",
                    instr.operands[1].memory.displacement);
    }
  }

  if (instr.isVex && instr.operandCount < 3) {
    instr.operandCount = 3;
    instr.operands[2].type = DecodedInstruction::Operand::Type::XMM;
    instr.operands[2].reg = static_cast<Register>(
        static_cast<size_t>(Register::XMM0) + (15 - instr.vex.vvvv()));
    instr.operands[2].size = instr.vex.L() ? 256 : 128;
    spdlog::trace("VEX third operand: type=XMM, reg={}, size={}",
                  static_cast<int>(instr.operands[2].reg),
                  instr.operands[2].size);
  }
}

void InstructionDecoder::ParseOpcode(const uint8_t *&buffer, size_t &remaining,
                                     DecodedInstruction &instr) {
  if (remaining == 0) {
    spdlog::error("No opcode bytes available");
    instr.instType = InstructionType::Unknown;
    return;
  }

  uint8_t primaryOpcode = *buffer;
  const uint8_t *opcodeStartBuffer = buffer;
  bool isTwoByteOpcode = (primaryOpcode == 0x0F);

  if (isTwoByteOpcode) {
    buffer++;
    remaining--;
    if (remaining == 0) {
      spdlog::error("Incomplete two-byte opcode");
      instr.instType = InstructionType::Unknown;
      return;
    }
    instr.opcode = *buffer;
    buffer++;
    remaining--;
    spdlog::trace("Parsing two-byte opcode 0x0F 0x{:x}", instr.opcode);
  } else {
    instr.opcode = primaryOpcode;
    buffer++;
    remaining--;
    spdlog::trace("Parsing single-byte opcode 0x{:x}", instr.opcode);
  }

  instr.operands[0].size =
      instr.HasRexW() ? 64 : (instr.operandSizeOverride ? 16 : 32);
  instr.operands[1].size =
      instr.HasRexW() ? 64 : (instr.operandSizeOverride ? 16 : 32);

  if (!isTwoByteOpcode) {
    auto it = singleByteOpcodes.find(instr.opcode);
    if (it != singleByteOpcodes.end()) {
      instr.instType = it->second.type;
      instr.operandCount = it->second.operandCount;
      for (uint8_t i = 0; i < instr.operandCount; ++i) {
        instr.operands[i].type = it->second.operandTypes[i];
      }
      if (instr.opcode == 0xC2) {
        if (remaining < 2) {
          spdlog::error("Incomplete RET imm16");
          instr.instType = InstructionType::Unknown;
          return;
        }
        instr.operands[0].immediate =
            *reinterpret_cast<const uint16_t *>(buffer);
        instr.operands[0].size = 16;
        buffer += 2;
        remaining -= 2;
        spdlog::trace("Parsed RET imm16: immediate=0x{:x}",
                      instr.operands[0].immediate);
      } else if (instr.opcode == 0xE8 || instr.opcode == 0xE9) {
        if (remaining < 4) {
          spdlog::error("Incomplete CALL/JMP rel32");
          instr.instType = InstructionType::Unknown;
          return;
        }
        instr.operands[0].immediate =
            *reinterpret_cast<const int32_t *>(buffer);
        instr.operands[0].size = 32;
        buffer += 4;
        remaining -= 4;
        spdlog::trace("Parsed {} rel32: immediate=0x{:x}",
                      instr.opcode == 0xE8 ? "CALL" : "JMP",
                      instr.operands[0].immediate);
      } else if (instr.opcode == 0xCC) {
        instr.operands[0].immediate = 3;
        instr.operands[0].size = 8;
        spdlog::trace("Parsed INT 3");
      } else if (instr.opcode == 0xCD) {
        if (remaining < 1) {
          spdlog::error("Incomplete INT imm8");
          instr.instType = InstructionType::Unknown;
          return;
        }
        instr.operands[0].immediate = *buffer;
        instr.operands[0].size = 8;
        buffer++;
        remaining--;
        spdlog::trace("Parsed INT imm8: immediate=0x{:x}",
                      instr.operands[0].immediate);
      } else if (instr.opcode == 0x6A) {
        if (remaining < 1) {
          spdlog::error("Incomplete PUSH imm8");
          instr.instType = InstructionType::Unknown;
          return;
        }
        instr.operands[0].immediate = static_cast<int8_t>(*buffer);
        instr.operands[0].size = 8;
        buffer++;
        remaining--;
        spdlog::trace("Parsed PUSH imm8: immediate=0x{:x}",
                      instr.operands[0].immediate);
      } else if (instr.opcode == 0x68) {
        if (remaining < 4) {
          spdlog::error("Incomplete PUSH imm32");
          instr.instType = InstructionType::Unknown;
          return;
        }
        instr.operands[0].immediate =
            *reinterpret_cast<const int32_t *>(buffer);
        instr.operands[0].size = 32;
        buffer += 4;
        remaining -= 4;
        spdlog::trace("Parsed PUSH imm32: immediate=0x{:x}",
                      instr.operands[0].immediate);
      } else if (instr.opcode == 0x01 || instr.opcode == 0x03 ||
                 instr.opcode == 0x29 || instr.opcode == 0x2B ||
                 instr.opcode == 0x3B || instr.opcode == 0x85 ||
                 instr.opcode == 0x8B || instr.opcode == 0x89 ||
                 instr.opcode == 0x8D) {
        if (remaining < 1) {
          spdlog::error("Incomplete ModRM for instruction 0x{:x}",
                        instr.opcode);
          instr.instType = InstructionType::Unknown;
          return;
        }
        ParseModRM(buffer, remaining, instr, *buffer);
        if (instr.opcode == 0x01 || instr.opcode == 0x29 ||
            instr.opcode == 0x3B || instr.opcode == 0x89) {
          std::swap(instr.operands[0], instr.operands[1]);
          spdlog::trace("Swapped operands for instruction 0x{:x}",
                        instr.opcode);
        }
      }
      if (instr.opcode == 0xA5 && instr.HasRexW())
        instr.instType = InstructionType::Movsq;
      else if (instr.opcode == 0xAB && instr.HasRexW())
        instr.instType = InstructionType::Stosq;
      else if (instr.opcode == 0xA7 && instr.HasRexW())
        instr.instType = InstructionType::Cmpsq;
      return;
    }

    // Check for group opcodes that need ModR/M extension
    if (instr.opcode == 0x80 || instr.opcode == 0x81 || instr.opcode == 0x83 ||
        instr.opcode == 0xC0 || instr.opcode == 0xC1 || instr.opcode == 0xD0 ||
        instr.opcode == 0xD1 || instr.opcode == 0xD2 || instr.opcode == 0xD3 ||
        instr.opcode == 0xF6 || instr.opcode == 0xF7 || instr.opcode == 0xFE ||
        instr.opcode == 0xFF) {

      if (remaining < 1) {
        spdlog::error("Incomplete ModRM for group opcode 0x{:x}", instr.opcode);
        instr.instType = InstructionType::Unknown;
        return;
      }

      uint8_t modrmByte = *buffer;
      uint8_t extension = (modrmByte >> 3) & 0x7;
      uint32_t groupKey =
          (static_cast<uint32_t>(instr.opcode) << 8) | extension;

      auto groupIt = groupOpcodes.find(groupKey);
      if (groupIt != groupOpcodes.end()) {
        instr.instType = groupIt->second.type;
        instr.operandCount = groupIt->second.operandCount;
        for (uint8_t i = 0; i < instr.operandCount; ++i) {
          instr.operands[i].type = groupIt->second.operandTypes[i];
        }

        // Parse the ModR/M byte
        ParseModRM(buffer, remaining, instr, modrmByte);

        // Handle second operand based on opcode type
        if (instr.opcode == 0x80 || instr.opcode == 0x81 ||
            instr.opcode == 0x83) {
          // Group 1: Immediate arithmetic - parse immediate
          uint8_t immSize = (instr.opcode == 0x80) ? 1
                            : (instr.opcode == 0x83)
                                ? 1
                                : (instr.operandSizeOverride ? 2 : 4);
          if (remaining < immSize) {
            spdlog::error("Incomplete immediate for group 1 opcode 0x{:x}",
                          instr.opcode);
            instr.instType = InstructionType::Unknown;
            return;
          }
          if (immSize == 1) {
            instr.operands[1].immediate = static_cast<int8_t>(*buffer);
            instr.operands[1].size = 8;
            buffer++;
            remaining--;
          } else if (immSize == 2) {
            instr.operands[1].immediate =
                *reinterpret_cast<const int16_t *>(buffer);
            instr.operands[1].size = 16;
            buffer += 2;
            remaining -= 2;
          } else {
            instr.operands[1].immediate =
                *reinterpret_cast<const int32_t *>(buffer);
            instr.operands[1].size = 32;
            buffer += 4;
            remaining -= 4;
          }
          spdlog::trace("Parsed Group 1 instruction: opcode=0x{:x}, "
                        "extension={}, immediate=0x{:x}",
                        instr.opcode, extension, instr.operands[1].immediate);
        } else if (instr.opcode == 0xC0 || instr.opcode == 0xC1) {
          // Group 2: Shift/rotate with immediate
          if (remaining < 1) {
            spdlog::error("Incomplete immediate for group 2 opcode 0x{:x}",
                          instr.opcode);
            instr.instType = InstructionType::Unknown;
            return;
          }
          instr.operands[1].immediate = *buffer;
          instr.operands[1].size = 8;
          buffer++;
          remaining--;
          spdlog::trace("Parsed Group 2 shift/rotate with immediate: "
                        "opcode=0x{:x}, extension={}, immediate={}",
                        instr.opcode, extension, instr.operands[1].immediate);
        } else if (instr.opcode == 0xD0 || instr.opcode == 0xD1) {
          // Group 2: Shift/rotate by 1
          instr.operands[1].immediate = 1;
          instr.operands[1].size = 8;
          spdlog::trace(
              "Parsed Group 2 shift/rotate by 1: opcode=0x{:x}, extension={}",
              instr.opcode, extension);
        } else if (instr.opcode == 0xD2 || instr.opcode == 0xD3) {
          // Group 2: Shift/rotate by CL register
          instr.operands[1].reg = Register::CL;
          instr.operands[1].size = 8;
          spdlog::trace(
              "Parsed Group 2 shift/rotate by CL: opcode=0x{:x}, extension={}",
              instr.opcode, extension);
        } else if (instr.opcode == 0xF6 || instr.opcode == 0xF7) {
          // Group 3: TEST needs immediate, others are single operand
          if (extension == 0) { // TEST
            uint8_t immSize = (instr.opcode == 0xF6)
                                  ? 1
                                  : (instr.operandSizeOverride ? 2 : 4);
            if (remaining < immSize) {
              spdlog::error("Incomplete immediate for TEST 0x{:x}",
                            instr.opcode);
              instr.instType = InstructionType::Unknown;
              return;
            }
            if (immSize == 1) {
              instr.operands[1].immediate = *buffer;
              instr.operands[1].size = 8;
              buffer++;
              remaining--;
            } else if (immSize == 2) {
              instr.operands[1].immediate =
                  *reinterpret_cast<const uint16_t *>(buffer);
              instr.operands[1].size = 16;
              buffer += 2;
              remaining -= 2;
            } else {
              instr.operands[1].immediate =
                  *reinterpret_cast<const uint32_t *>(buffer);
              instr.operands[1].size = 32;
              buffer += 4;
              remaining -= 4;
            }
            spdlog::trace(
                "Parsed Group 3 TEST: opcode=0x{:x}, immediate=0x{:x}",
                instr.opcode, instr.operands[1].immediate);
          } else {
            // Single operand instructions
            instr.operands[0] = instr.operands[1];
            instr.operandCount = 1;
            spdlog::trace(
                "Parsed Group 3 single operand: opcode=0x{:x}, extension={}",
                instr.opcode, extension);
          }
        }
        return;
      } else {
        spdlog::error("Unknown group opcode: 0x{:x} extension: {}",
                      instr.opcode, extension);
        instr.instType = InstructionType::Unknown;
        return;
      }
    }

    if (instr.opcode >= 0x50 && instr.opcode <= 0x57) {
      instr.instType = InstructionType::Push;
      instr.operandCount = 1;
      instr.operands[0].type = DecodedInstruction::Operand::Type::REGISTER;
      instr.operands[0].reg =
          ResolveRegister(instr.opcode & 0x7, (instr.rex & REX_B));
      instr.operands[0].size = 64;
      spdlog::trace("Parsed PUSH register: reg={}",
                    static_cast<int>(instr.operands[0].reg));
      return;
    }

    if (instr.opcode >= 0x58 && instr.opcode <= 0x5F) {
      instr.instType = InstructionType::Pop;
      instr.operandCount = 1;
      instr.operands[0].type = DecodedInstruction::Operand::Type::REGISTER;
      instr.operands[0].reg =
          ResolveRegister(instr.opcode & 0x7, (instr.rex & REX_B));
      instr.operands[0].size = 64;
      spdlog::trace("Parsed POP register: reg={}",
                    static_cast<int>(instr.operands[0].reg));
      return;
    }

    if (instr.opcode >= 0xB8 && instr.opcode <= 0xBF) {
      instr.instType = InstructionType::Mov;
      instr.operandCount = 2;
      instr.operands[0].type = DecodedInstruction::Operand::Type::REGISTER;
      instr.operands[0].reg =
          ResolveRegister(instr.opcode & 0x7, (instr.rex & REX_B));
      instr.operands[1].type = DecodedInstruction::Operand::Type::IMMEDIATE;
      if (instr.HasRexW()) {
        if (remaining < 8) {
          spdlog::error("Incomplete MOV r64, imm64");
          instr.instType = InstructionType::Unknown;
          return;
        }
        instr.operands[0].size = 64;
        instr.operands[1].size = 64;
        instr.operands[1].immediate =
            *reinterpret_cast<const uint64_t *>(buffer);
        buffer += 8;
        remaining -= 8;
        spdlog::trace("Parsed MOV r64, imm64: reg={}, immediate=0x{:x}",
                      static_cast<int>(instr.operands[0].reg),
                      instr.operands[1].immediate);
      } else {
        if (remaining < 4) {
          spdlog::error("Incomplete MOV r32, imm32");
          instr.instType = InstructionType::Unknown;
          return;
        }
        instr.operands[0].size = 32;
        instr.operands[1].size = 32;
        instr.operands[1].immediate =
            static_cast<uint64_t>(*reinterpret_cast<const uint32_t *>(buffer));
        buffer += 4;
        remaining -= 4;
        spdlog::trace("Parsed MOV r32, imm32: reg={}, immediate=0x{:x}",
                      static_cast<int>(instr.operands[0].reg),
                      instr.operands[1].immediate);
      }
      return;
    }

    if (instr.opcode >= 0x70 && instr.opcode <= 0x7F) {
      instr.instType = InstructionType::Jcc;
      instr.operandCount = 1;
      instr.operands[0].type = DecodedInstruction::Operand::Type::IMMEDIATE;
      if (remaining < 1) {
        spdlog::error("Incomplete Jcc rel8");
        instr.instType = InstructionType::Unknown;
        return;
      }
      instr.operands[0].immediate = static_cast<int8_t>(*buffer);
      instr.operands[0].size = 8;
      instr.conditionCode = instr.opcode & 0x0F;
      buffer++;
      remaining--;
      spdlog::trace("Parsed Jcc rel8: condition=0x{:x}, immediate=0x{:x}",
                    instr.conditionCode, instr.operands[0].immediate);
      return;
    }
  } else {
    auto it = twoByteOpcodes.find(instr.opcode);
    if (it != twoByteOpcodes.end()) {
      instr.instType = it->second.type;
      instr.operandCount = it->second.operandCount;
      for (uint8_t i = 0; i < instr.operandCount; ++i) {
        instr.operands[i].type = it->second.operandTypes[i];
      }
      if (instr.opcode >= 0x80 && instr.opcode <= 0x8F) {
        if (remaining < 4) {
          spdlog::error("Incomplete Jcc rel32");
          instr.instType = InstructionType::Unknown;
          return;
        }
        instr.operands[0].immediate =
            *reinterpret_cast<const int32_t *>(buffer);
        instr.operands[0].size = 32;
        instr.conditionCode = instr.opcode & 0x0F;
        buffer += 4;
        remaining -= 4;
        spdlog::trace("Parsed Jcc rel32: condition=0x{:x}, immediate=0x{:x}",
                      instr.conditionCode, instr.operands[0].immediate);
      } else if (instr.opcode == 0x01) {
        if (remaining < 1) {
          spdlog::error("Incomplete ModRM for Group 7");
          instr.instType = InstructionType::Unknown;
          return;
        }
        uint8_t modrmByte = *buffer;
        uint8_t extension = (modrmByte >> 3) & 0x7;
        if (extension == 3) {
          if ((modrmByte >> 6) == 0b11) {
            spdlog::error("Invalid ModRM for LIDT (register operand)");
            instr.instType = InstructionType::Unknown;
            return;
          }
          ParseModRM(buffer, remaining, instr, modrmByte);
          if (instr.instType == InstructionType::Unknown)
            return;
          instr.operands[0] = instr.operands[1];
          instr.operandCount = 1;
          instr.operands[0].size = 80;
          spdlog::trace("Parsed LIDT: memory operand, size=80");
        } else {
          spdlog::error("Unknown Group 7 extension: {}", extension);
          instr.instType = InstructionType::Unknown;
          return;
        }
      } else if ((instr.opcode >= 0x54 && instr.opcode <= 0x59) &&
                 instr.isVex) {
        ParseModRM(buffer, remaining, instr, *buffer);
        spdlog::trace("Parsed {}: operands={}",
                      magic_enum::enum_name(instr.instType),
                      instr.operandCount);
      } else if (instr.opcode >= 0x40 && instr.opcode <= 0x4F) {
        ParseModRM(buffer, remaining, instr, *buffer);
        instr.conditionCode = instr.opcode & 0x0F;
        spdlog::trace("Parsed CMOVCC: condition=0x{:x}, operands={}",
                      instr.conditionCode, instr.operandCount);
      } else if (instr.opcode == 0x05 || instr.opcode == 0x34) {
        spdlog::trace("Parsed {}: no operands",
                      magic_enum::enum_name(instr.instType));
      }
      return;
    }
  }

  if (remaining < 1) {
    spdlog::error("Incomplete opcode requiring ModRM");
    buffer = opcodeStartBuffer;
    remaining += (buffer - opcodeStartBuffer);
    instr.instType = InstructionType::Unknown;
    return;
  }
  uint8_t modrmByte = *buffer;

  if (!isTwoByteOpcode) {
    switch (primaryOpcode) {
    case 0xFF: {
      uint8_t reg = (modrmByte >> 3) & 0x7;
      if (reg == 2 || reg == 4) {
        instr.instType = InstructionType::Call;
        instr.operandCount = 1;
        ParseModRM(buffer, remaining, instr, modrmByte);
        instr.operands[0] = instr.operands[1];
        instr.operands[0].size = instr.HasRexW() ? 64 : 32;
        spdlog::trace("Parsed CALL indirect: operand size={}",
                      instr.operands[0].size);
      } else if (reg == 6) {
        instr.instType = InstructionType::Push;
        instr.operandCount = 1;
        ParseModRM(buffer, remaining, instr, modrmByte);
        instr.operands[0] = instr.operands[1];
        instr.operands[0].size = 64;
        spdlog::trace("Parsed PUSH indirect: operand size=64");
      } else if (reg == 0 || reg == 1) {
        instr.instType = InstructionType::Jump;
        instr.operandCount = 1;
        ParseModRM(buffer, remaining, instr, modrmByte);
        instr.operands[0] = instr.operands[1];
        instr.operands[0].size = instr.HasRexW() ? 64 : 32;
        spdlog::trace("Parsed JMP indirect: operand size={}",
                      instr.operands[0].size);
      } else {
        spdlog::error("Unknown FF group extension: {}", reg);
        instr.instType = InstructionType::Unknown;
      }
      return;
    }
    case 0x8F: {
      uint8_t reg = (modrmByte >> 3) & 0x7;
      if (reg == 0) {
        instr.instType = InstructionType::Pop;
        instr.operandCount = 1;
        ParseModRM(buffer, remaining, instr, modrmByte);
        instr.operands[0] = instr.operands[1];
        instr.operands[0].size = 64;
        spdlog::trace("Parsed POP indirect: operand size=64");
      } else {
        spdlog::error("Unknown 8F group extension: {}", reg);
        instr.instType = InstructionType::Unknown;
      }
      return;
    }
    default:
      spdlog::error("Unhandled single-byte opcode: 0x{:x}", primaryOpcode);
      instr.instType = InstructionType::Unknown;
      return;
    }
  } else {
    spdlog::error("Unhandled two-byte opcode: 0x0F 0x{:x}", instr.opcode);
    instr.instType = InstructionType::Unknown;
    return;
  }
}

} // namespace x86_64