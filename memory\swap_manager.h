// Copyright 2025 <Copyright Owner>

#pragma once

#include <chrono>
#include <cstdint>
#include <fstream>
#include <istream>
#include <ostream>
#include <shared_mutex>
#include <string>
#include <unordered_map>
#include <vector>


namespace ps4 {

/**
 * @brief Statistics for swap operations.
 */
struct SwapStats {
  uint64_t pagesSwapped = 0;      ///< Number of pages swapped
  uint64_t totalBytesSwapped = 0; ///< Total bytes swapped
  uint64_t swapInCount = 0;       ///< Number of swap-in operations
  uint64_t swapOutCount = 0;      ///< Number of swap-out operations
  uint64_t totalLatencyUs = 0;    ///< Total swap latency (microseconds)
  uint64_t cacheHits = 0;         ///< Number of cache hits
  uint64_t cacheMisses = 0;       ///< Number of cache misses
};

/**
 * @brief Manages virtual memory swapping to disk for the PS4 emulator.
 * @details Handles page swapping to and from disk, with prioritized page
 * selection, thread-safe operations, and detailed metrics for performance
 * analysis.
 */
class SwapManager {
public:
  /**
   * @brief Structure representing a swapped page entry.
   */
  struct SwapEntry {
    uint64_t virtAddr;    ///< Virtual address of the swapped page
    uint64_t processId;   ///< Process ID
    uint64_t swapOffset;  ///< Offset in swap file
    uint64_t accessCount; ///< Access count before swapping
    std::chrono::steady_clock::time_point lastAccessTime; ///< Last access time
  };

  /**
   * @brief Constructs a SwapManager instance.
   * @param swapFilePath Path to the swap file.
   * @param maxSwapSize Maximum swap file size (bytes).
   */
  explicit SwapManager(const std::string &swapFilePath, uint64_t maxSwapSize);

  /**
   * @brief Destructor, cleaning up resources.
   */
  ~SwapManager();

  /**
   * @brief Initializes the swap manager.
   * @return True on success, false otherwise.
   */
  bool Initialize();

  /**
   * @brief Shuts down the swap manager.
   */
  void Shutdown();

  /**
   * @brief Swaps a page to disk.
   * @param virtAddr Virtual address of the page.
   * @param processId Process ID.
   * @param pageData Page data to write.
   * @return True on success, false otherwise.
   */
  bool SwapOut(uint64_t virtAddr, uint64_t processId, const uint8_t *pageData);

  /**
   * @brief Swaps a page back from disk.
   * @param virtAddr Virtual address of the page.
   * @param processId Process ID.
   * @param pageData Buffer for read data.
   * @return True on success, false otherwise.
   */
  bool SwapIn(uint64_t virtAddr, uint64_t processId, uint8_t *pageData);

  /**
   * @brief Checks if a page is swapped out.
   * @param virtAddr Virtual address.
   * @param processId Process ID.
   * @return True if swapped, false otherwise.
   */
  bool IsPageSwapped(uint64_t virtAddr, uint64_t processId) const;

  /**
   * @brief Gets swap usage statistics.
   * @return Swap statistics.
   */
  SwapStats GetSwapStats() const;

  /**
   * @brief Saves the swap manager state to a stream.
   * @param out Output stream.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the swap manager state from a stream.
   * @param in Input stream.
   */
  void LoadState(std::istream &in);

private:
  /**
   * @brief Finds a free offset in the swap file.
   * @return Offset, or 0 if none available.
   */
  uint64_t FindFreeSwapOffset() const;

  std::string swapFilePath_;           ///< Path to swap file
  uint64_t maxSwapSize_;               ///< Maximum swap file size
  std::fstream swapFile_;              ///< Swap file stream
  mutable std::shared_mutex mutex_;    ///< Thread safety mutex
  std::vector<SwapEntry> swapEntries_; ///< Swapped page entries
  uint64_t currentSwapSize_;           ///< Current swap file size
  mutable SwapStats stats_; ///< Swap statistics (mutable for const methods)
};

} // namespace ps4