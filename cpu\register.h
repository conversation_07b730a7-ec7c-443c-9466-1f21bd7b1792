#pragma once

#include <cstddef>
#include <cstdint>

namespace x86_64 {

/**
 * @brief x86_64 register identifiers.
 */
enum class Register {
  NONE,
  RAX,
  RBX,
  RCX,
  RDX,
  RSI,
  RDI,
  RSP,
  RBP,
  R8,
  R9,
  R10,
  R11,
  R12,
  R13,
  R14,
  R15,
  RIP,
  EAX,
  EBX,
  ECX,
  EDX,
  ESI,
  EDI,
  ESP,
  EBP,
  // 8-bit registers
  AL,
  BL,
  CL,
  DL,
  AH,
  BH,
  CH,
  DH,
  // 16-bit registers
  AX,
  BX,
  CX,
  DX,
  SI,
  DI,
  SP,
  BP,
  XMM0,
  XMM1,
  XMM2,
  XMM3,
  XMM4,
  XMM5,
  XMM6,
  XMM7,
  XMM8,
  XMM9,
  XMM10,
  XMM11,
  XMM12,
  XMM13,
  XMM14,
  XMM15,
  COUNT
};

constexpr size_t REGISTER_COUNT = static_cast<size_t>(Register::COUNT);

/**
 * @brief Validates register index.
 * @param reg Register enum.
 * @return True if valid.
 */
inline bool IsValidRegister(Register reg) {
  return static_cast<size_t>(reg) < REGISTER_COUNT;
}

} // namespace x86_64