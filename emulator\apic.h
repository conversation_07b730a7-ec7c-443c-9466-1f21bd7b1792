#pragma once

#include <array>
#include <cstdint>
#include <fstream>
#include <mutex>
#include <ostream>
#include <unordered_map>
#include <vector>

namespace x86_64 {

/**
 * @brief Local APIC register indices.
 */
enum class LAPICRegister : uint32_t {
  ID = 0x020 / 4,                  ///< APIC ID register
  VERSION = 0x030 / 4,             ///< APIC version register
  TPR = 0x080 / 4,                 ///< Task Priority Register
  APR = 0x090 / 4,                 ///< Arbitration Priority Register
  PPR = 0x0A0 / 4,                 ///< Processor Priority Register
  EOI = 0x0B0 / 4,                 ///< End of Interrupt register
  RRD = 0x0C0 / 4,                 ///< Remote Read register
  LDR = 0x0D0 / 4,                 ///< Logical Destination Register
  DFR = 0x0E0 / 4,                 ///< Destination Format Register
  SPURIOUS = 0x0F0 / 4,            ///< Spurious Interrupt Vector Register
  ESR = 0x280 / 4,                 ///< Error Status Register
  ICR_LOW = 0x300 / 4,             ///< Interrupt Command Register (low)
  ICR_HIGH = 0x310 / 4,            ///< Interrupt Command Register (high)
  TIMER = 0x320 / 4,               ///< Timer LVT register
  THERMAL = 0x330 / 4,             ///< Thermal Sensor LVT register
  PERFORMANCE = 0x340 / 4,         ///< Performance Counter LVT register
  LINT0 = 0x350 / 4,               ///< Local Interrupt 0 LVT register
  LINT1 = 0x360 / 4,               ///< Local Interrupt 1 LVT register
  ERROR = 0x370 / 4,               ///< Error LVT register
  TIMER_INITIAL_COUNT = 0x380 / 4, ///< Timer initial count register
  TIMER_CURRENT_COUNT = 0x390 / 4, ///< Timer current count register
  TIMER_DIVIDE = 0x3E0 / 4         ///< Timer divide configuration register
};

/**
 * @brief Inter-Processor Interrupt (IPI) types.
 */
enum class IPIType : uint32_t {
  FIXED = 0,           ///< Fixed interrupt
  LOWEST_PRIORITY = 1, ///< Lowest priority interrupt
  SMI = 2,             ///< System Management Interrupt
  NMI = 4,             ///< Non-Maskable Interrupt
  INIT = 5,            ///< Initialization interrupt
  STARTUP = 6,         ///< Startup interrupt
  EXTINT = 7           ///< External interrupt
};

/**
 * @brief Statistics for APIC operations.
 */
struct APICStats {
  uint64_t operationCount = 0; ///< Total operations performed
  uint64_t totalLatencyUs = 0; ///< Total latency in microseconds
  std::unordered_map<uint32_t, uint64_t>
      interruptCounts;                                 ///< Per-interrupt counts
  std::unordered_map<IPIType, uint64_t> ipiTypeCounts; ///< Per-IPI type counts
  uint64_t timerInterrupts = 0; ///< Number of timer interrupts
};

/**
 * @brief Emulates the Local Advanced Programmable Interrupt Controller (APIC).
 */
class APIC {
public:
  /**
   * @brief Constructs an APIC instance for a specific core.
   * @param coreId The ID of the associated CPU core.
   */
  explicit APIC(uint32_t coreId);

  /**
   * @brief Destructs the APIC, ensuring proper shutdown.
   */
  ~APIC() noexcept;

  /**
   * @brief Initializes the APIC.
   * @return True on success, false on failure.
   */
  bool Initialize();

  /**
   * @brief Shuts down the APIC, disabling interrupt processing.
   */
  void Shutdown();

  /**
   * @brief Reads an APIC register.
   * @param reg The register to read.
   * @return The register value.
   */
  uint32_t ReadRegister(LAPICRegister reg);

  /**
   * @brief Writes to an APIC register.
   * @param reg The register to write.
   * @param value The value to write.
   */
  void WriteRegister(LAPICRegister reg, uint32_t value);

  /**
   * @brief Sends an Inter-Processor Interrupt (IPI).
   * @param destination The destination core ID or logical ID.
   * @param vector The interrupt vector.
   * @param type The IPI type.
   */
  void SendIPI(uint32_t destination, uint32_t vector, IPIType type);

  /**
   * @brief Handles an interrupt received by the APIC.
   * @param vector The interrupt vector.
   */
  void HandleInterrupt(uint32_t vector);

  /**
   * @brief Updates the APIC timer state.
   * @param cycles Number of CPU cycles to advance.
   */
  void UpdateTimer(uint64_t cycles);

  /**
   * @brief Saves the APIC state to a stream.
   * @param out The output stream.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the APIC state from a stream.
   * @param in The input stream.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Retrieves APIC operation statistics.
   * @return The APIC statistics structure.
   */
  APICStats GetStats() const;

private:
  /**
   * @brief Handles writes to the Interrupt Command Register (ICR).
   * @param icrLow The low 32 bits of the ICR.
   * @param icrHigh The high 32 bits of the ICR.
   */
  void HandleICRWrite(uint32_t icrLow, uint32_t icrHigh);

  /**
   * @brief Finds the core corresponding to a logical destination ID.
   * @param logicalId The logical destination ID.
   * @return The core ID, or UINT32_MAX if not found.
   */
  uint32_t FindLogicalDestination(uint32_t logicalId);

  /**
   * @brief Finds the core with the lowest priority for IPI delivery.
   * @return The core ID, or UINT32_MAX if none available.
   */
  uint32_t FindLowestPriorityCore();

  /**
   * @brief Counts trailing zeros in a 32-bit value.
   * @param value The input value.
   * @return The number of trailing zeros.
   */
  uint32_t CountTrailingZeros(uint32_t value);

  /**
   * @brief Counts leading zeros in a 32-bit value.
   * @param value The input value.
   * @return The number of leading zeros.
   */
  uint32_t CountLeadingZeros(uint32_t value);

  uint32_t m_coreId = 0;      ///< Core ID
  uint64_t m_baseAddress = 0; ///< APIC base address
  bool m_enabled = false;     ///< APIC enabled status
  std::array<uint32_t, 0x400 / 4> m_lapicRegisters = {}; ///< LAPIC registers
  mutable std::mutex m_apicMutex; ///< Thread safety mutex
  APICStats m_stats;              ///< Operation statistics
  uint64_t m_timerCycles = 0;     ///< Timer cycle counter
};

} // namespace x86_64