<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_MUTEXATTR_INIT(3) manual page</TITLE>
	<STYLE TYPE="text/css">
	<!--
		H4.cjk { font-family: "AR PL UMing CN" }
		H4.ctl { font-family: "Lohit Devanagari" }
		H2.cjk { font-family: "AR PL UMing CN" }
		H2.ctl { font-family: "Lohit Devanagari" }
	-->
	</STYLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4 CLASS="western">POSIX Threads for Windows â€“ REFERENCE -
<A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2 CLASS="western"><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_mutexattr_init, pthread_mutexattr_destroy,
pthread_mutexattr_settype, pthread_mutexattr_gettype - mutex creation
attributes 
</P>
<H2 CLASS="western"><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> 
</P>
<P><B>int pthread_mutexattr_init(pthread_mutexattr_t *</B><I>attr</I><B>);</B>
</P>
<P><B>int pthread_mutexattr_destroy(pthread_mutexattr_t *</B><I>attr</I><B>);</B>
</P>
<P><B>int pthread_mutexattr_settype(pthread_mutexattr_t *</B><I>attr</I><B>,
int </B><I>type</I><B>);</B> 
</P>
<P><B>int pthread_mutexattr_gettype(const pthread_mutexattr_t *</B><I>attr</I><B>,
int *</B><I>type</I><B>);</B> 
</P>
<P><B>int pthread_mutexattr_setkind_np(pthread_mutexattr_t *</B><I>attr</I><B>,
int </B><I>type</I><B>);</B> 
</P>
<P><B>int pthread_mutexattr_getkind_np(const pthread_mutexattr_t
*</B><I>attr</I><B>, int *</B><I>type</I><B>);</B> 
</P>
<P><B>int pthread_mutexattr_setrobust(pthread_mutexattr_t *</B><I>attr</I><B>,
int</B><SPAN STYLE="font-weight: normal"> </SPAN><I><SPAN STYLE="font-weight: normal">robust</SPAN></I><B>);</B>
</P>
<P><B>int pthread_mutexattr_getrobust(pthread_mutexattr_t *</B><I>attr</I><B>,
int</B><SPAN STYLE="font-weight: normal"> </SPAN><B>*</B><I><SPAN STYLE="font-weight: normal">robust</SPAN></I><B>);</B>
</P>
<H2 CLASS="western"><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>Mutex attributes can be specified at mutex creation time, by
passing a mutex attribute object as second argument to
<A HREF="pthread_mutex_init.html"><B>pthread_mutex_init</B>(3)</A> .
Passing <B>NULL</B> is equivalent to passing a mutex attribute object
with all attributes set to their default values. 
</P>
<P><B>pthread_mutexattr_init</B> initializes the mutex attribute
object <I>attr</I> and fills it with default values for the
attributes. 
</P>
<P><B>pthread_mutexattr_destroy</B> destroys a mutex attribute
object, which must not be reused until it is reinitialized.</P>
<P><B>pthread_mutexattr_settype</B> sets the mutex type attribute in
<I>attr</I> to the value specified by <I>type</I>. 
</P>
<P><B>pthread_mutexattr_gettype</B> retrieves the current value of
the mutex kind attribute in <I>attr</I> and stores it in the location
pointed to by <I>type</I>. 
</P>
<P><B>PThreads4W</B> also recognises the following equivalent
functions that are used in Linux:</P>
<P><B>pthread_mutexattr_setkind_np</B> is an alias for
<B>pthread_mutexattr_settype</B>. 
</P>
<P STYLE="font-weight: normal"><B>pthread_mutexattr_getkind_np</B> is
an alias for <B>pthread_mutexattr_gettype</B>. 
</P>
<P>The following mutex types are supported:</P>
<P STYLE="margin-left: 0.79in"><B>PTHREAD_MUTEX_NORMAL</B> - for
â€˜â€˜fastâ€™â€™ mutexes.</P>
<P STYLE="margin-left: 0.79in"><B>PTHREAD_MUTEX_RECURSIVE</B> - for
â€˜â€˜recursiveâ€™â€™ mutexes.</P>
<P STYLE="margin-left: 0.79in"><B>PTHREAD_MUTEX_ERRORCHECK</B> - for
â€˜â€˜error checkingâ€™â€™ mutexes.</P>
<P>The mutex type determines what happens if a thread attempts to
lock a mutex it already owns with <A HREF="pthread_mutex_lock.html"><B>pthread_mutex_lock</B>(3)</A>
. If the mutex is of the â€œnormalâ€� or â€œfastâ€� type,
<A HREF="pthread_mutex_lock.html"><B>pthread_mutex_lock</B>(3)</A>
simply suspends the calling thread forever. If the mutex is of the
â€˜â€˜error checkingâ€™â€™ type, <A HREF="pthread_mutex_lock.html"><B>pthread_mutex_lock</B>(3)</A>
returns immediately with the error code <B>EDEADLK</B>. If the mutex
is of the â€˜â€˜recursiveâ€™â€™ type, the call to
<A HREF="pthread_mutex_lock.html"><B>pthread_mutex_lock</B>(3)</A>
returns immediately with a success return code. The number of times
the thread owning the mutex has locked it is recorded in the mutex.
The owning thread must call <A HREF="pthread_mutex_unlock.html"><B>pthread_mutex_unlock</B>(3)</A>
the same number of times before the mutex returns to the unlocked
state. 
</P>
<P>The default mutex type is <B>PTHREAD_MUTEX_NORMAL</B></P>
<P><B>PThreads4W</B> also recognises the following equivalent types
that are used by Linux:</P>
<P STYLE="margin-left: 0.79in; font-weight: normal"><B>PTHREAD_MUTEX_FAST_NP</B>
â€“ equivalent to <B>PTHREAD_MUTEX_NORMAL</B></P>
<P STYLE="margin-left: 0.79in"><B>PTHREAD_MUTEX_RECURSIVE_NP</B></P>
<P STYLE="margin-left: 0.79in"><B>PTHREAD_MUTEX_ERRORCHECK_NP</B></P>
<P><B>pthread_mutexattr_setrobust</B><SPAN STYLE="font-weight: normal">
sets the robustness attribute to the value given by </SPAN><I><SPAN STYLE="font-weight: normal">robust</SPAN></I><SPAN STYLE="font-weight: normal">.</SPAN></P>
<P><B>pthread_mutexattr_getrobust</B><SPAN STYLE="font-weight: normal">
returns the current robustness value to the location given by
*</SPAN><I><SPAN STYLE="font-weight: normal">robust</SPAN></I><SPAN STYLE="font-weight: normal">.</SPAN></P>
<P><SPAN STYLE="font-weight: normal">The possible values for </SPAN><I><SPAN STYLE="font-weight: normal">robust</SPAN></I><SPAN STYLE="font-weight: normal">
are:</SPAN></P>
<P STYLE="margin-left: 0.79in"><B>PTHREAD_MUTEX_STALLED</B><SPAN STYLE="font-weight: normal">
- when the owner of the mutex terminates without unlocking the mutex,
all subsequent calls
 to pthread_mutex_*lock() are blocked from
progress in an unspecified manner.</SPAN></P>
<P STYLE="margin-left: 0.79in"><B>PTHREAD_MUTEX_ROBUST</B><SPAN STYLE="font-weight: normal">
- when the owner of the mutex terminates without unlocking the mutex,
the mutex is
 unlocked. The next owner of this mutex acquires the
mutex with an error return of
EOWNERDEAD.</SPAN></P>
<H2 CLASS="western"><A HREF="#toc3" NAME="sect3">Return Value</A></H2>
<P><SPAN STYLE="font-weight: normal">On success all functions return
0, otherwise they return an error code as follows:</SPAN></P>
<P><B>pthread_mutexattr_init</B></P>
<P STYLE="margin-left: 0.79in"><B>ENOMEM</B><SPAN STYLE="font-weight: normal">
- insufficient memory for </SPAN><I><SPAN STYLE="font-weight: normal">attr</SPAN></I><SPAN STYLE="font-weight: normal">.</SPAN></P>
<P><B>pthread_mutexattr_destroy</B></P>
<P STYLE="margin-left: 0.79in"><B>EINVAL</B><SPAN STYLE="font-weight: normal">
- </SPAN><I><SPAN STYLE="font-weight: normal">attr</SPAN></I><SPAN STYLE="font-weight: normal">
is invalid.</SPAN></P>
<P><B>pthread_mutexattr_gettype</B></P>
<P STYLE="margin-left: 0.79in"><B>EINVAL</B><SPAN STYLE="font-weight: normal">
- </SPAN><I><SPAN STYLE="font-weight: normal">attr</SPAN></I><SPAN STYLE="font-weight: normal">
is invalid.</SPAN></P>
<P><B>pthread_mutexattr_settype</B></P>
<DL>
	<DL>
		<DL>
			<DT><B>EINVAL</B><SPAN STYLE="font-weight: normal"> - </SPAN><I><SPAN STYLE="font-weight: normal">attr</SPAN></I><SPAN STYLE="font-weight: normal">
			is invalid or </SPAN><I><SPAN STYLE="font-weight: normal">type</SPAN></I><SPAN STYLE="font-weight: normal">
			is none of:</SPAN></DT><DL>
				<DL>
					<DT>
					<B>PTHREAD_MUTEX_NORMAL<BR>PTHREAD_MUTEX_FAST_NP<BR>PTHREAD_MUTEX_RECURSIVE<BR>PTHREAD_MUTEX_RECURSIVE_NP<BR>PTHREAD_MUTEX_ERRORCHECK<BR>PTHREAD_MUTEX_ERRORCHECK_NP</B></DT></DL>
			</DL>
		</DL>
	</DL>
	<DD STYLE="margin-left: 0in">
	<BR>
	</DD></DL>
<P>
<B>pthread_mutexattr_getrobust</B></P>
<P STYLE="margin-left: 0.79in"><B>EINVAL</B><SPAN STYLE="font-weight: normal">
â€“ </SPAN><I><SPAN STYLE="font-weight: normal">attr</SPAN></I><SPAN STYLE="font-weight: normal">
or </SPAN><I><SPAN STYLE="font-weight: normal">robust</SPAN></I><SPAN STYLE="font-weight: normal">
is invalid.</SPAN></P>
<P><B>pthread_mutexattr_setrobust</B></P>
<P STYLE="margin-left: 0.79in"><B>EINVAL</B><SPAN STYLE="font-weight: normal">
â€“ </SPAN><I><SPAN STYLE="font-weight: normal">attr</SPAN></I><SPAN STYLE="font-weight: normal">
or </SPAN><I><SPAN STYLE="font-weight: normal">robust</SPAN></I><SPAN STYLE="font-weight: normal">
is invalid.</SPAN></P>
<H2 CLASS="western"><A HREF="#toc5" NAME="sect5">Author</A></H2>
<P>Xavier Leroy &lt;<EMAIL>&gt; 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<H2 CLASS="western"><A HREF="#toc6" NAME="sect6">See Also</A></H2>
<P><A HREF="pthread_mutex_init.html"><B>pthread_mutex_init</B>(3)</A>
, <A HREF="pthread_mutex_lock.html"><B>pthread_mutex_lock</B>(3)</A>
, <A HREF="pthread_mutex_unlock.html"><B>pthread_mutex_unlock</B>(3)</A>
. 
</P>
<H2 CLASS="western"><A HREF="#toc7" NAME="sect7"><FONT COLOR="#000080"><U>Notes</U></FONT></A></H2>
<P>For speed, <B>PThreads4W</B> never checks the thread ownership
of non-robust mutexes of type <B>PTHREAD_MUTEX_NORMAL</B> (or
<B>PTHREAD_MUTEX_FAST_NP</B>) when performing operations on the
mutex. It is therefore possible for one thread to lock such a mutex
and another to unlock it.</P>
<P STYLE="font-weight: normal">When developing code, it is a common
precaution to substitute the error checking type, then drop in the
normal type for release if the extra performance is required.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0in"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0in"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0in"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0in"><A HREF="#sect3" NAME="toc3">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0in"><A HREF="#sect5" NAME="toc5">Author</A>
		</P>
	<LI><P STYLE="margin-bottom: 0in"><A HREF="#sect6" NAME="toc6">See
	Also</A></P>
	<LI><P><A HREF="#sect7" NAME="toc7">Notes</A></P>
</UL>
</BODY>
</HTML>