#include "ps4_gpu.h"
#include "../memory/memory_diagnostics.h"
#include <SDL_vulkan.h>
#include <algorithm>
#include <chrono>
#include <cstdint>
#include <cstring>
#include <fmt/format.h>
#include <memory>
#include <mutex>
#include <set>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <string>
#include <thread>
#include <utility>
#include <vector>
#include <vulkan/vulkan.h>
#ifdef _WIN32
#include <windows.h>
#define VK_USE_PLATFORM_WIN32_KHR
#include <vulkan/vulkan_win32.h>
#endif

namespace ps4 {

/**
 * @brief Constructs the GPU emulator.
 * @param memory MMU instance.
 * @param translator Shader translator.
 * @param tileManager Tile manager.
 * @param vulkanContext Shared Vulkan context.
 */
PS4GPU::PS4GPU(PS4MMU &memory, std::unique_ptr<GNMShaderTranslator> translator,
               std::unique_ptr<TileManager> tileManager,
               VulkanContext *vulkanContext, SDL_Window *window)
    : m_memory(memory), m_shaderTranslator(std::move(translator)),
      m_tileManager(std::move(tileManager)), m_vulkan(vulkanContext),
      m_window(window) {
  auto start = std::chrono::steady_clock::now();
  m_registerState = &m_gnmState; // Point to our internal GNM state
  m_stats = GPUStats();

  // Set up shader translation callbacks
  if (m_shaderTranslator) {
    m_shaderTranslator->SetShaderTranslationCallback_SPIRV(
        [this](GCNShaderType type, uint64_t hash,
               const std::vector<uint32_t> &spirv) {
          this->NotifyShaderTranslated(type, hash, spirv);
        });
    m_shaderTranslator->SetShaderTranslationCallback_GLSL(
        [this](GCNShaderType type, uint64_t hash, const std::string &glsl) {
          this->NotifyShaderTranslated(type, hash, glsl);
        });
  }

  // Set up register change callback for GNMRegisterState
  m_gnmState.SetRegisterChangeCallback([this](GNMRegisterType regType,
                                              uint32_t stage, uint32_t offset,
                                              uint32_t value) {
    this->NotifyRegisterChange(regType, stage, offset, value);
  });

  spdlog::info("PS4GPU constructed");
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Destructor, cleaning up resources.
 */
PS4GPU::~PS4GPU() {
  auto start = std::chrono::steady_clock::now();
  Shutdown();
  spdlog::info("PS4GPU destroyed");
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Initializes the GPU.
 * @return True on success, false otherwise.
 */
bool PS4GPU::Initialize() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    spdlog::info("PS4GPU initializing...");
    if (!m_vulkan || m_vulkan->device == VK_NULL_HANDLE) {
      spdlog::error("Invalid Vulkan context provided to PS4GPU");
      m_stats.cacheMisses++;
      throw GPUException("Invalid Vulkan context");
    }
    spdlog::info("PS4GPU: Creating swapchain...");
    spdlog::info("PS4GPU: About to call CreateSwapchain()");
    if (!CreateSwapchain()) {
      spdlog::error("Failed to create swapchain");
      m_stats.cacheMisses++;
      throw GPUException("PS4GPU swapchain creation failed");
    }
    spdlog::info("PS4GPU: CreateSwapchain() completed successfully");

    spdlog::info("PS4GPU: Creating swapchain image views...");
    if (!CreateSwapchainImageViews()) {
      spdlog::error("Failed to create swapchain image views");
      m_stats.cacheMisses++;
      throw GPUException("PS4GPU swapchain image views creation failed");
    }

    spdlog::info("PS4GPU: Creating command pool...");
    if (!CreateCommandPool()) {
      spdlog::error("Failed to create command pool");
      m_stats.cacheMisses++;
      throw GPUException("PS4GPU command pool creation failed");
    }

    spdlog::info("PS4GPU: Creating descriptor pool...");
    if (!CreateDescriptorPool()) {
      spdlog::error("Failed to create descriptor pool");
      m_stats.cacheMisses++;
      throw GPUException("PS4GPU descriptor pool creation failed");
    }

    spdlog::info("PS4GPU: Creating sync objects...");
    if (!CreateSyncObjects()) {
      spdlog::error("Failed to create sync objects");
      m_stats.cacheMisses++;
      throw GPUException("PS4GPU sync objects creation failed");
    }

    spdlog::info("PS4GPU: Creating default render pass...");
    if (!CreateDefaultRenderPass()) {
      spdlog::error("Failed to create default render pass");
      m_stats.cacheMisses++;
      throw GPUException("PS4GPU default render pass creation failed");
    }

    spdlog::info("PS4GPU: Creating framebuffers...");
    if (!CreateFramebuffers()) {
      spdlog::error("Failed to create framebuffers");
      m_stats.cacheMisses++;
      throw GPUException("PS4GPU framebuffers creation failed");
    }
    VkCommandBufferAllocateInfo cmdAllocInfo{};
    cmdAllocInfo.sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_ALLOCATE_INFO;
    cmdAllocInfo.commandPool = m_vulkan->commandPool;
    cmdAllocInfo.level = VK_COMMAND_BUFFER_LEVEL_PRIMARY;
    cmdAllocInfo.commandBufferCount = 1;
    if (vkAllocateCommandBuffers(m_vulkan->device, &cmdAllocInfo,
                                 &m_commandBuffer) != VK_SUCCESS) {
      spdlog::error("Failed to allocate command buffer");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan command buffer allocation failed");
    }
    VkPhysicalDeviceProperties properties;
    vkGetPhysicalDeviceProperties(m_vulkan->physicalDevice, &properties);
    m_timestampPeriod = properties.limits.timestampPeriod;
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    // RACE CONDITION FIX: Use .load() for atomic variables in logging
    auto latency = m_stats.totalLatencyUs.load();
    spdlog::info("PS4GPU initialized with Vulkan, latency={}us", latency);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Initialize failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Shuts down the GPU, releasing resources.
 */
void PS4GPU::Shutdown() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    if (!m_vulkan || !m_vulkan->device) {
      spdlog::trace("PS4GPU already shut down");
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      return;
    }
    vkDeviceWaitIdle(m_vulkan->device);
    if (m_commandBuffer != VK_NULL_HANDLE) {
      vkFreeCommandBuffers(m_vulkan->device, m_vulkan->commandPool, 1,
                           &m_commandBuffer);
      m_commandBuffer = VK_NULL_HANDLE;
    }
    for (auto &[_, fence] : m_fences) {
      if (fence != VK_NULL_HANDLE) {
        vkDestroyFence(m_vulkan->device, fence, nullptr);
      }
    }
    m_fences.clear();
    for (auto &[_, queryPool] : m_profileQueryPools) {
      if (queryPool != VK_NULL_HANDLE) {
        vkDestroyQueryPool(m_vulkan->device, queryPool, nullptr);
      }
    }
    m_profileQueryPools.clear();
    m_profileResults.clear();
    for (auto &[_, pipeline] : m_graphicsPipelineCache) {
      if (pipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(m_vulkan->device, pipeline, nullptr);
      }
    }
    m_graphicsPipelineCache.clear();
    for (auto &[_, pipeline] : m_computePipelineCache) {
      if (pipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(m_vulkan->device, pipeline, nullptr);
      }
    }
    m_computePipelineCache.clear();
    for (auto &[_, renderPass] : m_renderPassCache) {
      if (renderPass != VK_NULL_HANDLE) {
        vkDestroyRenderPass(m_vulkan->device, renderPass, nullptr);
      }
    }
    m_renderPassCache.clear();
    for (auto &[_, framebuffer] : m_framebufferCache) {
      if (framebuffer != VK_NULL_HANDLE) {
        vkDestroyFramebuffer(m_vulkan->device, framebuffer, nullptr);
      }
    }
    m_framebufferCache.clear();
    for (auto &[_, sampler] : m_samplers) {
      if (sampler != VK_NULL_HANDLE) {
        vkDestroySampler(m_vulkan->device, sampler, nullptr);
      }
    }
    m_samplers.clear();
    for (auto &[_, view] : m_textureViews) {
      if (view != VK_NULL_HANDLE) {
        vkDestroyImageView(m_vulkan->device, view, nullptr);
      }
    }
    m_textureViews.clear();
    for (auto &[_, target] : m_renderTargets) {
      if (target.view != VK_NULL_HANDLE) {
        vkDestroyImageView(m_vulkan->device, target.view, nullptr);
      }
      if (target.image != VK_NULL_HANDLE) {
        vkDestroyImage(m_vulkan->device, target.image, nullptr);
      }
      if (target.memory != VK_NULL_HANDLE) {
        vkFreeMemory(m_vulkan->device, target.memory, nullptr);
      }
    }
    m_renderTargets.clear();
    for (auto &[_, mapping] : m_memoryMappings) {
      if (mapping.mappedData) {
        vkUnmapMemory(m_vulkan->device, mapping.memory);
      }
      if (mapping.buffer != VK_NULL_HANDLE) {
        vkDestroyBuffer(m_vulkan->device, mapping.buffer, nullptr);
      }
      if (mapping.memory != VK_NULL_HANDLE) {
        vkFreeMemory(m_vulkan->device, mapping.memory, nullptr);
      }
    }
    m_memoryMappings.clear();
    for (auto &[_, shader] : m_shaderModules) {
      if (shader.module != VK_NULL_HANDLE) {
        vkDestroyShaderModule(m_vulkan->device, shader.module, nullptr);
      }
    }
    m_shaderModules.clear();
    if (m_currentPipelineLayout != VK_NULL_HANDLE) {
      vkDestroyPipelineLayout(m_vulkan->device, m_currentPipelineLayout,
                              nullptr);
      m_currentPipelineLayout = VK_NULL_HANDLE;
    }
    if (m_vulkan->inFlightFence != VK_NULL_HANDLE) {
      vkDestroyFence(m_vulkan->device, m_vulkan->inFlightFence, nullptr);
      m_vulkan->inFlightFence = VK_NULL_HANDLE;
    }
    if (m_vulkan->renderFinishedSemaphore != VK_NULL_HANDLE) {
      vkDestroySemaphore(m_vulkan->device, m_vulkan->renderFinishedSemaphore,
                         nullptr);
      m_vulkan->renderFinishedSemaphore = VK_NULL_HANDLE;
    }
    if (m_vulkan->imageAvailableSemaphore != VK_NULL_HANDLE) {
      vkDestroySemaphore(m_vulkan->device, m_vulkan->imageAvailableSemaphore,
                         nullptr);
      m_vulkan->imageAvailableSemaphore = VK_NULL_HANDLE;
    }
    if (m_vulkan->descriptorPool != VK_NULL_HANDLE) {
      vkDestroyDescriptorPool(m_vulkan->device, m_vulkan->descriptorPool,
                              nullptr);
      m_vulkan->descriptorPool = VK_NULL_HANDLE;
    }
    if (m_vulkan->commandPool != VK_NULL_HANDLE) {
      vkDestroyCommandPool(m_vulkan->device, m_vulkan->commandPool, nullptr);
      m_vulkan->commandPool = VK_NULL_HANDLE;
    }
    for (auto &view : m_vulkan->swapchainImageViews) {
      if (view != VK_NULL_HANDLE) {
        vkDestroyImageView(m_vulkan->device, view, nullptr);
      }
    }
    m_vulkan->swapchainImageViews.clear();
    if (m_vulkan->swapchain != VK_NULL_HANDLE) {
      vkDestroySwapchainKHR(m_vulkan->device, m_vulkan->swapchain, nullptr);
      m_vulkan->swapchain = VK_NULL_HANDLE;
    }
    if (m_vulkan->surface != VK_NULL_HANDLE) {
      vkDestroySurfaceKHR(m_vulkan->instance, m_vulkan->surface, nullptr);
      m_vulkan->surface = VK_NULL_HANDLE;
    }
    if (m_vulkan->device != VK_NULL_HANDLE) {
      vkDestroyDevice(m_vulkan->device, nullptr);
      m_vulkan->device = VK_NULL_HANDLE;
    }
    if (m_vulkan->instance != VK_NULL_HANDLE) {
      vkDestroyInstance(m_vulkan->instance, nullptr);
      m_vulkan->instance = VK_NULL_HANDLE;
    }
    m_vulkan = nullptr;
    m_currentRenderPass = VK_NULL_HANDLE;
    m_currentFramebuffer = VK_NULL_HANDLE;
    m_currentGraphicsPipeline = VK_NULL_HANDLE;
    m_currentComputePipeline = VK_NULL_HANDLE;
    m_currentDescriptorSets.clear();
    m_currentIndexBuffer = VK_NULL_HANDLE;
    m_currentVertexBuffers.clear();
    m_currentVertexBufferOffsets.clear();
    m_stats.vramUsage.store(0);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
    spdlog::info("PS4GPU shutdown, latency={}us",
                 m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("Shutdown failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Begins a new frame.
 */
void PS4GPU::BeginFrame() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  int maxAttempts = 3;
  int attempts = 0;
  VkResult result = VK_NOT_READY;
  try {
    vkWaitForFences(m_vulkan->device, 1, &m_vulkan->inFlightFence, VK_TRUE,
                    1000000000);
    vkResetFences(m_vulkan->device, 1, &m_vulkan->inFlightFence);
    // Retry acquiring the swapchain image up to maxAttempts
    while (attempts < maxAttempts) {
      // Use a finite timeout (1 second in nanoseconds)
      result =
          vkAcquireNextImageKHR(m_vulkan->device, m_vulkan->swapchain,
                                1000000000, m_vulkan->imageAvailableSemaphore,
                                VK_NULL_HANDLE, &m_currentSwapchainImageIndex);
      if (result == VK_SUCCESS || result == VK_SUBOPTIMAL_KHR)
        break;
      else if (result == VK_ERROR_OUT_OF_DATE_KHR) {
        spdlog::warn("Swapchain out of date, recreating...");
        if (!RecreateSwapchain()) {
          spdlog::error("Failed to recreate swapchain");
          m_stats.cacheMisses++;
          throw GPUException("Swapchain recreation failed");
        }
      } else {
        spdlog::warn("vkAcquireNextImageKHR attempt {} failed, retrying...",
                     attempts + 1);
      }
      ++attempts;
    }
    if (result != VK_SUCCESS && result != VK_SUBOPTIMAL_KHR) {
      spdlog::error("Failed to acquire swapchain image after {} attempts",
                    attempts);
      m_stats.cacheMisses++;
      throw GPUException("Vulkan swapchain image acquisition failed");
    }
    BeginCommandBuffer();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    // RACE CONDITION FIX: Use .load() for atomic variables in logging
    spdlog::trace("BeginFrame: Started frame {}, latency={}us", m_currentFrame,
                  m_stats.totalLatencyUs.load());
    ++m_currentFrame;
  } catch (const std::exception &e) {
    spdlog::error("BeginFrame failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Ends the current frame.
 */
void PS4GPU::EndFrame() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    if (m_currentRenderPass != VK_NULL_HANDLE) {
      vkCmdEndRenderPass(m_commandBuffer);
      m_currentRenderPass = VK_NULL_HANDLE;
    }
    if (vkEndCommandBuffer(m_commandBuffer) != VK_SUCCESS) {
      spdlog::error("Failed to end command buffer");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan command buffer end failed");
    }
    SubmitCommandBuffer();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    // RACE CONDITION FIX: Use .load() for atomic variables in logging
    spdlog::trace("EndFrame: Ended frame {}, latency={}us", m_currentFrame,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("EndFrame failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Presents the rendered frame.
 */
void PS4GPU::Present() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    VkPresentInfoKHR presentInfo{};
    presentInfo.sType = VK_STRUCTURE_TYPE_PRESENT_INFO_KHR;
    presentInfo.waitSemaphoreCount = 1;
    presentInfo.pWaitSemaphores = &m_vulkan->renderFinishedSemaphore;
    presentInfo.swapchainCount = 1;
    presentInfo.pSwapchains = &m_vulkan->swapchain;
    presentInfo.pImageIndices = &m_currentSwapchainImageIndex;
    VkResult result = vkQueuePresentKHR(m_vulkan->presentQueue, &presentInfo);
    if (result == VK_ERROR_OUT_OF_DATE_KHR || result == VK_SUBOPTIMAL_KHR) {
      spdlog::warn(
          "Swapchain out of date during present, will recreate on next frame");
      // Note: We don't recreate here as we're in the middle of presenting
      // The recreation will happen in the next BeginFrame() call
      m_stats.cacheHits++;
    } else if (result != VK_SUCCESS) {
      spdlog::error("Failed to present frame");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan present failed");
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    // RACE CONDITION FIX: Use .load() for atomic variables in logging
    spdlog::trace("Present: Presented frame {}, latency={}us", m_currentFrame,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("Present failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Sets a shader register.
 * @param stage Shader stage.
 * @param offset Register offset.
 * @param value Register value.
 */
void PS4GPU::SetShaderRegister(uint32_t stage, uint32_t offset,
                               uint32_t value) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    // CRITICAL FIX: Release GPU mutex before calling GNMRegisterState to
    // prevent deadlock GNMRegisterState will call back into
    // NotifyRegisterChange which needs GPU mutex
    lock.unlock();
    m_gnmState.SetShaderRegister(stage, offset, value);

    // Reacquire lock for statistics update
    lock.lock();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    // RACE CONDITION FIX: Use .load() for atomic variables in logging
    spdlog::trace(
        "SetShaderRegister: stage={}, offset={}, value={:x}, latency={}us",
        stage, offset, value, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("SetShaderRegister failed: {}", e.what());
    // Ensure we have lock for stats update
    if (!lock.owns_lock()) {
      lock.lock();
    }
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Sets a context register.
 * @param offset Register offset.
 * @param value Register value.
 */
void PS4GPU::SetContextRegister(uint32_t offset, uint32_t value) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    // CRITICAL FIX: Release GPU mutex before calling GNMRegisterState to
    // prevent deadlock GNMRegisterState will call back into
    // NotifyRegisterChange which needs GPU mutex
    lock.unlock();
    m_gnmState.SetContextRegister(offset, value);

    // Reacquire lock for statistics update
    lock.lock();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    // RACE CONDITION FIX: Use .load() for atomic variables in logging
    spdlog::trace("SetContextRegister: offset={}, value={:x}, latency={}us",
                  offset, value, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("SetContextRegister failed: {}", e.what());
    // Ensure we have lock for stats update
    if (!lock.owns_lock()) {
      lock.lock();
    }
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Sets a render target.
 * @param index Target index.
 * @param surfaceId Surface ID.
 */
void PS4GPU::SetRenderTarget(uint32_t index, uint64_t surfaceId) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    auto it = m_renderTargets.find(surfaceId);
    if (it == m_renderTargets.end()) {
      spdlog::error("Render target not found: {}", surfaceId);
      m_stats.cacheMisses++;
      throw GPUException("Invalid render target");
    }
    // removed: GNMRegisterState has no SetRenderTarget

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("SetRenderTarget: index={}, surfaceId={}, latency={}us",
                  index, surfaceId, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("SetRenderTarget failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Sets a depth render target.
 * @param surfaceId Surface ID.
 */
void PS4GPU::SetDepthRenderTarget(uint64_t surfaceId) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    auto it = m_renderTargets.find(surfaceId);
    if (it == m_renderTargets.end() || !it->second.isDepthStencil) {
      spdlog::error("Depth render target not found or invalid: {}", surfaceId);
      m_stats.cacheMisses++;
      throw GPUException("Invalid depth render target");
    }
    // removed: GNMRegisterState has no SetDepthRenderTarget

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("SetDepthRenderTarget: surfaceId={}, latency={}us", surfaceId,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("SetDepthRenderTarget failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Sets the viewport.
 * @param x Viewport x.
 * @param y Viewport y.
 * @param width Viewport width.
 * @param height Viewport height.
 * @param minDepth Minimum depth.
 * @param maxDepth Maximum depth.
 */
void PS4GPU::SetViewport(float x, float y, float width, float height,
                         float minDepth, float maxDepth) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    VkViewport viewport{};
    viewport.x = x;
    viewport.y = y;
    viewport.width = width;
    viewport.height = height;
    viewport.minDepth = minDepth;
    viewport.maxDepth = maxDepth;
    vkCmdSetViewport(m_commandBuffer, 0, 1, &viewport);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("SetViewport: x={}, y={}, width={}, height={}, minDepth={}, "
                  "maxDepth={}, latency={}us",
                  x, y, width, height, minDepth, maxDepth,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("SetViewport failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Sets the scissor rectangle.
 * @param x Scissor x.
 * @param y Scissor y.
 * @param width Scissor width.
 * @param height Scissor height.
 */
void PS4GPU::SetScissor(int32_t x, int32_t y, uint32_t width, uint32_t height) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    VkRect2D scissor{};
    scissor.offset = {x, y};
    scissor.extent = {width, height};
    vkCmdSetScissor(m_commandBuffer, 0, 1, &scissor);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("SetScissor: x={}, y={}, width={}, height={}, latency={}us",
                  x, y, width, height, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("SetScissor failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Binds a vertex buffer.
 * @param binding Binding index.
 * @param gpuAddress GPU address.
 * @param stride Buffer stride.
 */
void PS4GPU::BindVertexBuffer(uint32_t binding, uint64_t gpuAddress,
                              uint32_t stride) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    auto it = m_memoryMappings.find(gpuAddress);
    if (it == m_memoryMappings.end()) {
      spdlog::error("Vertex buffer mapping not found: {}", gpuAddress);
      m_stats.cacheMisses++;
      throw GPUException("Invalid vertex buffer mapping");
    }
    if (m_currentVertexBuffers.size() <= binding) {
      m_currentVertexBuffers.resize(binding + 1, VK_NULL_HANDLE);
      m_currentVertexBufferOffsets.resize(binding + 1, 0);
    }
    m_currentVertexBuffers[binding] = it->second.buffer;
    m_currentVertexBufferOffsets[binding] = 0;
    vkCmdBindVertexBuffers(m_commandBuffer, binding, 1,
                           &m_currentVertexBuffers[binding],
                           &m_currentVertexBufferOffsets[binding]);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("BindVertexBuffer: binding={}, gpuAddress={:x}, stride={}, "
                  "latency={}us",
                  binding, gpuAddress, stride, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("BindVertexBuffer failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Binds an index buffer.
 * @param gpuAddress GPU address.
 * @param indexType Index type.
 */
void PS4GPU::BindIndexBuffer(uint64_t gpuAddress, VkIndexType indexType) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    auto it = m_memoryMappings.find(gpuAddress);
    if (it == m_memoryMappings.end()) {
      spdlog::error("Index buffer mapping not found: {}", gpuAddress);
      m_stats.cacheMisses++;
      throw GPUException("Invalid index buffer mapping");
    }
    m_currentIndexBuffer = it->second.buffer;
    m_currentIndexType = indexType;
    vkCmdBindIndexBuffer(m_commandBuffer, m_currentIndexBuffer, 0, indexType);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        fmt::runtime(
            "BindIndexBuffer: gpuAddress={:x}, indexType={}, latency={}us"),
        gpuAddress, static_cast<uint32_t>(indexType),
        m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("BindIndexBuffer failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Binds a resource.
 * @param set Descriptor set.
 * @param binding Binding index.
 * @param resourceId Resource ID.
 */
void PS4GPU::BindResource(uint32_t set, uint32_t binding, uint64_t resourceId) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    VkDescriptorSet descriptorSet =
        AllocateDescriptorSet(GetOrCreateDescriptorSetLayout());
    VkDescriptorImageInfo imageInfo{};
    auto textureIt = m_textureViews.find(resourceId);
    if (textureIt != m_textureViews.end()) {
      imageInfo.imageView = textureIt->second;
      imageInfo.imageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
      auto samplerIt = m_samplers.find(resourceId);
      imageInfo.sampler =
          samplerIt != m_samplers.end() ? samplerIt->second : VK_NULL_HANDLE;
    } else {
      spdlog::error("Resource not found: {}", resourceId);
      m_stats.cacheMisses++;
      throw GPUException("Invalid resource ID");
    }
    VkWriteDescriptorSet write{};
    write.sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
    write.dstSet = descriptorSet;
    write.dstBinding = binding;
    write.dstArrayElement = 0;
    write.descriptorType = VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER;
    write.descriptorCount = 1;
    write.pImageInfo = &imageInfo;
    vkUpdateDescriptorSets(m_vulkan->device, 1, &write, 0, nullptr);
    if (m_currentDescriptorSets.size() <= set) {
      m_currentDescriptorSets.resize(set + 1, VK_NULL_HANDLE);
    }
    m_currentDescriptorSets[set] = descriptorSet;
    vkCmdBindDescriptorSets(m_commandBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS,
                            m_currentPipelineLayout, set, 1,
                            &m_currentDescriptorSets[set], 0, nullptr);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "BindResource: set={}, binding={}, resourceId={:x}, latency={}us", set,
        binding, resourceId, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("BindResource failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Draws indexed geometry.
 * @param indexCount Index count.
 * @param instanceCount Instance count.
 * @param firstIndex First index.
 * @param vertexOffset Vertex offset.
 * @param firstInstance First instance.
 */
void PS4GPU::DrawIndex(uint32_t indexCount, uint32_t instanceCount,
                       uint32_t firstIndex, int32_t vertexOffset,
                       uint32_t firstInstance) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    m_currentGraphicsPipeline = GetOrCreateGraphicsPipeline();
    vkCmdBindPipeline(m_commandBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS,
                      m_currentGraphicsPipeline);
    std::vector<uint64_t> colorTargetIds;
    // Populate from active render targets via GNM register state
    if (m_registerState) {
      for (uint32_t i = 0; i < 8; ++i) {
        try {
          uint32_t targetAddr = m_registerState->GetRenderTarget(i);
          if (targetAddr != 0) {
            // Convert GNM address to surface ID (simple mapping for now)
            uint64_t surfaceId = static_cast<uint64_t>(targetAddr);
            if (m_renderTargets.find(surfaceId) != m_renderTargets.end()) {
              colorTargetIds.push_back(surfaceId);
            }
          }
        } catch (const std::exception &e) {
          // Skip invalid render targets
          spdlog::trace("DrawIndex: Skipping render target {}: {}", i,
                        e.what());
        }
      }
    }
    // Fallback to all render targets if no active ones found
    if (colorTargetIds.empty()) {
      for (auto &p : m_renderTargets) {
        colorTargetIds.push_back(p.first);
      }
    }
    uint64_t depthTargetId = 0; // no depth target
    m_currentFramebuffer =
        GetOrCreateFramebuffer(colorTargetIds, depthTargetId);
    m_currentRenderPass = GetOrCreateRenderPass(RenderPassKey{});
    VkRenderPassBeginInfo renderPassInfo{};
    renderPassInfo.sType = VK_STRUCTURE_TYPE_RENDER_PASS_BEGIN_INFO;
    renderPassInfo.renderPass = m_currentRenderPass;
    renderPassInfo.framebuffer = m_currentFramebuffer;
    renderPassInfo.renderArea.offset = {0, 0};
    renderPassInfo.renderArea.extent = m_vulkan->swapchainExtent;
    VkClearValue clearValues[2] = {};
    clearValues[0].color = {{0.0f, 0.0f, 0.0f, 1.0f}};
    clearValues[1].depthStencil = {1.0f, 0};
    renderPassInfo.clearValueCount = depthTargetId ? 2 : 1;
    renderPassInfo.pClearValues = clearValues;
    vkCmdBeginRenderPass(m_commandBuffer, &renderPassInfo,
                         VK_SUBPASS_CONTENTS_INLINE);
    vkCmdDrawIndexed(m_commandBuffer, indexCount, instanceCount, firstIndex,
                     vertexOffset, firstInstance);
    m_stats.drawCalls++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("DrawIndex: indices={}, instances={}, firstIndex={}, "
                  "vertexOffset={}, firstInstance={}, latency={}us",
                  indexCount, instanceCount, firstIndex, vertexOffset,
                  firstInstance, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("DrawIndex failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Draws indexed geometry indirectly.
 * @param bufferAddress Buffer address.
 * @param drawCount Draw count.
 * @param stride Stride.
 */
void PS4GPU::DrawIndexIndirect(uint64_t bufferAddress, uint32_t drawCount,
                               uint32_t stride) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    auto it = m_memoryMappings.find(bufferAddress);
    if (it == m_memoryMappings.end()) {
      spdlog::error("Indirect buffer mapping not found: {}", bufferAddress);
      m_stats.cacheMisses++;
      throw GPUException("Invalid indirect buffer mapping");
    }
    m_currentGraphicsPipeline = GetOrCreateGraphicsPipeline();
    vkCmdBindPipeline(m_commandBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS,
                      m_currentGraphicsPipeline);
    std::vector<uint64_t> colorTargetIds;
    // Populate from active render targets via GNM register state
    if (m_registerState) {
      for (uint32_t i = 0; i < 8; ++i) {
        try {
          uint32_t targetAddr = m_registerState->GetRenderTarget(i);
          if (targetAddr != 0) {
            // Convert GNM address to surface ID (simple mapping for now)
            uint64_t surfaceId = static_cast<uint64_t>(targetAddr);
            if (m_renderTargets.find(surfaceId) != m_renderTargets.end()) {
              colorTargetIds.push_back(surfaceId);
            }
          }
        } catch (const std::exception &e) {
          // Skip invalid render targets
          spdlog::trace("DrawIndexIndirect: Skipping render target {}: {}", i,
                        e.what());
        }
      }
    }
    // Fallback to all render targets if no active ones found
    if (colorTargetIds.empty()) {
      for (auto &p : m_renderTargets) {
        colorTargetIds.push_back(p.first);
      }
    }
    uint64_t depthTargetId = 0; // no depth target
    m_currentFramebuffer =
        GetOrCreateFramebuffer(colorTargetIds, depthTargetId);
    m_currentRenderPass = GetOrCreateRenderPass(RenderPassKey{});
    VkRenderPassBeginInfo renderPassInfo{};
    renderPassInfo.sType = VK_STRUCTURE_TYPE_RENDER_PASS_BEGIN_INFO;
    renderPassInfo.renderPass = m_currentRenderPass;
    renderPassInfo.framebuffer = m_currentFramebuffer;
    renderPassInfo.renderArea.offset = {0, 0};
    renderPassInfo.renderArea.extent = m_vulkan->swapchainExtent;
    VkClearValue clearValues[2] = {};
    clearValues[0].color = {{0.0f, 0.0f, 0.0f, 1.0f}};
    clearValues[1].depthStencil = {1.0f, 0};
    renderPassInfo.clearValueCount = depthTargetId ? 2 : 1;
    renderPassInfo.pClearValues = clearValues;
    vkCmdBeginRenderPass(m_commandBuffer, &renderPassInfo,
                         VK_SUBPASS_CONTENTS_INLINE);
    vkCmdDrawIndexedIndirect(m_commandBuffer, it->second.buffer, 0, drawCount,
                             stride);
    m_stats.drawCalls++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("DrawIndexIndirect: bufferAddress={:x}, drawCount={}, "
                  "stride={}, latency={}us",
                  bufferAddress, drawCount, stride,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("DrawIndexIndirect failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Draws indexed geometry (simplified).
 * @param indexCount Index count.
 * @param instanceCount Instance count.
 * @param firstIndex First index.
 */
void PS4GPU::DrawIndexed(uint32_t indexCount, uint32_t instanceCount,
                         uint32_t firstIndex) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    DrawIndex(indexCount, instanceCount, firstIndex, 0, 0);
    m_stats.drawCalls++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "DrawIndexed: indices={}, instances={}, firstIndex={}, latency={}us",
        indexCount, instanceCount, firstIndex, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("DrawIndexed failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Dispatches a compute workload.
 * @param groupCountX Group count X.
 * @param groupCountY Group count Y.
 * @param groupCountZ Group count Z.
 */
void PS4GPU::Dispatch(uint32_t groupCountX, uint32_t groupCountY,
                      uint32_t groupCountZ) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    m_currentComputePipeline = GetOrCreateComputePipeline();
    vkCmdBindPipeline(m_commandBuffer, VK_PIPELINE_BIND_POINT_COMPUTE,
                      m_currentComputePipeline);
    vkCmdDispatch(m_commandBuffer, groupCountX, groupCountY, groupCountZ);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("Dispatch: groupCountX={}, groupCountY={}, groupCountZ={}, "
                  "latency={}us",
                  groupCountX, groupCountY, groupCountZ,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("Dispatch failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Dispatches a compute workload indirectly.
 * @param bufferAddress Buffer address.
 * @param offset Buffer offset.
 */
void PS4GPU::DispatchIndirect(uint64_t bufferAddress, uint64_t offset) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    auto it = m_memoryMappings.find(bufferAddress);
    if (it == m_memoryMappings.end()) {
      spdlog::error("Indirect buffer mapping not found: {}", bufferAddress);
      m_stats.cacheMisses++;
      throw GPUException("Invalid indirect buffer mapping");
    }
    m_currentComputePipeline = GetOrCreateComputePipeline();
    vkCmdBindPipeline(m_commandBuffer, VK_PIPELINE_BIND_POINT_COMPUTE,
                      m_currentComputePipeline);
    vkCmdDispatchIndirect(m_commandBuffer, it->second.buffer, offset);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "DispatchIndirect: bufferAddress={:x}, offset={}, latency={}us",
        bufferAddress, offset, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("DispatchIndirect failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Waits for a register or memory value.
 * @param address Address to wait on.
 * @param reference Reference value.
 * @param mask Value mask.
 * @param function Comparison function.
 * @param isMemory True if memory, false if register.
 */
void PS4GPU::WaitRegisterMemory(uint64_t address, uint32_t reference,
                                uint32_t mask, uint32_t function,
                                bool isMemory) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    spdlog::info("PS4GPU: WaitRegisterMemory(address=0x{:x}, ref=0x{:x}, "
                 "mask=0x{:x}, func={}, isMemory={})",
                 address, reference, mask, function, isMemory);

    // Implement actual wait logic
    auto startTime = std::chrono::high_resolution_clock::now();
    const auto timeout = std::chrono::milliseconds(1000); // 1 second timeout

    while (true) {
      uint32_t currentValue = 0;

      if (isMemory) {
        // Read from GPU memory
        try {
          uint64_t physAddr =
              m_memory.VirtualToPhysical(address, sizeof(uint32_t), false);
          if (physAddr != 0) {
            m_memory.ReadVirtual(address, &currentValue, sizeof(currentValue),
                                 1);
          }
        } catch (const std::exception &e) {
          spdlog::warn("PS4GPU: Failed to read memory at 0x{:x}: {}", address,
                       e.what());
          break;
        }
      } else {
        // Read from GPU register
        // For simplicity, we'll simulate register reads based on address
        if (address >= 0x8000 && address < 0x9000) {
          // Simulate some GPU registers
          currentValue = 0x12345678; // Dummy value
        }
      }

      // Apply mask and check condition
      uint32_t maskedValue = currentValue & mask;
      uint32_t maskedReference = reference & mask;

      bool conditionMet = false;
      switch (function) {
      case 0: // Always
        conditionMet = true;
        break;
      case 1: // Less than
        conditionMet = (maskedValue < maskedReference);
        break;
      case 2: // Less than or equal
        conditionMet = (maskedValue <= maskedReference);
        break;
      case 3: // Equal
        conditionMet = (maskedValue == maskedReference);
        break;
      case 4: // Not equal
        conditionMet = (maskedValue != maskedReference);
        break;
      case 5: // Greater than or equal
        conditionMet = (maskedValue >= maskedReference);
        break;
      case 6: // Greater than
        conditionMet = (maskedValue > maskedReference);
        break;
      default:
        spdlog::warn("PS4GPU: Unknown wait function {}", function);
        conditionMet = true;
        break;
      }

      if (conditionMet) {
        spdlog::debug("PS4GPU: Wait condition met, value=0x{:x}", currentValue);
        break;
      }

      // Check timeout
      auto currentTime = std::chrono::high_resolution_clock::now();
      if (currentTime - startTime > timeout) {
        spdlog::warn("PS4GPU: WaitRegisterMemory timeout after 1 second");
        break;
      }

      // Small delay to avoid busy waiting
      lock.unlock();
      std::this_thread::sleep_for(std::chrono::microseconds(100));
      lock.lock();
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("WaitRegisterMemory: address={:x}, reference={:x}, "
                  "mask={:x}, function={}, isMemory={}, latency={}us",
                  address, reference, mask, function, isMemory,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("WaitRegisterMemory failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Acquires GPU memory.
 * @param address Memory address.
 * @param size Memory size.
 */
void PS4GPU::AcquireMemory(uint64_t address, uint32_t size) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    m_stats.vramUsage += size;
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("AcquireMemory: address={:x}, size={}, vramUsage={} bytes, "
                  "latency={}us",
                  address, size, m_stats.vramUsage.load(),
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("AcquireMemory failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Releases GPU memory.
 * @param address Memory address.
 * @param size Memory size.
 */
void PS4GPU::ReleaseMemory(uint64_t address, uint32_t size) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    m_stats.vramUsage =
        (size <= m_stats.vramUsage) ? m_stats.vramUsage - size : 0;
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("ReleaseMemory: address={:x}, size={}, vramUsage={} bytes, "
                  "latency={}us",
                  address, size, m_stats.vramUsage.load(),
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("ReleaseMemory failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief No-op operation.
 */
void PS4GPU::Nop() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("Nop: latency={}us", m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("Nop failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Compiles a shader.
 * @param gcnCode GCN code.
 * @param size Code size.
 * @param type Shader type.
 * @return Shader ID, or 0 on failure.
 */
uint64_t PS4GPU::CompileShader(const void *gcnCode, size_t size,
                               GCNShaderType type) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    VkShaderStageFlagBits stage = VK_SHADER_STAGE_VERTEX_BIT; // Simplified
    uint64_t shaderId = TranslateGCNShader(gcnCode, size, stage);
    m_stats.shaderCompilations++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "CompileShader: shaderId={:x}, size={}, type={}, latency={}us",
        shaderId, size, static_cast<int>(type), m_stats.totalLatencyUs.load());
    return shaderId;
  } catch (const std::exception &e) {
    spdlog::error("CompileShader failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}

/**
 * @brief Unloads a shader.
 * @param shaderId Shader ID.
 */
void PS4GPU::UnloadShader(uint64_t shaderId) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    auto it = m_shaderModules.find(shaderId);
    if (it != m_shaderModules.end()) {
      if (it->second.module != VK_NULL_HANDLE) {
        vkDestroyShaderModule(m_vulkan->device, it->second.module, nullptr);
      }
      m_shaderModules.erase(it);
      m_stats.cacheHits++;
    } else {
      spdlog::warn("Shader not found: {}", shaderId);
      m_stats.cacheMisses++;
    }
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("UnloadShader: shaderId={:x}, latency={}us", shaderId,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("UnloadShader failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Maps CPU memory to GPU.
 * @param cpuAddress CPU address.
 * @param size Memory size.
 * @param usage Buffer usage flags.
 * @return GPU address, or 0 on failure.
 */
uint64_t PS4GPU::MapMemory(uint64_t cpuAddress, size_t size,
                           VkBufferUsageFlags usage) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    VkDeviceMemory memory;
    VkBuffer buffer = CreateBuffer(size, usage,
                                   VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT |
                                       VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
                                   memory);
    void *mappedData;
    if (vkMapMemory(m_vulkan->device, memory, 0, size, 0, &mappedData) !=
        VK_SUCCESS) {
      spdlog::error("Failed to map memory");
      vkDestroyBuffer(m_vulkan->device, buffer, nullptr);
      vkFreeMemory(m_vulkan->device, memory, nullptr);
      m_stats.cacheMisses++;
      throw GPUException("Vulkan memory mapping failed");
    }
    uint64_t gpuAddress = cpuAddress; // Simplified mapping
    MemoryMapping mapping;
    mapping.gpuAddress = gpuAddress;
    mapping.cpuAddress = cpuAddress;
    mapping.size = size;
    mapping.buffer = buffer;
    mapping.memory = memory;
    mapping.mappedData = mappedData;
    m_memoryMappings[gpuAddress] = mapping;
    m_stats.vramUsage += size;
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("MapMemory: cpuAddress={:x}, size={}, gpuAddress={:x}, "
                  "vramUsage={} bytes, latency={}us",
                  cpuAddress, size, gpuAddress, m_stats.vramUsage.load(),
                  m_stats.totalLatencyUs.load());
    return gpuAddress;
  } catch (const std::exception &e) {
    spdlog::error("MapMemory failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}

/**
 * @brief Unmaps GPU memory.
 * @param gpuAddress GPU address.
 */
void PS4GPU::UnmapMemory(uint64_t gpuAddress) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    auto it = m_memoryMappings.find(gpuAddress);
    if (it != m_memoryMappings.end()) {
      if (it->second.mappedData) {
        vkUnmapMemory(m_vulkan->device, it->second.memory);
      }
      if (it->second.buffer != VK_NULL_HANDLE) {
        vkDestroyBuffer(m_vulkan->device, it->second.buffer, nullptr);
      }
      if (it->second.memory != VK_NULL_HANDLE) {
        vkFreeMemory(m_vulkan->device, it->second.memory, nullptr);
      }
      m_stats.vramUsage = (it->second.size <= m_stats.vramUsage)
                              ? m_stats.vramUsage - it->second.size
                              : 0;
      m_memoryMappings.erase(it);
      ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
      m_stats.cacheHits++;
    } else {
      spdlog::warn("Memory mapping not found: {}", gpuAddress);
      m_stats.cacheMisses++;
    }
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "UnmapMemory: gpuAddress={:x}, vramUsage={} bytes, latency={}us",
        gpuAddress, m_stats.vramUsage.load(), m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("UnmapMemory failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Creates a texture.
 * @param surfaceId Surface ID.
 * @return Texture ID, or 0 on failure.
 */
uint64_t PS4GPU::CreateTexture(uint64_t surfaceId) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    // Check if texture view already exists
    auto existingIt = m_textureViews.find(surfaceId);
    if (existingIt != m_textureViews.end()) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace(
          "CreateTexture: Cache hit for surfaceId={:x}, latency={}us",
          surfaceId,
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count());
      return surfaceId;
    }

    // Look for existing render target first
    auto rtIt = m_renderTargets.find(surfaceId);
    if (rtIt != m_renderTargets.end()) {
      // Create image view from existing render target
      VkImageViewCreateInfo viewInfo{};
      viewInfo.sType = VK_STRUCTURE_TYPE_IMAGE_VIEW_CREATE_INFO;
      viewInfo.image = rtIt->second.image;
      viewInfo.viewType = VK_IMAGE_VIEW_TYPE_2D;
      viewInfo.format = rtIt->second.format;
      viewInfo.subresourceRange.aspectMask =
          rtIt->second.isDepthStencil
              ? VK_IMAGE_ASPECT_DEPTH_BIT | VK_IMAGE_ASPECT_STENCIL_BIT
              : VK_IMAGE_ASPECT_COLOR_BIT;
      viewInfo.subresourceRange.baseMipLevel = 0;
      viewInfo.subresourceRange.levelCount = 1;
      viewInfo.subresourceRange.baseArrayLayer = 0;
      viewInfo.subresourceRange.layerCount = 1;

      VkImageView textureView;
      if (vkCreateImageView(m_vulkan->device, &viewInfo, nullptr,
                            &textureView) != VK_SUCCESS) {
        spdlog::error("Failed to create texture view from render target");
        m_stats.cacheMisses++;
        throw GPUException("Vulkan texture view creation failed");
      }

      m_textureViews[surfaceId] = textureView;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace(
          "CreateTexture: Created view from render target, surfaceId={:x}, "
          "latency={}us",
          surfaceId,
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count());
      return surfaceId;
    }

    // Create new texture from scratch with default parameters
    VkImage image = VK_NULL_HANDLE;
    VkDeviceMemory memory = VK_NULL_HANDLE;
    VkImageView imageView = VK_NULL_HANDLE;

    // Default texture parameters (should be derived from PS4 surface format)
    uint32_t width = 256;                       // Default width
    uint32_t height = 256;                      // Default height
    VkFormat format = VK_FORMAT_R8G8B8A8_UNORM; // Default format

    // Create Vulkan image
    VkImageCreateInfo imageInfo{};
    imageInfo.sType = VK_STRUCTURE_TYPE_IMAGE_CREATE_INFO;
    imageInfo.imageType = VK_IMAGE_TYPE_2D;
    imageInfo.extent.width = width;
    imageInfo.extent.height = height;
    imageInfo.extent.depth = 1;
    imageInfo.mipLevels = 1;
    imageInfo.arrayLayers = 1;
    imageInfo.format = format;
    imageInfo.tiling = VK_IMAGE_TILING_OPTIMAL;
    imageInfo.initialLayout = VK_IMAGE_LAYOUT_UNDEFINED;
    imageInfo.usage =
        VK_IMAGE_USAGE_SAMPLED_BIT | VK_IMAGE_USAGE_TRANSFER_DST_BIT;
    imageInfo.samples = VK_SAMPLE_COUNT_1_BIT;
    imageInfo.sharingMode = VK_SHARING_MODE_EXCLUSIVE;

    if (vkCreateImage(m_vulkan->device, &imageInfo, nullptr, &image) !=
        VK_SUCCESS) {
      spdlog::error("Failed to create Vulkan image for texture");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan image creation failed");
    }

    // Allocate memory for the image
    VkMemoryRequirements memRequirements;
    vkGetImageMemoryRequirements(m_vulkan->device, image, &memRequirements);

    VkMemoryAllocateInfo allocInfo{};
    allocInfo.sType = VK_STRUCTURE_TYPE_MEMORY_ALLOCATE_INFO;
    allocInfo.allocationSize = memRequirements.size;
    allocInfo.memoryTypeIndex = FindMemoryType(
        memRequirements.memoryTypeBits, VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT);

    if (vkAllocateMemory(m_vulkan->device, &allocInfo, nullptr, &memory) !=
        VK_SUCCESS) {
      vkDestroyImage(m_vulkan->device, image, nullptr);
      spdlog::error("Failed to allocate memory for texture");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan memory allocation failed");
    }

    vkBindImageMemory(m_vulkan->device, image, memory, 0);

    // Create image view
    VkImageViewCreateInfo viewInfo{};
    viewInfo.sType = VK_STRUCTURE_TYPE_IMAGE_VIEW_CREATE_INFO;
    viewInfo.image = image;
    viewInfo.viewType = VK_IMAGE_VIEW_TYPE_2D;
    viewInfo.format = format;
    viewInfo.subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
    viewInfo.subresourceRange.baseMipLevel = 0;
    viewInfo.subresourceRange.levelCount = 1;
    viewInfo.subresourceRange.baseArrayLayer = 0;
    viewInfo.subresourceRange.layerCount = 1;

    if (vkCreateImageView(m_vulkan->device, &viewInfo, nullptr, &imageView) !=
        VK_SUCCESS) {
      vkFreeMemory(m_vulkan->device, memory, nullptr);
      vkDestroyImage(m_vulkan->device, image, nullptr);
      spdlog::error("Failed to create image view for texture");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan image view creation failed");
    }

    // Store the texture resources
    m_textureViews[surfaceId] = imageView;

    // Store as render target for resource management
    RenderTarget target{};
    target.surfaceId = surfaceId;
    target.image = image;
    target.memory = memory;
    target.view = imageView;
    target.format = format;
    target.width = width;
    target.height = height;
    target.isDepthStencil = false;
    m_renderTargets[surfaceId] = target;

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info(
        "CreateTexture: Created new texture, surfaceId={:x}, {}x{}, format={}, "
        "latency={}us",
        surfaceId, width, height, static_cast<int>(format),
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count());
    return surfaceId;
  } catch (const std::exception &e) {
    spdlog::error("CreateTexture failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}

/**
 * @brief Updates a texture.
 * @param textureId Texture ID.
 * @param data Texture data.
 * @param size Data size.
 */
void PS4GPU::UpdateTexture(uint64_t textureId, const void *data, size_t size) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    auto renderIt = m_renderTargets.find(textureId);
    if (renderIt == m_renderTargets.end()) {
      spdlog::error("Render target not found for texture update: {}",
                    textureId);
      m_stats.cacheMisses++;
      throw GPUException("Invalid texture ID");
    }
    VkDeviceMemory stagingMemory;
    VkBuffer stagingBuffer =
        CreateBuffer(size, VK_BUFFER_USAGE_TRANSFER_SRC_BIT,
                     VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT |
                         VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
                     stagingMemory);
    void *mappedData;
    if (vkMapMemory(m_vulkan->device, stagingMemory, 0, size, 0, &mappedData) !=
        VK_SUCCESS) {
      spdlog::error("Failed to map staging buffer memory");
      vkDestroyBuffer(m_vulkan->device, stagingBuffer, nullptr);
      vkFreeMemory(m_vulkan->device, stagingMemory, nullptr);
      m_stats.cacheMisses++;
      throw GPUException("Vulkan memory mapping failed");
    }
    std::memcpy(mappedData, data, size);
    vkUnmapMemory(m_vulkan->device, stagingMemory);
    TransitionImageLayout(renderIt->second.image, renderIt->second.format,
                          VK_IMAGE_LAYOUT_UNDEFINED,
                          VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL);
    CopyBufferToImage(stagingBuffer, renderIt->second.image,
                      renderIt->second.width, renderIt->second.height);
    TransitionImageLayout(renderIt->second.image, renderIt->second.format,
                          VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
                          VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL);
    vkDestroyBuffer(m_vulkan->device, stagingBuffer, nullptr);
    vkFreeMemory(m_vulkan->device, stagingMemory, nullptr);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("UpdateTexture: textureId={:x}, size={}, latency={}us",
                  textureId, size, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("UpdateTexture failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Deletes a texture.
 * @param textureId Texture ID.
 */
void PS4GPU::DeleteTexture(uint64_t textureId) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    auto it = m_textureViews.find(textureId);
    if (it != m_textureViews.end()) {
      if (it->second != VK_NULL_HANDLE) {
        vkDestroyImageView(m_vulkan->device, it->second, nullptr);
      }
      m_textureViews.erase(it);
      m_stats.cacheHits++;
    } else {
      spdlog::warn("Texture not found: {}", textureId);
      m_stats.cacheMisses++;
    }
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("DeleteTexture: textureId={:x}, latency={}us", textureId,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("DeleteTexture failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Creates a sampler.
 * @return Sampler ID, or 0 on failure.
 */
uint64_t PS4GPU::CreateSampler() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    // Get sampler state from GNM registers (simplified approach)
    // In a real implementation, these would be derived from PS4 texture sampler
    // registers
    uint32_t samplerState0 =
        m_gnmState.GetContextRegister(0x300); // Hypothetical sampler register
    uint32_t samplerState1 =
        m_gnmState.GetContextRegister(0x301); // Hypothetical sampler register

    // Create sampler ID based on state for caching
    uint64_t samplerId =
        (static_cast<uint64_t>(samplerState0) << 32) | samplerState1;
    if (samplerId == 0) {
      samplerId = 1; // Default sampler ID
    }

    // Check if sampler already exists
    auto it = m_samplers.find(samplerId);
    if (it != m_samplers.end()) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace(
          "CreateSampler: Cache hit for samplerId={:x}, latency={}us",
          samplerId,
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count());
      return samplerId;
    }

    VkSamplerCreateInfo samplerInfo{};
    samplerInfo.sType = VK_STRUCTURE_TYPE_SAMPLER_CREATE_INFO;

    // Derive filter modes from sampler state
    bool linearMag = (samplerState0 & 0x1) != 0;
    bool linearMin = (samplerState0 & 0x2) != 0;
    samplerInfo.magFilter = linearMag ? VK_FILTER_LINEAR : VK_FILTER_NEAREST;
    samplerInfo.minFilter = linearMin ? VK_FILTER_LINEAR : VK_FILTER_NEAREST;

    // Derive address modes from sampler state
    uint32_t addressModeU = (samplerState0 >> 2) & 0x7;
    uint32_t addressModeV = (samplerState0 >> 5) & 0x7;
    uint32_t addressModeW = (samplerState0 >> 8) & 0x7;

    auto convertAddressMode = [](uint32_t mode) -> VkSamplerAddressMode {
      switch (mode) {
      case 0:
        return VK_SAMPLER_ADDRESS_MODE_REPEAT;
      case 1:
        return VK_SAMPLER_ADDRESS_MODE_MIRRORED_REPEAT;
      case 2:
        return VK_SAMPLER_ADDRESS_MODE_CLAMP_TO_EDGE;
      case 3:
        return VK_SAMPLER_ADDRESS_MODE_CLAMP_TO_BORDER;
      default:
        return VK_SAMPLER_ADDRESS_MODE_REPEAT;
      }
    };

    samplerInfo.addressModeU = convertAddressMode(addressModeU);
    samplerInfo.addressModeV = convertAddressMode(addressModeV);
    samplerInfo.addressModeW = convertAddressMode(addressModeW);
    // Anisotropy settings
    bool anisotropyEnable = (samplerState1 & 0x1) != 0;
    uint32_t maxAnisotropy = (samplerState1 >> 1) & 0xF;
    samplerInfo.anisotropyEnable = anisotropyEnable ? VK_TRUE : VK_FALSE;
    samplerInfo.maxAnisotropy =
        anisotropyEnable
            ? static_cast<float>(maxAnisotropy > 0 ? maxAnisotropy : 1)
            : 1.0f;

    // Border color
    uint32_t borderColor = (samplerState1 >> 5) & 0x3;
    switch (borderColor) {
    case 0:
      samplerInfo.borderColor = VK_BORDER_COLOR_FLOAT_TRANSPARENT_BLACK;
      break;
    case 1:
      samplerInfo.borderColor = VK_BORDER_COLOR_FLOAT_OPAQUE_BLACK;
      break;
    case 2:
      samplerInfo.borderColor = VK_BORDER_COLOR_FLOAT_OPAQUE_WHITE;
      break;
    default:
      samplerInfo.borderColor = VK_BORDER_COLOR_FLOAT_TRANSPARENT_BLACK;
      break;
    }

    // Normalized coordinates
    bool unnormalizedCoords = (samplerState1 & 0x80) != 0;
    samplerInfo.unnormalizedCoordinates =
        unnormalizedCoords ? VK_TRUE : VK_FALSE;

    // Comparison settings
    bool compareEnable = (samplerState1 & 0x100) != 0;
    uint32_t compareOp = (samplerState1 >> 9) & 0x7;
    samplerInfo.compareEnable = compareEnable ? VK_TRUE : VK_FALSE;
    samplerInfo.compareOp = compareEnable ? static_cast<VkCompareOp>(compareOp)
                                          : VK_COMPARE_OP_ALWAYS;

    // Mipmap settings
    bool linearMipmap = (samplerState1 & 0x1000) != 0;
    samplerInfo.mipmapMode = linearMipmap ? VK_SAMPLER_MIPMAP_MODE_LINEAR
                                          : VK_SAMPLER_MIPMAP_MODE_NEAREST;
    samplerInfo.minLod = 0.0f;
    samplerInfo.maxLod = VK_LOD_CLAMP_NONE;
    samplerInfo.mipLodBias = 0.0f;

    VkSampler sampler;
    if (vkCreateSampler(m_vulkan->device, &samplerInfo, nullptr, &sampler) !=
        VK_SUCCESS) {
      spdlog::error("Failed to create sampler");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan sampler creation failed");
    }

    m_samplers[samplerId] = sampler;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::debug(
        "CreateSampler: Created new sampler, samplerId={:x}, magFilter={}, "
        "minFilter={}, addressU={}, latency={}us",
        samplerId, static_cast<int>(samplerInfo.magFilter),
        static_cast<int>(samplerInfo.minFilter),
        static_cast<int>(samplerInfo.addressModeU),
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count());
    return samplerId;
  } catch (const std::exception &e) {
    spdlog::error("CreateSampler failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}

/**
 * @brief Deletes a sampler.
 * @param samplerId Sampler ID.
 */
void PS4GPU::DeleteSampler(uint64_t samplerId) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    auto it = m_samplers.find(samplerId);
    if (it != m_samplers.end()) {
      if (it->second != VK_NULL_HANDLE) {
        vkDestroySampler(m_vulkan->device, it->second, nullptr);
      }
      m_samplers.erase(it);
      m_stats.cacheHits++;
    } else {
      spdlog::warn("Sampler not found: {}", samplerId);
      m_stats.cacheMisses++;
    }
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("DeleteSampler: samplerId={:x}, latency={}us", samplerId,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("DeleteSampler failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Waits for the GPU to become idle.
 */
void PS4GPU::WaitForGPUIdle() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    if (m_vulkan->device != VK_NULL_HANDLE) {
      vkDeviceWaitIdle(m_vulkan->device);
      m_stats.cacheHits++;
    }
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("WaitForGPUIdle: latency={}us",
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("WaitForGPUIdle failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Inserts a fence.
 * @param fenceValue Output fence value.
 */
void PS4GPU::InsertFence(uint64_t *fenceValue) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    if (!fenceValue) {
      spdlog::error("Invalid fence value pointer");
      m_stats.cacheMisses++;
      throw GPUException("Invalid fence value pointer");
    }
    VkFenceCreateInfo fenceInfo{};
    fenceInfo.sType = VK_STRUCTURE_TYPE_FENCE_CREATE_INFO;
    VkFence fence;
    if (vkCreateFence(m_vulkan->device, &fenceInfo, nullptr, &fence) !=
        VK_SUCCESS) {
      spdlog::error("Failed to create fence");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan fence creation failed");
    }
    *fenceValue = m_nextFenceValue++;
    m_fences[*fenceValue] = fence;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("InsertFence: fenceValue={:x}, latency={}us", *fenceValue,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("InsertFence failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Checks if a fence is signaled.
 * @param fenceValue Fence value.
 * @return True if signaled, false otherwise.
 */
bool PS4GPU::CheckFence(uint64_t fenceValue) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    auto it = m_fences.find(fenceValue);
    if (it == m_fences.end()) {
      spdlog::error("Fence not found: {}", fenceValue);
      m_stats.cacheMisses++;
      return false;
    }
    VkResult result = vkGetFenceStatus(m_vulkan->device, it->second);
    if (result == VK_SUCCESS) {
      vkDestroyFence(m_vulkan->device, it->second, nullptr);
      m_fences.erase(it);
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace("CheckFence: fenceValue={:x}, signaled=true, latency={}us",
                    fenceValue, m_stats.totalLatencyUs.load());
      return true;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("CheckFence: fenceValue={:x}, signaled=false, latency={}us",
                  fenceValue, m_stats.totalLatencyUs.load());
    return false;
  } catch (const std::exception &e) {
    spdlog::error("CheckFence failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Begins GPU profiling.
 * @param label Profile label.
 */
void PS4GPU::BeginGPUProfiler(const std::string &label) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    auto it = m_profileQueryPools.find(label);
    VkQueryPool queryPool;
    if (it == m_profileQueryPools.end()) {
      VkQueryPoolCreateInfo queryPoolInfo{};
      queryPoolInfo.sType = VK_STRUCTURE_TYPE_QUERY_POOL_CREATE_INFO;
      queryPoolInfo.queryType = VK_QUERY_TYPE_TIMESTAMP;
      queryPoolInfo.queryCount = 2;
      if (vkCreateQueryPool(m_vulkan->device, &queryPoolInfo, nullptr,
                            &queryPool) != VK_SUCCESS) {
        spdlog::error("Failed to create query pool");
        m_stats.cacheMisses++;
        throw GPUException("Vulkan query pool creation failed");
      }
      m_profileQueryPools[label] = queryPool;
    } else {
      queryPool = it->second;
    }
    vkCmdResetQueryPool(m_commandBuffer, queryPool, 0, 2);
    vkCmdWriteTimestamp(m_commandBuffer, VK_PIPELINE_STAGE_TOP_OF_PIPE_BIT,
                        queryPool, 0);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("BeginGPUProfiler: label={}, latency={}us", label,
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("BeginGPUProfiler failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Ends GPU profiling.
 */
void PS4GPU::EndGPUProfiler() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    if (!m_profileQueryPools.empty()) {
      auto &queryPool = m_profileQueryPools.begin()->second;
      vkCmdWriteTimestamp(m_commandBuffer, VK_PIPELINE_STAGE_BOTTOM_OF_PIPE_BIT,
                          queryPool, 1);
      m_stats.cacheHits++;
    }
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("EndGPUProfiler: latency={}us",
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("EndGPUProfiler failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Gets profiling results.
 * @return Vector of profiling results.
 */
std::vector<ProfileResult> PS4GPU::GetProfileResults() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    std::vector<ProfileResult> results;
    for (const auto &[label, queryPool] : m_profileQueryPools) {
      uint64_t timestamps[2];
      VkResult result = vkGetQueryPoolResults(
          m_vulkan->device, queryPool, 0, 2, sizeof(timestamps), timestamps,
          sizeof(uint64_t), VK_QUERY_RESULT_64_BIT | VK_QUERY_RESULT_WAIT_BIT);
      if (result == VK_SUCCESS) {
        ProfileResult profileResult;
        profileResult.label = label;
        profileResult.startTimestamp = timestamps[0];
        profileResult.endTimestamp = timestamps[1];
        results.push_back(profileResult);
      }
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("GetProfileResults: results={}, latency={}us", results.size(),
                  m_stats.totalLatencyUs.load());
    return results;
  } catch (const std::exception &e) {
    spdlog::error("GetProfileResults failed: {}", e.what());
    m_stats.cacheMisses++;
    return {};
  }
}

/**
 * @brief Retrieves GPU statistics.
 * @return Current statistics.
 */
GPUStats PS4GPU::GetStats() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return m_stats;
  } catch (const std::exception &e) {
    spdlog::error("GetStats failed: {}", e.what());
    m_stats.cacheMisses++;
    return m_stats;
  }
}

/**
 * @brief Saves the GPU state to a stream.
 * @param out Output stream.
 */
void PS4GPU::SaveState(std::ostream &out) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    uint64_t shaderCount = m_shaderModules.size();
    out.write(reinterpret_cast<const char *>(&shaderCount),
              sizeof(shaderCount));
    for (const auto &[id, shader] : m_shaderModules) {
      out.write(reinterpret_cast<const char *>(&id), sizeof(id));
      uint32_t spirvSize = static_cast<uint32_t>(shader.spirvCode.size());
      out.write(reinterpret_cast<const char *>(&spirvSize), sizeof(spirvSize));
      out.write(reinterpret_cast<const char *>(shader.spirvCode.data()),
                spirvSize * sizeof(uint32_t));
      out.write(reinterpret_cast<const char *>(&shader.type),
                sizeof(shader.type));
    }
    uint64_t targetCount = m_renderTargets.size();
    out.write(reinterpret_cast<const char *>(&targetCount),
              sizeof(targetCount));
    for (const auto &[id, target] : m_renderTargets) {
      out.write(reinterpret_cast<const char *>(&id), sizeof(id));
      out.write(reinterpret_cast<const char *>(&target.surfaceId),
                sizeof(target.surfaceId));
      out.write(reinterpret_cast<const char *>(&target.format),
                sizeof(target.format));
      out.write(reinterpret_cast<const char *>(&target.width),
                sizeof(target.width));
      out.write(reinterpret_cast<const char *>(&target.height),
                sizeof(target.height));
      out.write(reinterpret_cast<const char *>(&target.isDepthStencil),
                sizeof(target.isDepthStencil));
    }
    uint64_t mappingCount = m_memoryMappings.size();
    out.write(reinterpret_cast<const char *>(&mappingCount),
              sizeof(mappingCount));
    for (const auto &[id, mapping] : m_memoryMappings) {
      out.write(reinterpret_cast<const char *>(&id), sizeof(id));
      out.write(reinterpret_cast<const char *>(&mapping.gpuAddress),
                sizeof(mapping.gpuAddress));
      out.write(reinterpret_cast<const char *>(&mapping.cpuAddress),
                sizeof(mapping.cpuAddress));
      out.write(reinterpret_cast<const char *>(&mapping.size),
                sizeof(mapping.size));
    }
    out.write(reinterpret_cast<const char *>(&m_currentFrame),
              sizeof(m_currentFrame));
    out.write(reinterpret_cast<const char *>(&m_currentSwapchainImageIndex),
              sizeof(m_currentSwapchainImageIndex));
    out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));
    if (!out.good()) {
      throw std::runtime_error("Failed to write GPU state");
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("SaveState: Saved GPU state, shaders={}, targets={}, "
                 "mappings={}, latency={}us",
                 shaderCount, targetCount, mappingCount,
                 m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("SaveState failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Loads the GPU state from a stream.
 * @param in Input stream.
 */
void PS4GPU::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      spdlog::error("Unsupported GPU state version: {}", version);
      throw std::runtime_error("Invalid GPU state version");
    }
    m_shaderModules.clear();
    uint64_t shaderCount;
    in.read(reinterpret_cast<char *>(&shaderCount), sizeof(shaderCount));
    for (uint64_t i = 0; i < shaderCount && in.good(); ++i) {
      uint64_t id;
      in.read(reinterpret_cast<char *>(&id), sizeof(id));
      uint32_t spirvSize;
      in.read(reinterpret_cast<char *>(&spirvSize), sizeof(spirvSize));
      std::vector<uint32_t> spirvCode(spirvSize);
      in.read(reinterpret_cast<char *>(spirvCode.data()),
              spirvSize * sizeof(uint32_t));
      ShaderModule shader;
      in.read(reinterpret_cast<char *>(&shader.type), sizeof(shader.type));
      shader.spirvCode = spirvCode;
      VkShaderModuleCreateInfo createInfo{};
      createInfo.sType = VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO;
      createInfo.codeSize = spirvCode.size() * sizeof(uint32_t);
      createInfo.pCode = spirvCode.data();
      if (vkCreateShaderModule(m_vulkan->device, &createInfo, nullptr,
                               &shader.module) != VK_SUCCESS) {
        spdlog::error("Failed to recreate shader module during state load");
        throw GPUException("Vulkan shader module creation failed");
      }
      m_shaderModules[id] = shader;
    }
    m_renderTargets.clear();
    uint64_t targetCount;
    in.read(reinterpret_cast<char *>(&targetCount), sizeof(targetCount));
    for (uint64_t i = 0; i < targetCount && in.good(); ++i) {
      uint64_t id;
      in.read(reinterpret_cast<char *>(&id), sizeof(id));
      RenderTarget target;
      in.read(reinterpret_cast<char *>(&target.surfaceId),
              sizeof(target.surfaceId));
      in.read(reinterpret_cast<char *>(&target.format), sizeof(target.format));
      in.read(reinterpret_cast<char *>(&target.width), sizeof(target.width));
      in.read(reinterpret_cast<char *>(&target.height), sizeof(target.height));
      in.read(reinterpret_cast<char *>(&target.isDepthStencil),
              sizeof(target.isDepthStencil));
      // Recreate image and image view
      VkImageCreateInfo imageInfo{};
      imageInfo.sType = VK_STRUCTURE_TYPE_IMAGE_CREATE_INFO;
      imageInfo.imageType = VK_IMAGE_TYPE_2D;
      imageInfo.format = target.format;
      imageInfo.extent.width = target.width;
      imageInfo.extent.height = target.height;
      imageInfo.extent.depth = 1;
      imageInfo.mipLevels = 1;
      imageInfo.arrayLayers = 1;
      imageInfo.samples = VK_SAMPLE_COUNT_1_BIT;
      imageInfo.tiling = VK_IMAGE_TILING_OPTIMAL;
      imageInfo.usage = target.isDepthStencil
                            ? VK_IMAGE_USAGE_DEPTH_STENCIL_ATTACHMENT_BIT
                            : VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT |
                                  VK_IMAGE_USAGE_SAMPLED_BIT;
      imageInfo.sharingMode = VK_SHARING_MODE_EXCLUSIVE;
      imageInfo.initialLayout = VK_IMAGE_LAYOUT_UNDEFINED;
      if (vkCreateImage(m_vulkan->device, &imageInfo, nullptr, &target.image) !=
          VK_SUCCESS) {
        spdlog::error(
            "Failed to recreate render target image during state load");
        throw GPUException("Vulkan image creation failed");
      }
      VkMemoryRequirements memRequirements;
      vkGetImageMemoryRequirements(m_vulkan->device, target.image,
                                   &memRequirements);
      VkMemoryAllocateInfo allocInfo{};
      allocInfo.sType = VK_STRUCTURE_TYPE_MEMORY_ALLOCATE_INFO;
      allocInfo.allocationSize = memRequirements.size;
      allocInfo.memoryTypeIndex = FindMemoryType(
          memRequirements.memoryTypeBits, VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT);
      if (vkAllocateMemory(m_vulkan->device, &allocInfo, nullptr,
                           &target.memory) != VK_SUCCESS) {
        spdlog::error(
            "Failed to allocate render target memory during state load");
        vkDestroyImage(m_vulkan->device, target.image, nullptr);
        throw GPUException("Vulkan memory allocation failed");
      }
      vkBindImageMemory(m_vulkan->device, target.image, target.memory, 0);
      VkImageViewCreateInfo viewInfo{};
      viewInfo.sType = VK_STRUCTURE_TYPE_IMAGE_VIEW_CREATE_INFO;
      viewInfo.image = target.image;
      viewInfo.viewType = VK_IMAGE_VIEW_TYPE_2D;
      viewInfo.format = target.format;
      viewInfo.subresourceRange.aspectMask =
          target.isDepthStencil
              ? VK_IMAGE_ASPECT_DEPTH_BIT | VK_IMAGE_ASPECT_STENCIL_BIT
              : VK_IMAGE_ASPECT_COLOR_BIT;
      viewInfo.subresourceRange.baseMipLevel = 0;
      viewInfo.subresourceRange.levelCount = 1;
      viewInfo.subresourceRange.baseArrayLayer = 0;
      viewInfo.subresourceRange.layerCount = 1;
      if (vkCreateImageView(m_vulkan->device, &viewInfo, nullptr,
                            &target.view) != VK_SUCCESS) {
        spdlog::error(
            "Failed to recreate render target view during state load");
        vkDestroyImage(m_vulkan->device, target.image, nullptr);
        vkFreeMemory(m_vulkan->device, target.memory, nullptr);
        throw GPUException("Vulkan image view creation failed");
      }
      m_renderTargets[id] = target;
      m_stats.vramUsage += memRequirements.size;
    }
    m_memoryMappings.clear();
    uint64_t mappingCount;
    in.read(reinterpret_cast<char *>(&mappingCount), sizeof(mappingCount));
    for (uint64_t i = 0; i < mappingCount && in.good(); ++i) {
      uint64_t id;
      in.read(reinterpret_cast<char *>(&id), sizeof(id));
      MemoryMapping mapping;
      in.read(reinterpret_cast<char *>(&mapping.gpuAddress),
              sizeof(mapping.gpuAddress));
      in.read(reinterpret_cast<char *>(&mapping.cpuAddress),
              sizeof(mapping.cpuAddress));
      in.read(reinterpret_cast<char *>(&mapping.size), sizeof(mapping.size));
      mapping.buffer = CreateBuffer(mapping.size,
                                    VK_BUFFER_USAGE_TRANSFER_SRC_BIT |
                                        VK_BUFFER_USAGE_TRANSFER_DST_BIT,
                                    VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT |
                                        VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
                                    mapping.memory);
      if (vkMapMemory(m_vulkan->device, mapping.memory, 0, mapping.size, 0,
                      &mapping.mappedData) != VK_SUCCESS) {
        spdlog::error("Failed to map memory during state load");
        vkDestroyBuffer(m_vulkan->device, mapping.buffer, nullptr);
        vkFreeMemory(m_vulkan->device, mapping.memory, nullptr);
        throw GPUException("Vulkan memory mapping failed");
      }
      m_memoryMappings[id] = mapping;
      m_stats.vramUsage += mapping.size;
    }
    in.read(reinterpret_cast<char *>(&m_currentFrame), sizeof(m_currentFrame));
    in.read(reinterpret_cast<char *>(&m_currentSwapchainImageIndex),
            sizeof(m_currentSwapchainImageIndex));
    in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));
    if (!in.good()) {
      throw std::runtime_error("Failed to read GPU state");
    }
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("LoadState: Loaded GPU state, shaders={}, targets={}, "
                 "mappings={}, latency={}us",
                 shaderCount, targetCount, mappingCount,
                 m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("LoadState failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Creates the swapchain.
 * @return True on success, false otherwise.
 */
bool PS4GPU::CreateSwapchain() {
  auto start = std::chrono::steady_clock::now();
  // Note: No mutex lock needed here as this is only called from Initialize()
  // which already holds the lock
  try {
    spdlog::info("Creating swapchain...");

    // Validate Vulkan context and window
    if (!m_vulkan || !m_vulkan->instance || !m_vulkan->physicalDevice ||
        !m_window) {
      spdlog::error("Invalid Vulkan context or window");
      m_stats.cacheMisses++;
      throw GPUException("Invalid Vulkan context or window for swapchain");
    }

    // RACE CONDITION FIX: Synchronize access to VulkanContext swapchain
    std::unique_lock<std::shared_mutex> vulkanLock(m_vulkan->contextMutex);

    // Check if swapchain already exists (created by main.cpp)
    if (m_vulkan->swapchain != VK_NULL_HANDLE) {
      spdlog::info(
          "Swapchain already exists, using existing swapchain from main.cpp");

      // Get swapchain images from existing swapchain
      uint32_t imageCount;
      if (vkGetSwapchainImagesKHR(m_vulkan->device, m_vulkan->swapchain,
                                  &imageCount, nullptr) != VK_SUCCESS) {
        spdlog::error("Failed to get swapchain image count");
        m_stats.cacheMisses++;
        throw GPUException("Failed to get swapchain image count");
      }

      m_vulkan->swapchainImages.resize(imageCount);
      if (vkGetSwapchainImagesKHR(
              m_vulkan->device, m_vulkan->swapchain, &imageCount,
              m_vulkan->swapchainImages.data()) != VK_SUCCESS) {
        spdlog::error("Failed to get swapchain images");
        m_stats.cacheMisses++;
        throw GPUException("Failed to get swapchain images");
      }

      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::info("Using existing swapchain with {} images, latency={}us",
                   imageCount, m_stats.totalLatencyUs.load());
      return true;
    }

    // Create surface if not already created
    if (m_vulkan->surface == VK_NULL_HANDLE) {
      if (!SDL_Vulkan_CreateSurface(m_window, m_vulkan->instance,
                                    &m_vulkan->surface)) {
        spdlog::error("Failed to create Vulkan surface: {}", SDL_GetError());
        m_stats.cacheMisses++;
        throw GPUException("Vulkan surface creation failed");
      }
      spdlog::info("Created Vulkan surface");
    }

    // Query surface capabilities
    VkSurfaceCapabilitiesKHR capabilities;
    if (vkGetPhysicalDeviceSurfaceCapabilitiesKHR(
            m_vulkan->physicalDevice, m_vulkan->surface, &capabilities) !=
        VK_SUCCESS) {
      spdlog::error("Failed to query surface capabilities");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan surface capabilities query failed");
    }

    // Query surface formats
    uint32_t formatCount;
    vkGetPhysicalDeviceSurfaceFormatsKHR(
        m_vulkan->physicalDevice, m_vulkan->surface, &formatCount, nullptr);
    std::vector<VkSurfaceFormatKHR> formats(formatCount);
    vkGetPhysicalDeviceSurfaceFormatsKHR(m_vulkan->physicalDevice,
                                         m_vulkan->surface, &formatCount,
                                         formats.data());
    if (formats.empty()) {
      spdlog::error("No surface formats available");
      m_stats.cacheMisses++;
      throw GPUException("No Vulkan surface formats available");
    }
    VkSurfaceFormatKHR selectedFormat = formats[0];
    for (const auto &fmt : formats) {
      if (fmt.format == VK_FORMAT_B8G8R8A8_SRGB &&
          fmt.colorSpace == VK_COLOR_SPACE_SRGB_NONLINEAR_KHR) {
        selectedFormat = fmt;
        break;
      }
    }

    // Query present modes
    uint32_t presentModeCount;
    vkGetPhysicalDeviceSurfacePresentModesKHR(m_vulkan->physicalDevice,
                                              m_vulkan->surface,
                                              &presentModeCount, nullptr);
    std::vector<VkPresentModeKHR> presentModes(presentModeCount);
    vkGetPhysicalDeviceSurfacePresentModesKHR(
        m_vulkan->physicalDevice, m_vulkan->surface, &presentModeCount,
        presentModes.data());
    VkPresentModeKHR selectedPresentMode = VK_PRESENT_MODE_FIFO_KHR;
    for (const auto &mode : presentModes) {
      if (mode == VK_PRESENT_MODE_MAILBOX_KHR) {
        selectedPresentMode = mode;
        break;
      }
    }

    // Determine swapchain extent
    VkExtent2D extent;
    if (capabilities.currentExtent.width != UINT32_MAX) {
      extent = capabilities.currentExtent;
    } else {
      int width, height;
      SDL_Vulkan_GetDrawableSize(m_window, &width, &height);
      extent = {
          static_cast<uint32_t>(std::clamp(
              width, static_cast<int>(capabilities.minImageExtent.width),
              static_cast<int>(capabilities.maxImageExtent.width))),
          static_cast<uint32_t>(std::clamp(
              height, static_cast<int>(capabilities.minImageExtent.height),
              static_cast<int>(capabilities.maxImageExtent.height)))};
    }

    // Determine image count
    uint32_t imageCount = capabilities.minImageCount + 1;
    if (capabilities.maxImageCount > 0 &&
        imageCount > capabilities.maxImageCount) {
      imageCount = capabilities.maxImageCount;
    }

    // Verify queue families
    if (m_vulkan->graphicsQueueFamily == UINT32_MAX ||
        m_vulkan->presentQueueFamily == UINT32_MAX) {
      spdlog::error("Invalid queue families: graphics={}, present={}",
                    m_vulkan->graphicsQueueFamily,
                    m_vulkan->presentQueueFamily);
      m_stats.cacheMisses++;
      throw GPUException("Invalid Vulkan queue families");
    }

    // Create swapchain
    VkSwapchainCreateInfoKHR createInfo{};
    createInfo.sType = VK_STRUCTURE_TYPE_SWAPCHAIN_CREATE_INFO_KHR;
    createInfo.surface = m_vulkan->surface;
    createInfo.minImageCount = imageCount;
    createInfo.imageFormat = selectedFormat.format;
    createInfo.imageColorSpace = selectedFormat.colorSpace;
    createInfo.imageExtent = extent;
    createInfo.imageArrayLayers = 1;
    createInfo.imageUsage = VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT;
    uint32_t queueFamilyIndices[] = {m_vulkan->graphicsQueueFamily,
                                     m_vulkan->presentQueueFamily};
    if (m_vulkan->graphicsQueueFamily != m_vulkan->presentQueueFamily) {
      createInfo.imageSharingMode = VK_SHARING_MODE_CONCURRENT;
      createInfo.queueFamilyIndexCount = 2;
      createInfo.pQueueFamilyIndices = queueFamilyIndices;
    } else {
      createInfo.imageSharingMode = VK_SHARING_MODE_EXCLUSIVE;
    }
    createInfo.preTransform = capabilities.currentTransform;
    createInfo.compositeAlpha = VK_COMPOSITE_ALPHA_OPAQUE_BIT_KHR;
    createInfo.presentMode = selectedPresentMode;
    createInfo.clipped = VK_TRUE;
    createInfo.oldSwapchain = VK_NULL_HANDLE;

    if (vkCreateSwapchainKHR(m_vulkan->device, &createInfo, nullptr,
                             &m_vulkan->swapchain) != VK_SUCCESS) {
      spdlog::error("Failed to create Vulkan swapchain");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan swapchain creation failed");
    }

    // Retrieve swapchain images
    vkGetSwapchainImagesKHR(m_vulkan->device, m_vulkan->swapchain, &imageCount,
                            nullptr);
    m_vulkan->swapchainImages.resize(imageCount);
    vkGetSwapchainImagesKHR(m_vulkan->device, m_vulkan->swapchain, &imageCount,
                            m_vulkan->swapchainImages.data());

    m_vulkan->swapchainImageFormat = selectedFormat.format;
    m_vulkan->swapchainExtent = extent;

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info(
        "CreateSwapchain: Created swapchain, extent={}x{}, imageCount={}, "
        "format={}, colorSpace={}, presentMode={}, latency={}us",
        extent.width, extent.height, imageCount,
        static_cast<int>(selectedFormat.format),
        static_cast<int>(selectedFormat.colorSpace),
        static_cast<int>(selectedPresentMode), m_stats.totalLatencyUs.load());
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateSwapchain failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Creates swapchain image views.
 * @return True on success, false otherwise.
 */
bool PS4GPU::CreateSwapchainImageViews() {
  auto start = std::chrono::steady_clock::now();
  // Note: No mutex lock needed here as this is only called from Initialize()
  // which already holds the lock
  try {
    m_vulkan->swapchainImageViews.resize(m_vulkan->swapchainImages.size());
    for (size_t i = 0; i < m_vulkan->swapchainImages.size(); ++i) {
      VkImageViewCreateInfo createInfo{};
      createInfo.sType = VK_STRUCTURE_TYPE_IMAGE_VIEW_CREATE_INFO;
      createInfo.image = m_vulkan->swapchainImages[i];
      createInfo.viewType = VK_IMAGE_VIEW_TYPE_2D;
      createInfo.format = m_vulkan->swapchainImageFormat;
      createInfo.subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
      createInfo.subresourceRange.baseMipLevel = 0;
      createInfo.subresourceRange.levelCount = 1;
      createInfo.subresourceRange.baseArrayLayer = 0;
      createInfo.subresourceRange.layerCount = 1;
      if (vkCreateImageView(m_vulkan->device, &createInfo, nullptr,
                            &m_vulkan->swapchainImageViews[i]) != VK_SUCCESS) {
        spdlog::error("Failed to create swapchain image view {}", i);
        m_stats.cacheMisses++;
        throw GPUException("Vulkan image view creation failed");
      }
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info(
        "CreateSwapchainImageViews: Created {} image views, latency={}us",
        m_vulkan->swapchainImageViews.size(), m_stats.totalLatencyUs.load());
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateSwapchainImageViews failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Creates the command pool.
 * @return True on success, false otherwise.
 */
bool PS4GPU::CreateCommandPool() {
  auto start = std::chrono::steady_clock::now();
  // Note: No mutex lock needed here as this is only called from Initialize()
  // which already holds the lock
  try {
    VkCommandPoolCreateInfo poolInfo{};
    poolInfo.sType = VK_STRUCTURE_TYPE_COMMAND_POOL_CREATE_INFO;
    poolInfo.flags = VK_COMMAND_POOL_CREATE_RESET_COMMAND_BUFFER_BIT;
    poolInfo.queueFamilyIndex = m_vulkan->graphicsQueueFamily;
    if (vkCreateCommandPool(m_vulkan->device, &poolInfo, nullptr,
                            &m_vulkan->commandPool) != VK_SUCCESS) {
      spdlog::error("Failed to create command pool");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan command pool creation failed");
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("CreateCommandPool: Created command pool, latency={}us",
                 m_stats.totalLatencyUs.load());
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateCommandPool failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Creates the descriptor pool.
 * @return True on success, false otherwise.
 */
bool PS4GPU::CreateDescriptorPool() {
  auto start = std::chrono::steady_clock::now();
  // Note: No mutex lock needed here as this is only called from Initialize()
  // which already holds the lock
  try {
    VkDescriptorPoolSize poolSizes[] = {
        {VK_DESCRIPTOR_TYPE_SAMPLER, 1000},
        {VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 1000},
        {VK_DESCRIPTOR_TYPE_SAMPLED_IMAGE, 1000},
        {VK_DESCRIPTOR_TYPE_STORAGE_IMAGE, 1000},
        {VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 1000},
        {VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 1000},
    };
    VkDescriptorPoolCreateInfo poolInfo{};
    poolInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_POOL_CREATE_INFO;
    poolInfo.flags = VK_DESCRIPTOR_POOL_CREATE_FREE_DESCRIPTOR_SET_BIT;
    poolInfo.maxSets = 1000;
    poolInfo.poolSizeCount = std::size(poolSizes);
    poolInfo.pPoolSizes = poolSizes;
    if (vkCreateDescriptorPool(m_vulkan->device, &poolInfo, nullptr,
                               &m_vulkan->descriptorPool) != VK_SUCCESS) {
      spdlog::error("Failed to create descriptor pool");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan descriptor pool creation failed");
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("CreateDescriptorPool: Created descriptor pool, latency={}us",
                 m_stats.totalLatencyUs.load());
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateDescriptorPool failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Creates synchronization objects.
 * @return True on success, false otherwise.
 */
bool PS4GPU::CreateSyncObjects() {
  auto start = std::chrono::steady_clock::now();
  // Note: No mutex lock needed here as this is only called from Initialize()
  // which already holds the lock
  try {
    VkSemaphoreCreateInfo semaphoreInfo{};
    semaphoreInfo.sType = VK_STRUCTURE_TYPE_SEMAPHORE_CREATE_INFO;
    VkFenceCreateInfo fenceInfo{};
    fenceInfo.sType = VK_STRUCTURE_TYPE_FENCE_CREATE_INFO;
    fenceInfo.flags = VK_FENCE_CREATE_SIGNALED_BIT;
    if (vkCreateSemaphore(m_vulkan->device, &semaphoreInfo, nullptr,
                          &m_vulkan->imageAvailableSemaphore) != VK_SUCCESS ||
        vkCreateSemaphore(m_vulkan->device, &semaphoreInfo, nullptr,
                          &m_vulkan->renderFinishedSemaphore) != VK_SUCCESS ||
        vkCreateFence(m_vulkan->device, &fenceInfo, nullptr,
                      &m_vulkan->inFlightFence) != VK_SUCCESS) {
      spdlog::error("Failed to create synchronization objects");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan synchronization object creation failed");
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info(
        "CreateSyncObjects: Created synchronization objects, latency={}us",
        m_stats.totalLatencyUs.load());
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateSyncObjects failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Creates the default render pass.
 * @return True on success, false otherwise.
 */
bool PS4GPU::CreateDefaultRenderPass() {
  auto start = std::chrono::steady_clock::now();
  // Note: No mutex lock needed here as this is only called from Initialize()
  // which already holds the lock
  try {
    VkAttachmentDescription colorAttachment{};
    colorAttachment.format = m_vulkan->swapchainImageFormat;
    colorAttachment.samples = VK_SAMPLE_COUNT_1_BIT;
    colorAttachment.loadOp = VK_ATTACHMENT_LOAD_OP_CLEAR;
    colorAttachment.storeOp = VK_ATTACHMENT_STORE_OP_STORE;
    colorAttachment.stencilLoadOp = VK_ATTACHMENT_LOAD_OP_DONT_CARE;
    colorAttachment.stencilStoreOp = VK_ATTACHMENT_STORE_OP_DONT_CARE;
    colorAttachment.initialLayout = VK_IMAGE_LAYOUT_UNDEFINED;
    colorAttachment.finalLayout = VK_IMAGE_LAYOUT_PRESENT_SRC_KHR;
    VkAttachmentReference colorAttachmentRef{};
    colorAttachmentRef.attachment = 0;
    colorAttachmentRef.layout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;
    VkSubpassDescription subpass{};
    subpass.pipelineBindPoint = VK_PIPELINE_BIND_POINT_GRAPHICS;
    subpass.colorAttachmentCount = 1;
    subpass.pColorAttachments = &colorAttachmentRef;
    VkSubpassDependency dependency{};
    dependency.srcSubpass = VK_SUBPASS_EXTERNAL;
    dependency.dstSubpass = 0;
    dependency.srcStageMask = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
    dependency.srcAccessMask = 0;
    dependency.dstStageMask = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
    dependency.dstAccessMask = VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT;
    VkRenderPassCreateInfo renderPassInfo{};
    renderPassInfo.sType = VK_STRUCTURE_TYPE_RENDER_PASS_CREATE_INFO;
    renderPassInfo.attachmentCount = 1;
    renderPassInfo.pAttachments = &colorAttachment;
    renderPassInfo.subpassCount = 1;
    renderPassInfo.pSubpasses = &subpass;
    renderPassInfo.dependencyCount = 1;
    renderPassInfo.pDependencies = &dependency;
    VkRenderPass renderPass;
    if (vkCreateRenderPass(m_vulkan->device, &renderPassInfo, nullptr,
                           &renderPass) != VK_SUCCESS) {
      spdlog::error("Failed to create default render pass");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan render pass creation failed");
    }
    m_renderPassCache[RenderPassKey{}] = renderPass;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info(
        "CreateDefaultRenderPass: Created default render pass, latency={}us",
        m_stats.totalLatencyUs.load());
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateDefaultRenderPass failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Creates framebuffers.
 * @return True on success, false otherwise.
 */
bool PS4GPU::CreateFramebuffers() {
  auto start = std::chrono::steady_clock::now();
  // Note: No mutex lock needed here as this is only called from Initialize()
  // which already holds the lock
  try {
    for (size_t i = 0; i < m_vulkan->swapchainImageViews.size(); ++i) {
      VkImageView attachments[] = {m_vulkan->swapchainImageViews[i]};
      VkFramebufferCreateInfo framebufferInfo{};
      framebufferInfo.sType = VK_STRUCTURE_TYPE_FRAMEBUFFER_CREATE_INFO;
      framebufferInfo.renderPass = m_renderPassCache[RenderPassKey{}];
      framebufferInfo.attachmentCount = 1;
      framebufferInfo.pAttachments = attachments;
      framebufferInfo.width = m_vulkan->swapchainExtent.width;
      framebufferInfo.height = m_vulkan->swapchainExtent.height;
      framebufferInfo.layers = 1;
      VkFramebuffer framebuffer;
      if (vkCreateFramebuffer(m_vulkan->device, &framebufferInfo, nullptr,
                              &framebuffer) != VK_SUCCESS) {
        spdlog::error("Failed to create framebuffer {}", i);
        m_stats.cacheMisses++;
        throw GPUException("Vulkan framebuffer creation failed");
      }
      m_framebufferCache[i] = framebuffer;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("CreateFramebuffers: Created {} framebuffers, latency={}us",
                 m_vulkan->swapchainImageViews.size(),
                 m_stats.totalLatencyUs.load());
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateFramebuffers failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Builds graphics pipeline key from current GNM register state.
 * @return Graphics pipeline key.
 */
GraphicsPipelineKey PS4GPU::BuildGraphicsPipelineKey() {
  GraphicsPipelineKey key{};

  try {
    // Get current shader addresses from GNM state
    // For now, use shader base addresses as shader IDs
    uint32_t vsAddress = m_gnmState.GetShaderBase(0); // VS = stage 0
    uint32_t psAddress = m_gnmState.GetShaderBase(1); // PS = stage 1

    // Set shader IDs (use addresses as IDs for now)
    key.vsShaderId = vsAddress;
    key.psShaderId = psAddress;

    // Get primitive topology from GNM state
    uint32_t primitiveTypeReg =
        m_gnmState.GetContextRegister(0x202); // VGT_PRIMITIVE_TYPE
    switch (primitiveTypeReg & 0x3F) { // Lower 6 bits define primitive type
    case 0x00:
      key.topology = VK_PRIMITIVE_TOPOLOGY_POINT_LIST;
      break;
    case 0x01:
      key.topology = VK_PRIMITIVE_TOPOLOGY_LINE_LIST;
      break;
    case 0x02:
      key.topology = VK_PRIMITIVE_TOPOLOGY_LINE_STRIP;
      break;
    case 0x03:
      key.topology = VK_PRIMITIVE_TOPOLOGY_TRIANGLE_LIST;
      break;
    case 0x04:
      key.topology = VK_PRIMITIVE_TOPOLOGY_TRIANGLE_STRIP;
      break;
    case 0x05:
      key.topology = VK_PRIMITIVE_TOPOLOGY_TRIANGLE_FAN;
      break;
    default:
      key.topology = VK_PRIMITIVE_TOPOLOGY_TRIANGLE_LIST;
      break;
    }

    // Get render pass from current render targets
    std::vector<VkFormat> colorFormats;
    for (uint32_t i = 0; i < 8; ++i) {
      uint32_t rtAddress = m_gnmState.GetRenderTarget(i);
      if (rtAddress != 0) {
        colorFormats.push_back(VK_FORMAT_B8G8R8A8_UNORM); // Default format
      }
    }

    // Create render pass key and get/create the render pass
    RenderPassKey rpKey{};
    rpKey.colorFormats = colorFormats;
    rpKey.depthFormat = VK_FORMAT_D32_SFLOAT_S8_UINT; // Default depth format

    key.renderPass = GetOrCreateRenderPass(rpKey);
    key.subpassIndex = 0; // Default to first subpass

    // Create hash values for pipeline state (simplified approach)
    // In a real implementation, these would be computed from actual state
    uint32_t paSuScModeReg =
        m_gnmState.GetContextRegister(0x200); // PA_SU_SC_MODE_CNTL
    uint32_t paSuVtxCntlReg =
        m_gnmState.GetContextRegister(0x201); // PA_SU_VTX_CNTL
    uint32_t cbColorControlReg =
        m_gnmState.GetContextRegister(0x108); // CB_COLOR_CONTROL
    uint32_t cbBlendReg =
        m_gnmState.GetContextRegister(0x110); // CB_BLEND0_CONTROL

    // Create simple hashes from register values
    key.rasterizerStateHash = paSuScModeReg ^ paSuVtxCntlReg;
    key.blendStateHash = cbColorControlReg ^ cbBlendReg;
    key.depthStencilStateHash = 0; // Default for now
    key.vertexInputHash = 0;       // Default for now

    spdlog::trace("Built graphics pipeline key: VS=0x{:x}, PS=0x{:x}, "
                  "topology={}, renderPass={}",
                  key.vsShaderId, key.psShaderId,
                  static_cast<int>(key.topology),
                  reinterpret_cast<uintptr_t>(key.renderPass));

  } catch (const std::exception &e) {
    spdlog::error("BuildGraphicsPipelineKey failed: {}", e.what());
    // Return a default key on failure
  }

  return key;
}

/**
 * @brief Gets or creates a graphics pipeline.
 * @return Pipeline handle.
 */
VkPipeline PS4GPU::GetOrCreateGraphicsPipeline() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    // Build pipeline key from current GNM register state
    GraphicsPipelineKey key = BuildGraphicsPipelineKey();

    // Check cache first
    auto it = m_graphicsPipelineCache.find(key);
    if (it != m_graphicsPipelineCache.end()) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace(
          "GetOrCreateGraphicsPipeline: Cache hit, latency={}us",
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count());
      return it->second;
    }
    VkPipelineShaderStageCreateInfo shaderStages[2] = {};
    auto vsIt = m_shaderModules.find(key.vsShaderId);
    auto psIt = m_shaderModules.find(key.psShaderId);
    if (vsIt == m_shaderModules.end() || psIt == m_shaderModules.end()) {
      spdlog::error("Shader modules not found for pipeline");
      m_stats.cacheMisses++;
      throw GPUException("Invalid shader modules");
    }
    shaderStages[0].sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
    shaderStages[0].stage = VK_SHADER_STAGE_VERTEX_BIT;
    shaderStages[0].module = vsIt->second.module;
    shaderStages[0].pName = "main";
    shaderStages[1].sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
    shaderStages[1].stage = VK_SHADER_STAGE_FRAGMENT_BIT;
    shaderStages[1].module = psIt->second.module;
    shaderStages[1].pName = "main";
    VkPipelineVertexInputStateCreateInfo vertexInputInfo{};
    vertexInputInfo.sType =
        VK_STRUCTURE_TYPE_PIPELINE_VERTEX_INPUT_STATE_CREATE_INFO;
    VkPipelineInputAssemblyStateCreateInfo inputAssembly{};
    inputAssembly.sType =
        VK_STRUCTURE_TYPE_PIPELINE_INPUT_ASSEMBLY_STATE_CREATE_INFO;
    inputAssembly.topology = key.topology;
    inputAssembly.primitiveRestartEnable = VK_FALSE;
    VkPipelineViewportStateCreateInfo viewportState{};
    viewportState.sType = VK_STRUCTURE_TYPE_PIPELINE_VIEWPORT_STATE_CREATE_INFO;
    viewportState.viewportCount = 1;
    viewportState.scissorCount = 1;
    VkPipelineRasterizationStateCreateInfo rasterizer{};
    rasterizer.sType =
        VK_STRUCTURE_TYPE_PIPELINE_RASTERIZATION_STATE_CREATE_INFO;
    rasterizer.depthClampEnable = VK_FALSE;
    rasterizer.rasterizerDiscardEnable = VK_FALSE;

    // Derive rasterizer state from hash (simplified approach)
    uint32_t rasterizerHash = key.rasterizerStateHash;
    bool wireframeMode = (rasterizerHash & 0x4) != 0;
    rasterizer.polygonMode =
        wireframeMode ? VK_POLYGON_MODE_LINE : VK_POLYGON_MODE_FILL;
    rasterizer.lineWidth = 1.0f;

    uint32_t cullMode = (rasterizerHash >> 1) & 0x3;
    switch (cullMode) {
    case 0:
      rasterizer.cullMode = VK_CULL_MODE_NONE;
      break;
    case 1:
      rasterizer.cullMode = VK_CULL_MODE_FRONT_BIT;
      break;
    case 2:
      rasterizer.cullMode = VK_CULL_MODE_BACK_BIT;
      break;
    case 3:
      rasterizer.cullMode = VK_CULL_MODE_FRONT_AND_BACK;
      break;
    default:
      rasterizer.cullMode = VK_CULL_MODE_BACK_BIT;
      break;
    }

    bool frontCcw = (rasterizerHash & 0x1) != 0;
    rasterizer.frontFace =
        frontCcw ? VK_FRONT_FACE_COUNTER_CLOCKWISE : VK_FRONT_FACE_CLOCKWISE;
    VkPipelineMultisampleStateCreateInfo multisampling{};
    multisampling.sType =
        VK_STRUCTURE_TYPE_PIPELINE_MULTISAMPLE_STATE_CREATE_INFO;
    multisampling.sampleShadingEnable = VK_FALSE;
    multisampling.rasterizationSamples = VK_SAMPLE_COUNT_1_BIT;
    VkPipelineColorBlendAttachmentState colorBlendAttachment{};
    colorBlendAttachment.colorWriteMask =
        VK_COLOR_COMPONENT_R_BIT | VK_COLOR_COMPONENT_G_BIT |
        VK_COLOR_COMPONENT_B_BIT | VK_COLOR_COMPONENT_A_BIT;

    // Derive blend state from hash (simplified approach)
    uint32_t blendHash = key.blendStateHash;
    bool blendEnable = (blendHash & 0x1) != 0;
    colorBlendAttachment.blendEnable = blendEnable ? VK_TRUE : VK_FALSE;

    if (blendEnable) {
      // Extract blend factors from hash (simplified mapping)
      colorBlendAttachment.srcColorBlendFactor =
          static_cast<VkBlendFactor>((blendHash >> 16) & 0x1F);
      colorBlendAttachment.dstColorBlendFactor =
          static_cast<VkBlendFactor>((blendHash >> 21) & 0x1F);
      colorBlendAttachment.colorBlendOp =
          static_cast<VkBlendOp>((blendHash >> 26) & 0x7);
      colorBlendAttachment.srcAlphaBlendFactor =
          static_cast<VkBlendFactor>((blendHash >> 8) & 0x1F);
      colorBlendAttachment.dstAlphaBlendFactor =
          static_cast<VkBlendFactor>((blendHash >> 13) & 0x1F);
      colorBlendAttachment.alphaBlendOp =
          static_cast<VkBlendOp>((blendHash >> 29) & 0x7);

      // Clamp to valid ranges
      if (colorBlendAttachment.srcColorBlendFactor >
          VK_BLEND_FACTOR_ONE_MINUS_SRC1_ALPHA) {
        colorBlendAttachment.srcColorBlendFactor = VK_BLEND_FACTOR_SRC_ALPHA;
      }
      if (colorBlendAttachment.dstColorBlendFactor >
          VK_BLEND_FACTOR_ONE_MINUS_SRC1_ALPHA) {
        colorBlendAttachment.dstColorBlendFactor =
            VK_BLEND_FACTOR_ONE_MINUS_SRC_ALPHA;
      }
      if (colorBlendAttachment.colorBlendOp > VK_BLEND_OP_MAX) {
        colorBlendAttachment.colorBlendOp = VK_BLEND_OP_ADD;
      }
      if (colorBlendAttachment.srcAlphaBlendFactor >
          VK_BLEND_FACTOR_ONE_MINUS_SRC1_ALPHA) {
        colorBlendAttachment.srcAlphaBlendFactor = VK_BLEND_FACTOR_SRC_ALPHA;
      }
      if (colorBlendAttachment.dstAlphaBlendFactor >
          VK_BLEND_FACTOR_ONE_MINUS_SRC1_ALPHA) {
        colorBlendAttachment.dstAlphaBlendFactor =
            VK_BLEND_FACTOR_ONE_MINUS_SRC_ALPHA;
      }
      if (colorBlendAttachment.alphaBlendOp > VK_BLEND_OP_MAX) {
        colorBlendAttachment.alphaBlendOp = VK_BLEND_OP_ADD;
      }
    }
    VkPipelineColorBlendStateCreateInfo colorBlending{};
    colorBlending.sType =
        VK_STRUCTURE_TYPE_PIPELINE_COLOR_BLEND_STATE_CREATE_INFO;
    colorBlending.logicOpEnable = VK_FALSE;
    colorBlending.attachmentCount = 1;
    colorBlending.pAttachments = &colorBlendAttachment;
    VkPipelineDynamicStateCreateInfo dynamicState{};
    dynamicState.sType = VK_STRUCTURE_TYPE_PIPELINE_DYNAMIC_STATE_CREATE_INFO;
    VkDynamicState dynamicStates[] = {VK_DYNAMIC_STATE_VIEWPORT,
                                      VK_DYNAMIC_STATE_SCISSOR};
    dynamicState.dynamicStateCount = std::size(dynamicStates);
    dynamicState.pDynamicStates = dynamicStates;
    VkPipelineLayoutCreateInfo pipelineLayoutInfo{};
    pipelineLayoutInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_LAYOUT_CREATE_INFO;
    VkDescriptorSetLayout descriptorSetLayout =
        GetOrCreateDescriptorSetLayout();
    pipelineLayoutInfo.setLayoutCount = 1;
    pipelineLayoutInfo.pSetLayouts = &descriptorSetLayout;
    if (vkCreatePipelineLayout(m_vulkan->device, &pipelineLayoutInfo, nullptr,
                               &m_currentPipelineLayout) != VK_SUCCESS) {
      spdlog::error("Failed to create pipeline layout");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan pipeline layout creation failed");
    }
    VkGraphicsPipelineCreateInfo pipelineInfo{};
    pipelineInfo.sType = VK_STRUCTURE_TYPE_GRAPHICS_PIPELINE_CREATE_INFO;
    pipelineInfo.stageCount = 2;
    pipelineInfo.pStages = shaderStages;
    pipelineInfo.pVertexInputState = &vertexInputInfo;
    pipelineInfo.pInputAssemblyState = &inputAssembly;
    pipelineInfo.pViewportState = &viewportState;
    pipelineInfo.pRasterizationState = &rasterizer;
    pipelineInfo.pMultisampleState = &multisampling;
    pipelineInfo.pColorBlendState = &colorBlending;
    pipelineInfo.pDynamicState = &dynamicState;
    pipelineInfo.layout = m_currentPipelineLayout;
    pipelineInfo.renderPass = key.renderPass;
    pipelineInfo.subpass = key.subpassIndex;
    pipelineInfo.basePipelineHandle = VK_NULL_HANDLE;
    VkPipeline graphicsPipeline;
    if (vkCreateGraphicsPipelines(m_vulkan->device, VK_NULL_HANDLE, 1,
                                  &pipelineInfo, nullptr,
                                  &graphicsPipeline) != VK_SUCCESS) {
      spdlog::error("Failed to create graphics pipeline");
      vkDestroyPipelineLayout(m_vulkan->device, m_currentPipelineLayout,
                              nullptr);
      m_stats.cacheMisses++;
      throw GPUException("Vulkan graphics pipeline creation failed");
    }
    m_graphicsPipelineCache[key] = graphicsPipeline;
    m_stats.pipelineCreations++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("GetOrCreateGraphicsPipeline: Created pipeline, latency={}us",
                 m_stats.totalLatencyUs.load());
    return graphicsPipeline;
  } catch (const std::exception &e) {
    spdlog::error("GetOrCreateGraphicsPipeline failed: {}", e.what());
    m_stats.cacheMisses++;
    return VK_NULL_HANDLE;
  }
}

/**
 * @brief Gets or creates a compute pipeline.
 * @return Pipeline handle.
 */
VkPipeline PS4GPU::GetOrCreateComputePipeline() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    uint64_t shaderId = 0; // stub: no compute shader ID
    auto it = m_computePipelineCache.find(shaderId);
    if (it != m_computePipelineCache.end()) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      return it->second;
    }
    auto shaderIt = m_shaderModules.find(shaderId);
    if (shaderIt == m_shaderModules.end()) {
      spdlog::error("Compute shader module not found: {}", shaderId);
      m_stats.cacheMisses++;
      throw GPUException("Invalid compute shader module");
    }
    VkPipelineShaderStageCreateInfo shaderStage{};
    shaderStage.sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
    shaderStage.stage = VK_SHADER_STAGE_COMPUTE_BIT;
    shaderStage.module = shaderIt->second.module;
    shaderStage.pName = "main";
    VkPipelineLayoutCreateInfo pipelineLayoutInfo{};
    pipelineLayoutInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_LAYOUT_CREATE_INFO;
    VkDescriptorSetLayout descriptorSetLayout =
        GetOrCreateDescriptorSetLayout();
    pipelineLayoutInfo.setLayoutCount = 1;
    pipelineLayoutInfo.pSetLayouts = &descriptorSetLayout;
    if (vkCreatePipelineLayout(m_vulkan->device, &pipelineLayoutInfo, nullptr,
                               &m_currentPipelineLayout) != VK_SUCCESS) {
      spdlog::error("Failed to create compute pipeline layout");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan pipeline layout creation failed");
    }
    VkComputePipelineCreateInfo pipelineInfo{};
    pipelineInfo.sType = VK_STRUCTURE_TYPE_COMPUTE_PIPELINE_CREATE_INFO;
    pipelineInfo.stage = shaderStage;
    pipelineInfo.layout = m_currentPipelineLayout;
    VkPipeline computePipeline;
    if (vkCreateComputePipelines(m_vulkan->device, VK_NULL_HANDLE, 1,
                                 &pipelineInfo, nullptr,
                                 &computePipeline) != VK_SUCCESS) {
      spdlog::error("Failed to create compute pipeline");
      vkDestroyPipelineLayout(m_vulkan->device, m_currentPipelineLayout,
                              nullptr);
      m_stats.cacheMisses++;
      throw GPUException("Vulkan compute pipeline creation failed");
    }
    m_computePipelineCache[shaderId] = computePipeline;
    m_stats.pipelineCreations++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("GetOrCreateComputePipeline: Created pipeline, shaderId={:x}, "
                 "latency={}us",
                 shaderId, m_stats.totalLatencyUs.load());
    return computePipeline;
  } catch (const std::exception &e) {
    spdlog::error("GetOrCreateComputePipeline failed: {}", e.what());
    m_stats.cacheMisses++;
    return VK_NULL_HANDLE;
  }
}

/**
 * @brief Gets or creates a render pass.
 * @param key Render pass key.
 * @return Render pass handle.
 */
VkRenderPass PS4GPU::GetOrCreateRenderPass(const RenderPassKey &key) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    auto it = m_renderPassCache.find(key);
    if (it != m_renderPassCache.end()) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      return it->second;
    }
    std::vector<VkAttachmentDescription> attachments;
    std::vector<VkAttachmentReference> colorAttachments;
    for (const auto &format : key.colorFormats) {
      VkAttachmentDescription colorAttachment{};
      colorAttachment.format = format;
      colorAttachment.samples = VK_SAMPLE_COUNT_1_BIT;
      colorAttachment.loadOp = key.colorLoadOp;
      colorAttachment.storeOp = key.colorStoreOp;
      colorAttachment.stencilLoadOp = VK_ATTACHMENT_LOAD_OP_DONT_CARE;
      colorAttachment.stencilStoreOp = VK_ATTACHMENT_STORE_OP_DONT_CARE;
      colorAttachment.initialLayout = key.initialColorLayout;
      colorAttachment.finalLayout = key.finalColorLayout;
      attachments.push_back(colorAttachment);
      VkAttachmentReference colorAttachmentRef{};
      colorAttachmentRef.attachment =
          static_cast<uint32_t>(colorAttachments.size());
      colorAttachmentRef.layout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;
      colorAttachments.push_back(colorAttachmentRef);
    }
    VkAttachmentDescription depthAttachment{};
    VkAttachmentReference depthAttachmentRef{};
    if (key.depthFormat != VK_FORMAT_UNDEFINED) {
      depthAttachment.format = key.depthFormat;
      depthAttachment.samples = VK_SAMPLE_COUNT_1_BIT;
      depthAttachment.loadOp = key.depthLoadOp;
      depthAttachment.storeOp = key.depthStoreOp;
      depthAttachment.stencilLoadOp = key.depthLoadOp;
      depthAttachment.stencilStoreOp = key.depthStoreOp;
      depthAttachment.initialLayout = key.initialDepthLayout;
      depthAttachment.finalLayout = key.finalDepthLayout;
      attachments.push_back(depthAttachment);
      depthAttachmentRef.attachment =
          static_cast<uint32_t>(attachments.size() - 1);
      depthAttachmentRef.layout =
          VK_IMAGE_LAYOUT_DEPTH_STENCIL_ATTACHMENT_OPTIMAL;
    }
    VkSubpassDescription subpass{};
    subpass.pipelineBindPoint = VK_PIPELINE_BIND_POINT_GRAPHICS;
    subpass.colorAttachmentCount =
        static_cast<uint32_t>(colorAttachments.size());
    subpass.pColorAttachments = colorAttachments.data();
    if (key.depthFormat != VK_FORMAT_UNDEFINED) {
      subpass.pDepthStencilAttachment = &depthAttachmentRef;
    }
    VkSubpassDependency dependency{};
    dependency.srcSubpass = VK_SUBPASS_EXTERNAL;
    dependency.dstSubpass = 0;
    dependency.srcStageMask = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT |
                              VK_PIPELINE_STAGE_EARLY_FRAGMENT_TESTS_BIT;
    dependency.srcAccessMask = 0;
    dependency.dstStageMask = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT |
                              VK_PIPELINE_STAGE_EARLY_FRAGMENT_TESTS_BIT;
    dependency.dstAccessMask = VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT |
                               VK_ACCESS_DEPTH_STENCIL_ATTACHMENT_WRITE_BIT;
    VkRenderPassCreateInfo renderPassInfo{};
    renderPassInfo.sType = VK_STRUCTURE_TYPE_RENDER_PASS_CREATE_INFO;
    renderPassInfo.attachmentCount = static_cast<uint32_t>(attachments.size());
    renderPassInfo.pAttachments = attachments.data();
    renderPassInfo.subpassCount = 1;
    renderPassInfo.pSubpasses = &subpass;
    renderPassInfo.dependencyCount = 1;
    renderPassInfo.pDependencies = &dependency;
    VkRenderPass renderPass;
    if (vkCreateRenderPass(m_vulkan->device, &renderPassInfo, nullptr,
                           &renderPass) != VK_SUCCESS) {
      spdlog::error("Failed to create render pass");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan render pass creation failed");
    }
    m_renderPassCache[key] = renderPass;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("GetOrCreateRenderPass: Created render pass, "
                 "colorAttachments={}, latency={}us",
                 colorAttachments.size(), m_stats.totalLatencyUs.load());
    return renderPass;
  } catch (const std::exception &e) {
    spdlog::error("GetOrCreateRenderPass failed: {}", e.what());
    m_stats.cacheMisses++;
    return VK_NULL_HANDLE;
  }
}

/**
 * @brief Gets or creates a framebuffer.
 * @param colorTargetIds Color target IDs.
 * @param depthTargetId Depth target ID.
 * @return Framebuffer handle.
 */
VkFramebuffer
PS4GPU::GetOrCreateFramebuffer(const std::vector<uint64_t> &colorTargetIds,
                               uint64_t depthTargetId) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    uint64_t key = 0;
    for (const auto &id : colorTargetIds) {
      key ^= id;
    }
    key ^= depthTargetId;
    auto it = m_framebufferCache.find(key);
    if (it != m_framebufferCache.end()) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      return it->second;
    }
    std::vector<VkImageView> attachments;
    for (const auto &id : colorTargetIds) {
      auto targetIt = m_renderTargets.find(id);
      if (targetIt == m_renderTargets.end()) {
        spdlog::error("Color render target not found: {}", id);
        m_stats.cacheMisses++;
        throw GPUException("Invalid color render target");
      }
      attachments.push_back(targetIt->second.view);
    }
    if (depthTargetId) {
      auto targetIt = m_renderTargets.find(depthTargetId);
      if (targetIt == m_renderTargets.end()) {
        spdlog::error("Depth render target not found: {}", depthTargetId);
        m_stats.cacheMisses++;
        throw GPUException("Invalid depth render target");
      }
      attachments.push_back(targetIt->second.view);
    }
    VkFramebufferCreateInfo framebufferInfo{};
    framebufferInfo.sType = VK_STRUCTURE_TYPE_FRAMEBUFFER_CREATE_INFO;
    framebufferInfo.renderPass = GetOrCreateRenderPass(RenderPassKey{});
    framebufferInfo.attachmentCount = static_cast<uint32_t>(attachments.size());
    framebufferInfo.pAttachments = attachments.data();
    framebufferInfo.width = m_vulkan->swapchainExtent.width;
    framebufferInfo.height = m_vulkan->swapchainExtent.height;
    framebufferInfo.layers = 1;
    VkFramebuffer framebuffer;
    if (vkCreateFramebuffer(m_vulkan->device, &framebufferInfo, nullptr,
                            &framebuffer) != VK_SUCCESS) {
      spdlog::error("Failed to create framebuffer");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan framebuffer creation failed");
    }
    m_framebufferCache[key] = framebuffer;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("GetOrCreateFramebuffer: Created framebuffer, attachments={}, "
                 "latency={}us",
                 attachments.size(), m_stats.totalLatencyUs.load());
    return framebuffer;
  } catch (const std::exception &e) {
    spdlog::error("GetOrCreateFramebuffer failed: {}", e.what());
    m_stats.cacheMisses++;
    return VK_NULL_HANDLE;
  }
}

/**
 * @brief Gets or creates a descriptor set layout.
 * @return Descriptor set layout handle.
 */
VkDescriptorSetLayout PS4GPU::GetOrCreateDescriptorSetLayout() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    VkDescriptorSetLayoutBinding samplerBinding{};
    samplerBinding.binding = 0;
    samplerBinding.descriptorCount = 1;
    samplerBinding.descriptorType = VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER;
    samplerBinding.pImmutableSamplers = nullptr;
    samplerBinding.stageFlags = VK_SHADER_STAGE_FRAGMENT_BIT;
    VkDescriptorSetLayoutCreateInfo layoutInfo{};
    layoutInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_CREATE_INFO;
    layoutInfo.bindingCount = 1;
    layoutInfo.pBindings = &samplerBinding;
    VkDescriptorSetLayout descriptorSetLayout;
    if (vkCreateDescriptorSetLayout(m_vulkan->device, &layoutInfo, nullptr,
                                    &descriptorSetLayout) != VK_SUCCESS) {
      spdlog::error("Failed to create descriptor set layout");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan descriptor set layout creation failed");
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("GetOrCreateDescriptorSetLayout: Created descriptor set "
                 "layout, latency={}us",
                 m_stats.totalLatencyUs.load());
    return descriptorSetLayout;
  } catch (const std::exception &e) {
    spdlog::error("GetOrCreateDescriptorSetLayout failed: {}", e.what());
    m_stats.cacheMisses++;
    return VK_NULL_HANDLE;
  }
}

/**
 * @brief Allocates a descriptor set.
 * @param layout Descriptor set layout.
 * @return Descriptor set handle.
 */
VkDescriptorSet PS4GPU::AllocateDescriptorSet(VkDescriptorSetLayout layout) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    VkDescriptorSetAllocateInfo allocInfo{};
    allocInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_ALLOCATE_INFO;
    allocInfo.descriptorPool = m_vulkan->descriptorPool;
    allocInfo.descriptorSetCount = 1;
    allocInfo.pSetLayouts = &layout;
    VkDescriptorSet descriptorSet;
    if (vkAllocateDescriptorSets(m_vulkan->device, &allocInfo,
                                 &descriptorSet) != VK_SUCCESS) {
      spdlog::error("Failed to allocate descriptor set");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan descriptor set allocation failed");
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "AllocateDescriptorSet: Allocated descriptor set, latency={}us",
        m_stats.totalLatencyUs.load());
    return descriptorSet;
  } catch (const std::exception &e) {
    spdlog::error("AllocateDescriptorSet failed: {}", e.what());
    m_stats.cacheMisses++;
    return VK_NULL_HANDLE;
  }
}

/**
 * @brief Updates the descriptor set.
 */
void PS4GPU::UpdateDescriptorSet() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    // Get current descriptor set (assume first one for now)
    if (m_currentDescriptorSets.empty()) {
      spdlog::warn("No descriptor sets available for update");
      return;
    }

    VkDescriptorSet descriptorSet = m_currentDescriptorSets[0];
    std::vector<VkWriteDescriptorSet> writes;
    std::vector<VkDescriptorImageInfo> imageInfos;
    std::vector<VkDescriptorBufferInfo> bufferInfos;

    // Update texture bindings
    uint32_t textureBinding = 0;
    for (const auto &[textureId, textureView] : m_textureViews) {
      if (textureView != VK_NULL_HANDLE &&
          textureBinding < 16) { // Limit to 16 textures
        VkDescriptorImageInfo imageInfo{};

        // Find corresponding sampler (use default if not found)
        auto samplerIt = m_samplers.find(textureId);
        imageInfo.sampler = (samplerIt != m_samplers.end()) ? samplerIt->second
                                                            : VK_NULL_HANDLE;
        imageInfo.imageView = textureView;
        imageInfo.imageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;

        imageInfos.push_back(imageInfo);

        VkWriteDescriptorSet write{};
        write.sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
        write.dstSet = descriptorSet;
        write.dstBinding = textureBinding;
        write.dstArrayElement = 0;
        write.descriptorType = VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER;
        write.descriptorCount = 1;
        write.pImageInfo = &imageInfos.back();

        writes.push_back(write);
        textureBinding++;
      }
    }

    // Update uniform buffer bindings
    uint32_t bufferBinding = 16; // Start after texture bindings
    for (const auto &[bufferAddress, mapping] : m_memoryMappings) {
      if (mapping.gpuAddress != 0 &&
          bufferBinding < 32) { // Limit total bindings
        // Create buffer info for uniform buffers
        VkDescriptorBufferInfo bufferInfo{};
        bufferInfo.buffer =
            VK_NULL_HANDLE; // Would need to map to actual VkBuffer
        bufferInfo.offset = 0;
        bufferInfo.range = VK_WHOLE_SIZE;

        // Skip for now since we don't have actual VkBuffer handles
        // This would need proper buffer management implementation
        bufferBinding++;
      }
    }

    // Apply descriptor set updates
    if (!writes.empty()) {
      vkUpdateDescriptorSets(m_vulkan->device,
                             static_cast<uint32_t>(writes.size()),
                             writes.data(), 0, nullptr);

      spdlog::debug("Updated descriptor set with {} bindings", writes.size());
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "UpdateDescriptorSet: Updated descriptor set with {} writes, "
        "latency={}us",
        writes.size(),
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count());
  } catch (const std::exception &e) {
    spdlog::error("UpdateDescriptorSet failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Finds a memory type.
 * @param typeFilter Type filter.
 * @param properties Memory properties.
 * @return Memory type index.
 */
uint32_t PS4GPU::FindMemoryType(uint32_t typeFilter,
                                VkMemoryPropertyFlags properties) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    VkPhysicalDeviceMemoryProperties memProperties;
    vkGetPhysicalDeviceMemoryProperties(m_vulkan->physicalDevice,
                                        &memProperties);
    for (uint32_t i = 0; i < memProperties.memoryTypeCount; ++i) {
      if ((typeFilter & (1 << i)) &&
          (memProperties.memoryTypes[i].propertyFlags & properties) ==
              properties) {
        m_stats.cacheHits++;
        auto end = std::chrono::steady_clock::now();
        m_stats.totalLatencyUs +=
            std::chrono::duration_cast<std::chrono::microseconds>(end - start)
                .count();
        spdlog::trace("FindMemoryType: Found memory type {}, latency={}us", i,
                      m_stats.totalLatencyUs.load());
        return i;
      }
    }
    spdlog::error("Failed to find suitable memory type");
    m_stats.cacheMisses++;
    throw GPUException("Suitable memory type not found");
  } catch (const std::exception &e) {
    spdlog::error("FindMemoryType failed: {}", e.what());
    m_stats.cacheMisses++;
    throw;
  }
}

/**
 * @brief Creates a buffer.
 * @param size Buffer size.
 * @param usage Buffer usage.
 * @param properties Memory properties.
 * @param memory Output memory handle.
 * @return Buffer handle.
 */
VkBuffer PS4GPU::CreateBuffer(uint64_t size, VkBufferUsageFlags usage,
                              VkMemoryPropertyFlags properties,
                              VkDeviceMemory &memory) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    VkBufferCreateInfo bufferInfo{};
    bufferInfo.sType = VK_STRUCTURE_TYPE_BUFFER_CREATE_INFO;
    bufferInfo.size = size;
    bufferInfo.usage = usage;
    bufferInfo.sharingMode = VK_SHARING_MODE_EXCLUSIVE;
    VkBuffer buffer;
    if (vkCreateBuffer(m_vulkan->device, &bufferInfo, nullptr, &buffer) !=
        VK_SUCCESS) {
      spdlog::error("Failed to create buffer");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan buffer creation failed");
    }
    VkMemoryRequirements memRequirements;
    vkGetBufferMemoryRequirements(m_vulkan->device, buffer, &memRequirements);
    VkMemoryAllocateInfo allocInfo{};
    allocInfo.sType = VK_STRUCTURE_TYPE_MEMORY_ALLOCATE_INFO;
    allocInfo.allocationSize = memRequirements.size;
    allocInfo.memoryTypeIndex =
        FindMemoryType(memRequirements.memoryTypeBits, properties);
    if (vkAllocateMemory(m_vulkan->device, &allocInfo, nullptr, &memory) !=
        VK_SUCCESS) {
      spdlog::error("Failed to allocate buffer memory");
      vkDestroyBuffer(m_vulkan->device, buffer, nullptr);
      m_stats.cacheMisses++;
      throw GPUException("Vulkan memory allocation failed");
    }
    vkBindBufferMemory(m_vulkan->device, buffer, memory, 0);
    m_stats.vramUsage += memRequirements.size;
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("CreateBuffer: Created buffer, size={}, vramUsage={} bytes, "
                  "latency={}us",
                  size, m_stats.vramUsage.load(),
                  m_stats.totalLatencyUs.load());
    return buffer;
  } catch (const std::exception &e) {
    spdlog::error("CreateBuffer failed: {}", e.what());
    m_stats.cacheMisses++;
    throw;
  }
}

/**
 * @brief Begins a command buffer.
 */
void PS4GPU::BeginCommandBuffer() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    vkResetCommandBuffer(m_commandBuffer, 0);
    VkCommandBufferBeginInfo beginInfo{};
    beginInfo.sType = VK_STRUCTURE_TYPE_COMMAND_BUFFER_BEGIN_INFO;
    if (vkBeginCommandBuffer(m_commandBuffer, &beginInfo) != VK_SUCCESS) {
      spdlog::error("Failed to begin command buffer");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan command buffer begin failed");
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("BeginCommandBuffer: Begun command buffer, latency={}us",
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("BeginCommandBuffer failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Submits a command buffer.
 */
void PS4GPU::SubmitCommandBuffer() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    VkSubmitInfo submitInfo{};
    submitInfo.sType = VK_STRUCTURE_TYPE_SUBMIT_INFO;
    VkSemaphore waitSemaphores[] = {m_vulkan->imageAvailableSemaphore};
    VkPipelineStageFlags waitStages[] = {
        VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT};
    submitInfo.waitSemaphoreCount = 1;
    submitInfo.pWaitSemaphores = waitSemaphores;
    submitInfo.pWaitDstStageMask = waitStages;
    submitInfo.commandBufferCount = 1;
    submitInfo.pCommandBuffers = &m_commandBuffer;
    VkSemaphore signalSemaphores[] = {m_vulkan->renderFinishedSemaphore};
    submitInfo.signalSemaphoreCount = 1;
    submitInfo.pSignalSemaphores = signalSemaphores;
    if (vkQueueSubmit(m_vulkan->graphicsQueue, 1, &submitInfo,
                      m_vulkan->inFlightFence) != VK_SUCCESS) {
      spdlog::error("Failed to submit command buffer");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan command buffer submission failed");
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("SubmitCommandBuffer: Submitted command buffer, latency={}us",
                  m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("SubmitCommandBuffer failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Transitions an image layout.
 * @param image Image handle.
 * @param format Image format.
 * @param oldLayout Old layout.
 * @param newLayout New layout.
 */
void PS4GPU::TransitionImageLayout(VkImage image, VkFormat format,
                                   VkImageLayout oldLayout,
                                   VkImageLayout newLayout) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    VkImageMemoryBarrier barrier{};
    barrier.sType = VK_STRUCTURE_TYPE_IMAGE_MEMORY_BARRIER;
    barrier.oldLayout = oldLayout;
    barrier.newLayout = newLayout;
    barrier.srcQueueFamilyIndex = VK_QUEUE_FAMILY_IGNORED;
    barrier.dstQueueFamilyIndex = VK_QUEUE_FAMILY_IGNORED;
    barrier.image = image;
    barrier.subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
    barrier.subresourceRange.baseMipLevel = 0;
    barrier.subresourceRange.levelCount = 1;
    barrier.subresourceRange.baseArrayLayer = 0;
    barrier.subresourceRange.layerCount = 1;
    VkPipelineStageFlags sourceStage;
    VkPipelineStageFlags destinationStage;
    if (oldLayout == VK_IMAGE_LAYOUT_UNDEFINED &&
        newLayout == VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL) {
      barrier.srcAccessMask = 0;
      barrier.dstAccessMask = VK_ACCESS_TRANSFER_WRITE_BIT;
      sourceStage = VK_PIPELINE_STAGE_TOP_OF_PIPE_BIT;
      destinationStage = VK_PIPELINE_STAGE_TRANSFER_BIT;
    } else if (oldLayout == VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL &&
               newLayout == VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL) {
      barrier.srcAccessMask = VK_ACCESS_TRANSFER_WRITE_BIT;
      barrier.dstAccessMask = VK_ACCESS_SHADER_READ_BIT;
      sourceStage = VK_PIPELINE_STAGE_TRANSFER_BIT;
      destinationStage = VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT;
    } else {
      spdlog::error("Unsupported layout transition");
      m_stats.cacheMisses++;
      throw GPUException("Unsupported image layout transition");
    }
    vkCmdPipelineBarrier(m_commandBuffer, sourceStage, destinationStage, 0, 0,
                         nullptr, 0, nullptr, 1, &barrier);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        fmt::runtime(
            "TransitionImageLayout: oldLayout={}, newLayout={}, latency={}us"),
        static_cast<uint32_t>(oldLayout), static_cast<uint32_t>(newLayout),
        m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("TransitionImageLayout failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Copies a buffer to an image.
 * @param buffer Buffer handle.
 * @param image Image handle.
 * @param width Image width.
 * @param height Image height.
 */
void PS4GPU::CopyBufferToImage(VkBuffer buffer, VkImage image, uint32_t width,
                               uint32_t height) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    VkBufferImageCopy region{};
    region.bufferOffset = 0;
    region.bufferRowLength = 0;
    region.bufferImageHeight = 0;
    region.imageSubresource.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
    region.imageSubresource.mipLevel = 0;
    region.imageSubresource.baseArrayLayer = 0;
    region.imageSubresource.layerCount = 1;
    region.imageOffset = {0, 0, 0};
    region.imageExtent = {width, height, 1};
    vkCmdCopyBufferToImage(m_commandBuffer, buffer, image,
                           VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL, 1, &region);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("CopyBufferToImage: width={}, height={}, latency={}us", width,
                  height, m_stats.totalLatencyUs.load());
  } catch (const std::exception &e) {
    spdlog::error("CopyBufferToImage failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Translates GCN shader to SPIR-V.
 * @param gcnCode GCN code.
 * @param size Code size.
 * @param stage Shader stage.
 * @return Shader ID, or 0 on failure.
 */
uint64_t PS4GPU::TranslateGCNShader(const void *gcnCode, size_t size,
                                    VkShaderStageFlagBits stage) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    // Convert raw GCN code to vector<uint32_t>
    const uint32_t *codePtr = reinterpret_cast<const uint32_t *>(gcnCode);
    size_t instrCount = size / sizeof(uint32_t);
    std::vector<uint32_t> bytecode(codePtr, codePtr + instrCount);
    // Map Vulkan stage to GCNShaderType
    GCNShaderType shaderType;
    switch (stage) {
    case VK_SHADER_STAGE_VERTEX_BIT:
      shaderType = GCNShaderType::VERTEX;
      break;
    case VK_SHADER_STAGE_FRAGMENT_BIT:
      shaderType = GCNShaderType::PIXEL;
      break;
    case VK_SHADER_STAGE_GEOMETRY_BIT:
      shaderType = GCNShaderType::GEOMETRY;
      break;
    case VK_SHADER_STAGE_COMPUTE_BIT:
      shaderType = GCNShaderType::COMPUTE;
      break;
    case VK_SHADER_STAGE_TESSELLATION_CONTROL_BIT:
      shaderType = GCNShaderType::HULL;
      break;
    case VK_SHADER_STAGE_TESSELLATION_EVALUATION_BIT:
      shaderType = GCNShaderType::DOMAIN_SHADER;
      break;
    default:
      shaderType = GCNShaderType::VERTEX;
      break;
    }
    // Perform translation
    auto spirvCode = m_shaderTranslator->TranslateToSPIRV(bytecode, shaderType);

    VkShaderModuleCreateInfo createInfo{};
    createInfo.sType = VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO;
    createInfo.codeSize = spirvCode.size() * sizeof(uint32_t);
    createInfo.pCode = spirvCode.data();
    ShaderModule shader;
    shader.type = shaderType;
    if (vkCreateShaderModule(m_vulkan->device, &createInfo, nullptr,
                             &shader.module) != VK_SUCCESS) {
      spdlog::error("Failed to create shader module");
      m_stats.cacheMisses++;
      throw GPUException("Vulkan shader module creation failed");
    }
    uint64_t shaderId = reinterpret_cast<uint64_t>(shader.module);
    m_shaderModules[shaderId] = shader;
    m_stats.shaderCompilations++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("TranslateGCNShader: Translated shader, id={:x}, size={}, "
                 "stage={}, latency={}us",
                 shaderId, size, static_cast<uint32_t>(stage),
                 m_stats.totalLatencyUs.load());
    return shaderId;
  } catch (const std::exception &e) {
    spdlog::error("TranslateGCNShader failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}

/**
 * @brief Clears the shader cache.
 * Destroys all shader modules and resets statistics.
 */
void PS4GPU::ClearShaderCache() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    spdlog::info("Clearing shader cache, {} shaders", m_shaderModules.size());

    // Clean up all shader modules
    for (auto &[shaderId, shader] : m_shaderModules) {
      if (shader.module != VK_NULL_HANDLE) {
        vkDestroyShaderModule(m_vulkan->device, shader.module, nullptr);
      }
    }

    // Clear the shader module map
    m_shaderModules.clear();

    // Reset shader stats
    m_stats.shaderCount = 0;
    m_stats.cacheHits++;

    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info(
        "Shader cache cleared, latency={}us",
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count());
  } catch (const std::exception &e) {
    m_stats.cacheMisses++;
    m_stats.errorCount++;
    spdlog::error("Failed to clear shader cache: {}", e.what());
  }
}

/**
 * @brief Notifies GPU of packet processing from command processor.
 * @param header Packet header information.
 * @param data Packet data.
 */
void PS4GPU::NotifyPacketProcessed(uint32_t header,
                                   const std::vector<uint32_t> &data) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    // Update GPU state based on packet processing
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "NotifyPacketProcessed: header=0x{:x}, data_size={}, latency={}us",
        header, data.size(),
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count());
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("NotifyPacketProcessed failed: {}", e.what());
  }
}

/**
 * @brief Notifies GPU of shader translation completion.
 * @param type Shader type that was translated.
 * @param bytecodeHash Hash of the original bytecode.
 * @param spirvCode Translated SPIR-V code.
 */
void PS4GPU::NotifyShaderTranslated(GCNShaderType type, uint64_t bytecodeHash,
                                    const std::vector<uint32_t> &spirvCode) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    // Track shader translation for pipeline creation
    m_stats.shaderCompilations++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "NotifyShaderTranslated: type={}, hash=0x{:x}, spirv_size={}, "
        "latency={}us",
        static_cast<int>(type), bytecodeHash, spirvCode.size(),
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count());
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("NotifyShaderTranslated failed: {}", e.what());
  }
}

/**
 * @brief Notifies GPU of shader translation completion (GLSL version).
 * @param type Shader type that was translated.
 * @param bytecodeHash Hash of the original bytecode.
 * @param glslCode Translated GLSL code.
 */
void PS4GPU::NotifyShaderTranslated(GCNShaderType type, uint64_t bytecodeHash,
                                    const std::string &glslCode) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    // Track shader translation for pipeline creation
    m_stats.shaderCompilations++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "NotifyShaderTranslated: type={}, hash=0x{:x}, glsl_size={}, "
        "latency={}us",
        static_cast<int>(type), bytecodeHash, glslCode.size(),
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count());
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("NotifyShaderTranslated failed: {}", e.what());
  }
}

/**
 * @brief Notifies GPU of register change for rendering updates.
 * @param regType Type of register (shader, context, etc.).
 * @param stage Shader stage (for shader registers).
 * @param offset Register offset.
 * @param value New register value.
 */
void PS4GPU::NotifyRegisterChange(GNMRegisterType regType, uint32_t stage,
                                  uint32_t offset, uint32_t value) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    // Handle register changes for rendering pipeline updates
    switch (regType) {
    case GNMRegisterType::SHADER_REG:
      HandleShaderRegisterChange(stage, offset, value);
      break;
    case GNMRegisterType::CONTEXT_REG:
      HandleContextRegisterChange(offset, value);
      break;
    case GNMRegisterType::CONFIG_REG:
      HandleConfigRegisterChange(offset, value);
      break;
    case GNMRegisterType::USER_REG:
      HandleUserRegisterChange(offset, value);
      break;
    default:
      spdlog::warn("Unknown register type: {}", static_cast<int>(regType));
      break;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "NotifyRegisterChange: type={}, stage={}, offset=0x{:x}, value=0x{:x}, "
        "latency={}us",
        static_cast<int>(regType), stage, offset, value,
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count());
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("NotifyRegisterChange failed: {}", e.what());
  }
}

/**
 * @brief Handles shader register changes.
 * @param stage Shader stage.
 * @param offset Register offset.
 * @param value New register value.
 */
void PS4GPU::HandleShaderRegisterChange(uint32_t stage, uint32_t offset,
                                        uint32_t value) {
  try {
    // Handle shader-specific register changes
    switch (offset) {
    case 0x0: // SPI_SHADER_PGM_LO_PS - Pixel shader program low address
    case 0x1: // SPI_SHADER_PGM_HI_PS - Pixel shader program high address
      if (stage == static_cast<uint32_t>(GCNShaderType::PIXEL)) {
        // Store shader address information
        m_gnmState.SetShaderRegister(stage, offset, value);
        // Force pipeline state invalidation
        m_pipelineStateDirty = true;
        spdlog::debug("Updated PS shader address register 0x{:x}: 0x{:x}",
                      offset, value);
      }
      break;
    case 0x2: // SPI_SHADER_PGM_RSRC1_PS - Pixel shader resource register 1
      if (stage == static_cast<uint32_t>(GCNShaderType::PIXEL)) {
        m_gnmState.SetShaderRegister(stage, offset, value);
        m_pipelineStateDirty = true;
        spdlog::debug("Updated PS resource register 1: 0x{:x}", value);
      }
      break;
    default:
      // Handle other shader registers generically
      m_gnmState.SetShaderRegister(stage, offset, value);
      m_pipelineStateDirty = true;
      spdlog::trace(
          "Updated shader register: stage={}, offset=0x{:x}, value=0x{:x}",
          stage, offset, value);
      break;
    }
  } catch (const std::exception &e) {
    spdlog::error("HandleShaderRegisterChange failed: {}", e.what());
  }
}

/**
 * @brief Handles context register changes.
 * @param offset Register offset.
 * @param value New register value.
 */
void PS4GPU::HandleContextRegisterChange(uint32_t offset, uint32_t value) {
  try {
    // Handle context register changes (viewport, scissor, render targets, etc.)
    switch (offset) {
    case 0x80: // PA_CL_VPORT_XSCALE - Viewport X scale
    case 0x81: // PA_CL_VPORT_XOFFSET - Viewport X offset
    case 0x82: // PA_CL_VPORT_YSCALE - Viewport Y scale
    case 0x83: // PA_CL_VPORT_YOFFSET - Viewport Y offset
    case 0x84: // PA_CL_VPORT_ZSCALE - Viewport Z scale
    case 0x85: // PA_CL_VPORT_ZOFFSET - Viewport Z offset
    {
      // Update viewport state in register storage
      m_gnmState.SetContextRegister(offset, value);
      m_viewportStateDirty = true;
      spdlog::debug("Updated viewport register 0x{:x}: {}", offset,
                    *reinterpret_cast<const float *>(&value));
    } break;
    case 0x100: // CB_COLOR0_BASE - Color buffer 0 base address
    case 0x101: // CB_COLOR1_BASE - Color buffer 1 base address
    case 0x102: // CB_COLOR2_BASE - Color buffer 2 base address
    case 0x103: // CB_COLOR3_BASE - Color buffer 3 base address
    case 0x104: // CB_COLOR4_BASE - Color buffer 4 base address
    case 0x105: // CB_COLOR5_BASE - Color buffer 5 base address
    case 0x106: // CB_COLOR6_BASE - Color buffer 6 base address
    case 0x107: // CB_COLOR7_BASE - Color buffer 7 base address
    {
      uint32_t rtIndex = offset - 0x100;
      uint64_t baseAddr = static_cast<uint64_t>(value)
                          << 8; // Address is in 256-byte units
      m_gnmState.SetContextRegister(offset, value);
      m_renderTargetStateDirty = true;
      spdlog::debug("Updated render target {} base address: 0x{:x}", rtIndex,
                    baseAddr);
    } break;
    default:
      // Handle other context registers generically
      m_gnmState.SetContextRegister(offset, value);
      spdlog::trace("Updated context register: offset=0x{:x}, value=0x{:x}",
                    offset, value);
      break;
    }
  } catch (const std::exception &e) {
    spdlog::error("HandleContextRegisterChange failed: {}", e.what());
  }
}

/**
 * @brief Handles config register changes.
 * @param offset Register offset.
 * @param value New register value.
 */
void PS4GPU::HandleConfigRegisterChange(uint32_t offset, uint32_t value) {
  try {
    // Handle config register changes (global GPU configuration)
    m_gnmState.SetConfigRegister(offset, value);
    spdlog::trace("Updated config register: offset=0x{:x}, value=0x{:x}",
                  offset, value);

    // Some config registers may affect global state
    switch (offset) {
    case 0x200: // Example: PA_SU_SC_MODE_CNTL - Screen coordinate mode control
      // Update global rasterization state
      m_pipelineStateDirty = true;
      break;
    default:
      // Most config registers don't require immediate action
      break;
    }
  } catch (const std::exception &e) {
    spdlog::error("HandleConfigRegisterChange failed: {}", e.what());
  }
}

/**
 * @brief Handles user config register changes.
 * @param offset Register offset.
 * @param value New register value.
 */
void PS4GPU::HandleUserRegisterChange(uint32_t offset, uint32_t value) {
  try {
    // Handle user config register changes (application-specific configuration)
    m_gnmState.SetUserRegister(offset, value);
    spdlog::trace("Updated user register: offset=0x{:x}, value=0x{:x}", offset,
                  value);

    // User config registers typically don't affect immediate rendering state
    // but may be used for driver-specific optimizations or debugging
  } catch (const std::exception &e) {
    spdlog::error("HandleUserRegisterChange failed: {}", e.what());
  }
}

/**
 * @brief Notifies GPU of shader execution completion.
 * @param shaderType Type of shader that was executed.
 * @param instructionCount Number of instructions executed.
 */
void PS4GPU::NotifyShaderExecuted(GCNShaderType shaderType,
                                  uint64_t instructionCount) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    // Track shader execution for performance monitoring
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "NotifyShaderExecuted: type={}, instruction_count={}, latency={}us",
        static_cast<int>(shaderType), instructionCount,
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count());
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("NotifyShaderExecuted failed: {}", e.what());
  }
}

/**
 * @brief Clears the render target cache.
 * Destroys all render targets and associated resources.
 */
void PS4GPU::ClearRenderTargetCache() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    spdlog::info("Clearing render target cache, {} targets",
                 m_renderTargets.size());

    // Clean up all render targets
    for (auto &[targetId, target] : m_renderTargets) {
      if (target.view != VK_NULL_HANDLE) {
        vkDestroyImageView(m_vulkan->device, target.view, nullptr);
      }
      if (target.image != VK_NULL_HANDLE) {
        vkDestroyImage(m_vulkan->device, target.image, nullptr);
      }
      if (target.memory != VK_NULL_HANDLE) {
        vkFreeMemory(m_vulkan->device, target.memory, nullptr);
      }
    }

    // Clear the render target map
    m_renderTargets.clear();

    m_stats.cacheHits++;

    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info(
        "Render target cache cleared, latency={}us",
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count());
  } catch (const std::exception &e) {
    m_stats.cacheMisses++;
    m_stats.errorCount++;
    spdlog::error("Failed to clear render target cache: {}", e.what());
  }
}

// Set a new TileManager instance
void PS4GPU::SetTileManager(std::unique_ptr<TileManager> tileManager) {
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  m_tileManager = std::move(tileManager);
}

// Get the TileManager instance
TileManager &PS4GPU::GetTileManager() {
  std::shared_lock<std::shared_mutex> lock(m_gpuMutex);
  if (!m_tileManager) {
    throw GPUException("TileManager not set");
  }
  return *m_tileManager;
}

// Get the internal GNMRegisterState
GNMRegisterState &PS4GPU::GetRegisterState() { return m_gnmState; }

/**
 * @brief Recreates the swapchain.
 * @return True on success, false otherwise.
 */
bool PS4GPU::RecreateSwapchain() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_gpuMutex);
  try {
    spdlog::info("Recreating swapchain...");

    // Wait for device to be idle
    vkDeviceWaitIdle(m_vulkan->device);

    // RACE CONDITION FIX: Synchronize access to VulkanContext swapchain
    std::unique_lock<std::shared_mutex> vulkanLock(m_vulkan->contextMutex);

    // Clean up old swapchain resources
    for (auto &[_, framebuffer] : m_framebufferCache) {
      if (framebuffer != VK_NULL_HANDLE) {
        vkDestroyFramebuffer(m_vulkan->device, framebuffer, nullptr);
      }
    }
    m_framebufferCache.clear();

    for (auto &view : m_vulkan->swapchainImageViews) {
      if (view != VK_NULL_HANDLE) {
        vkDestroyImageView(m_vulkan->device, view, nullptr);
      }
    }
    m_vulkan->swapchainImageViews.clear();

    if (m_vulkan->swapchain != VK_NULL_HANDLE) {
      vkDestroySwapchainKHR(m_vulkan->device, m_vulkan->swapchain, nullptr);
      m_vulkan->swapchain = VK_NULL_HANDLE;
    }

    // Release Vulkan lock before calling CreateSwapchain to prevent deadlock
    vulkanLock.unlock();

    // Recreate swapchain components
    if (!CreateSwapchain() || !CreateSwapchainImageViews() ||
        !CreateFramebuffers()) {
      spdlog::error("Failed to recreate swapchain");
      m_stats.cacheMisses++;
      throw GPUException("Swapchain recreation failed");
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Swapchain recreated successfully, latency={}us",
                 m_stats.totalLatencyUs.load());
    return true;
  } catch (const std::exception &e) {
    spdlog::error("RecreateSwapchain failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}
} // namespace ps4
