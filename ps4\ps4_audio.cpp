// Copyright 2025 <Copyright Owner>

#include "ps4_audio.h"
#include "../memory/memory_diagnostics.h"
#include <algorithm>
#include <chrono>
#include <cmath>
#include <cstring>
#include <fmt/format.h>
#include <spdlog/spdlog.h>
#include <stdexcept>


#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

namespace ps4 {

/**
 * @brief Constructs the audio device.
 */
AudioDevice::AudioDevice() {
  auto start = std::chrono::steady_clock::now();
  audioStream_ = {nullptr, SAMPLE_RATE, 2, 1.0f, false, {}};
  stats_ = Stats();
  spdlog::info(fmt::format("AudioDevice constructed"));
  auto end = std::chrono::steady_clock::now();
  stats_.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Destructor, cleaning up resources.
 */
AudioDevice::~AudioDevice() {
  auto start = std::chrono::steady_clock::now();
  Stop();
  spdlog::info(fmt::format("AudioDevice destroyed"));
  auto end = std::chrono::steady_clock::now();
  stats_.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief PortAudio stream callback.
 * @param input Input buffer (unused).
 * @param output Output buffer.
 * @param frameCount Number of frames.
 * @param timeInfo Timing information.
 * @param statusFlags Status flags.
 * @param userData Pointer to AudioDevice.
 * @return paContinue to continue streaming.
 */
int AudioDevice::StreamCallback(const void *input, void *output,
                                unsigned long frameCount,
                                const PaStreamCallbackTimeInfo *timeInfo,
                                PaStreamCallbackFlags statusFlags,
                                void *userData) {
  auto start = std::chrono::steady_clock::now();
  AudioDevice *device = static_cast<AudioDevice *>(userData);
  std::unique_lock<std::shared_mutex> lock(device->streamMutex_);
  AudioStream *stream = &device->audioStream_;
  try {
    if (!stream || !stream->isPlaying) {
      spdlog::trace(fmt::format("Audio stream not playing"));
      std::memset(output, 0, frameCount * stream->numChannels * sizeof(float));
      stream->cacheHits++;
      device->stats_.cacheHits++;
      return paContinue;
    }
    float *out = static_cast<float *>(output);
    size_t framesToCopy = frameCount;
    if (stream->buffers.empty()) {
      stream->bufferUnderruns++;
      device->stats_.bufferUnderruns++;
      // Only log every 100 underruns to avoid spam
      if (stream->bufferUnderruns % 100 == 1) {
        spdlog::warn(fmt::format("Audio buffer underrun, count={}",
                                 stream->bufferUnderruns));
      }
      std::memset(out, 0, frameCount * stream->numChannels * sizeof(float));
      stream->cacheMisses++;
      device->stats_.cacheMisses++;
      return paContinue;
    }
    size_t availableFrames =
        stream->buffers.front().size() / stream->numChannels;
    if (availableFrames < framesToCopy) {
      stream->bufferUnderruns++;
      device->stats_.bufferUnderruns++;
      spdlog::warn(
          fmt::format("Audio underflow: available={} frames, requested={}",
                      availableFrames, framesToCopy));
      framesToCopy = availableFrames;
    }
    for (size_t i = 0; i < framesToCopy * stream->numChannels; ++i) {
      out[i] = stream->buffers.front()[i] * stream->volume;
    }
    if (framesToCopy < frameCount) {
      std::memset(out + framesToCopy * stream->numChannels, 0,
                  (frameCount - framesToCopy) * stream->numChannels *
                      sizeof(float));
    }
    stream->buffers.pop();
    stream->cacheHits++;
    device->stats_.cacheHits++;
    device->stats_.bufferQueueSize = stream->buffers.size();
    auto end = std::chrono::steady_clock::now();
    device->stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return paContinue;
  } catch (const std::exception &e) {
    spdlog::error(fmt::format("Stream callback failed: {}", e.what()));
    stream->cacheMisses++;
    device->stats_.cacheMisses++;
    return paContinue;
  }
}

/**
 * @brief Initializes the audio device.
 * @param deviceIndex PortAudio device index.
 * @return True on success, false otherwise.
 */
bool AudioDevice::Initialize(PaDeviceIndex deviceIndex) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(streamMutex_);
  try {
    PaError err = Pa_Initialize();
    if (err != paNoError) {
      spdlog::error(fmt::format("PortAudio initialization failed: {}",
                                Pa_GetErrorText(err)));
      return false;
    }
    PaStreamParameters outputParams = {};
    outputParams.device =
        (deviceIndex == -1) ? Pa_GetDefaultOutputDevice() : deviceIndex;
    if (outputParams.device == paNoDevice) {
      spdlog::error(fmt::format("No valid audio device found"));
      Pa_Terminate();
      throw AudioException("No valid audio device");
    }
    outputParams.channelCount = audioStream_.numChannels;
    outputParams.sampleFormat = paFloat32;
    outputParams.suggestedLatency =
        Pa_GetDeviceInfo(outputParams.device)->defaultLowOutputLatency;
    outputParams.hostApiSpecificStreamInfo = nullptr;
    err = Pa_OpenDefaultStream(
        &audioStream_.stream, 0, audioStream_.numChannels, paFloat32,
        audioStream_.sampleRate, FRAMES_PER_BUFFER, StreamCallback, this);
    if (err != paNoError) {
      spdlog::error(fmt::format("PortAudio open default stream failed: {}",
                                Pa_GetErrorText(err)));
      return false;
    }
    initialized_ = true;
    stats_.numChannels = audioStream_.numChannels;
    audioStream_.cacheHits++;
    stats_.cacheHits++;
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info(fmt::format("AudioDevice initialized with device {}",
                             outputParams.device));
    return true;
  } catch (const std::exception &e) {
    spdlog::error(
        fmt::format("AudioDevice initialization exception: {}", e.what()));
    return false;
  }
}

/**
 * @brief Starts audio playback.
 */
void AudioDevice::Start() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(streamMutex_);
  try {
    if (initialized_ && audioStream_.stream && !audioStream_.isPlaying) {
      PaError err = Pa_StartStream(audioStream_.stream);
      if (err != paNoError) {
        spdlog::error(fmt::format("PortAudio start stream failed: {}",
                                  Pa_GetErrorText(err)));
        throw AudioException("PortAudio stream start failed: " +
                             std::string(Pa_GetErrorText(err)));
      }
      audioStream_.isPlaying = true;
      audioStream_.cacheHits++;
      stats_.cacheHits++;
      spdlog::info("AudioDevice started");
    }
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  } catch (const std::exception &e) {
    spdlog::error(fmt::format("AudioDevice start failed: {}", e.what()));
    audioStream_.cacheMisses++;
    stats_.cacheMisses++;
  }
}

/**
 * @brief Stops audio playback.
 */
void AudioDevice::Stop() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(streamMutex_);
  try {
    if (initialized_ && audioStream_.stream) {
      audioStream_.isPlaying = false;
      PaError err = Pa_StopStream(audioStream_.stream);
      if (err != paNoError) {
        spdlog::warn(fmt::format("PortAudio stop stream failed: {}",
                                 Pa_GetErrorText(err)));
      }
      err = Pa_CloseStream(audioStream_.stream);
      if (err != paNoError) {
        spdlog::warn(fmt::format("PortAudio close stream failed: {}",
                                 Pa_GetErrorText(err)));
      }
      audioStream_.stream = nullptr;
      while (!audioStream_.buffers.empty()) {
        audioStream_.buffers.pop();
      }
      audioStream_.cacheHits++;
      stats_.cacheHits++;
      stats_.bufferQueueSize = 0;
      spdlog::info(fmt::format("AudioDevice stopped"));
    }
    Pa_Terminate();
    initialized_ = false;
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  } catch (const std::exception &e) {
    spdlog::error(fmt::format("AudioDevice stop failed: {}", e.what()));
    audioStream_.cacheMisses++;
    stats_.cacheMisses++;
  }
}

/**
 * @brief Queues an audio buffer for playback.
 * @param buffer Audio data.
 */
void AudioDevice::QueueBuffer(const std::vector<float> &buffer) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(streamMutex_);
  try {
    if (!initialized_) {
      spdlog::warn(fmt::format(
          "Attempted to queue buffer on uninitialized audio device"));
      audioStream_.cacheMisses++;
      stats_.cacheMisses++;
      return;
    }
    if (buffer.size() % audioStream_.numChannels != 0) {
      spdlog::warn(
          fmt::format("Invalid buffer size: {} not divisible by channels {}",
                      buffer.size(), audioStream_.numChannels));
      audioStream_.cacheMisses++;
      stats_.cacheMisses++;
      return;
    }
    audioStream_.buffers.push(buffer);
    audioStream_.cacheHits++;
    stats_.cacheHits++;
    stats_.bufferQueueSize = audioStream_.buffers.size();
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(fmt::format("Queued audio buffer of size {}", buffer.size()));
  } catch (const std::exception &e) {
    spdlog::error(fmt::format("Queue buffer failed: {}", e.what()));
    audioStream_.cacheMisses++;
    stats_.cacheMisses++;
  }
}

/**
 * @brief Sets the volume level.
 * @param volume Volume (0.0 to 1.0).
 */
void AudioDevice::SetVolume(float volume) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(streamMutex_);
  try {
    audioStream_.volume = std::clamp(volume, 0.0f, 1.0f);
    audioStream_.cacheHits++;
    stats_.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        fmt::format("Set audio volume to {:.2f}", audioStream_.volume));
  } catch (const std::exception &e) {
    spdlog::error(fmt::format("Set volume failed: {}", e.what()));
    audioStream_.cacheMisses++;
    stats_.cacheMisses++;
  }
}

/**
 * @brief Reconfigures the stream with new parameters.
 * @param sampleRate New sample rate.
 * @param numChannels New number of channels.
 * @return True on success, false otherwise.
 */
bool AudioDevice::Reconfigure(int sampleRate, int numChannels) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(streamMutex_);
  try {
    if (!initialized_) {
      spdlog::warn("Cannot reconfigure uninitialized audio device");
      audioStream_.cacheMisses++;
      stats_.cacheMisses++;
      return false;
    }
    if (numChannels < 1 || numChannels > MAX_CHANNELS) {
      spdlog::warn(fmt::format("Invalid channel count: {}", numChannels));
      audioStream_.cacheMisses++;
      stats_.cacheMisses++;
      return false;
    }
    Stop();
    audioStream_.sampleRate = sampleRate;
    audioStream_.numChannels = numChannels;
    stats_.numChannels = numChannels;
    bool success = Initialize(-1); // Reinitialize with default device
    audioStream_.cacheHits++;
    stats_.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info(
        fmt::format("AudioDevice reconfigured: sampleRate={}, numChannels={}",
                    sampleRate, numChannels));
    return success;
  } catch (const std::exception &e) {
    spdlog::error(fmt::format("Reconfigure audio device failed: {}", e.what()));
    audioStream_.cacheMisses++;
    stats_.cacheMisses++;
    return false;
  }
}

/**
 * @brief Retrieves audio statistics.
 * @return Current statistics.
 */
AudioDevice::Stats &AudioDevice::GetStats() {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(streamMutex_);
  try {
    stats_.bufferQueueSize = audioStream_.buffers.size();
    stats_.numChannels = audioStream_.numChannels;
    audioStream_.cacheHits++;
    stats_.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return stats_;
  } catch (const std::exception &e) {
    spdlog::error(fmt::format("Get audio stats failed: {}", e.what()));
    stats_.cacheMisses++;
    return stats_;
  }
}

/**
 * @brief Saves the audio device state.
 * @param out Output stream.
 */
void AudioDevice::SaveState(std::ostream &out) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(streamMutex_);
  try {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    out.write(reinterpret_cast<const char *>(&audioStream_.sampleRate),
              sizeof(audioStream_.sampleRate));
    out.write(reinterpret_cast<const char *>(&audioStream_.numChannels),
              sizeof(audioStream_.numChannels));
    out.write(reinterpret_cast<const char *>(&audioStream_.volume),
              sizeof(audioStream_.volume));
    out.write(reinterpret_cast<const char *>(&audioStream_.isPlaying),
              sizeof(audioStream_.isPlaying));
    out.write(reinterpret_cast<const char *>(&audioStream_.bufferUnderruns),
              sizeof(audioStream_.bufferUnderruns));
    out.write(reinterpret_cast<const char *>(&audioStream_.cacheHits),
              sizeof(audioStream_.cacheHits));
    out.write(reinterpret_cast<const char *>(&audioStream_.cacheMisses),
              sizeof(audioStream_.cacheMisses));
    uint64_t bufferCount = audioStream_.buffers.size();
    out.write(reinterpret_cast<const char *>(&bufferCount),
              sizeof(bufferCount));
    auto tempBuffers = audioStream_.buffers;
    while (!tempBuffers.empty()) {
      const auto &buffer = tempBuffers.front();
      uint64_t bufferSize = buffer.size();
      out.write(reinterpret_cast<const char *>(&bufferSize),
                sizeof(bufferSize));
      out.write(reinterpret_cast<const char *>(buffer.data()),
                bufferSize * sizeof(float));
      tempBuffers.pop();
    }
    out.write(reinterpret_cast<const char *>(&stats_), sizeof(stats_));
    if (!out.good()) {
      throw std::runtime_error("Failed to write audio state");
    }
    const_cast<AudioStream &>(audioStream_).cacheHits++;
    const_cast<Stats &>(stats_).cacheHits++;
    auto end = std::chrono::steady_clock::now();
    const_cast<Stats &>(stats_).totalLatencyUs =
        const_cast<Stats &>(stats_).totalLatencyUs +
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info(
        fmt::format("AudioDevice state saved: {} buffers", bufferCount));
  } catch (const std::exception &e) {
    spdlog::error(fmt::format("AudioDevice SaveState failed: {}", e.what()));
    const_cast<AudioStream &>(audioStream_).cacheMisses++;
    const_cast<Stats &>(stats_).cacheMisses++;
  }
}

/**
 * @brief Loads the audio device state.
 * @param in Input stream.
 */
void AudioDevice::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(streamMutex_);
  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      spdlog::error(
          fmt::format("Unsupported audio state version: {}", version));
      throw std::runtime_error("Invalid audio state version");
    }
    Stop();
    int sampleRate, numChannels;
    in.read(reinterpret_cast<char *>(&sampleRate), sizeof(sampleRate));
    in.read(reinterpret_cast<char *>(&numChannels), sizeof(numChannels));
    in.read(reinterpret_cast<char *>(&audioStream_.volume),
            sizeof(audioStream_.volume));
    in.read(reinterpret_cast<char *>(&audioStream_.isPlaying),
            sizeof(audioStream_.isPlaying));
    in.read(reinterpret_cast<char *>(&audioStream_.bufferUnderruns),
            sizeof(audioStream_.bufferUnderruns));
    in.read(reinterpret_cast<char *>(&audioStream_.cacheHits),
            sizeof(audioStream_.cacheHits));
    in.read(reinterpret_cast<char *>(&audioStream_.cacheMisses),
            sizeof(audioStream_.cacheMisses));
    uint64_t bufferCount;
    in.read(reinterpret_cast<char *>(&bufferCount), sizeof(bufferCount));
    while (!audioStream_.buffers.empty()) {
      audioStream_.buffers.pop();
    }
    for (uint64_t i = 0; i < bufferCount && in.good(); ++i) {
      uint64_t bufferSize;
      in.read(reinterpret_cast<char *>(&bufferSize), sizeof(bufferSize));
      std::vector<float> buffer(bufferSize);
      in.read(reinterpret_cast<char *>(buffer.data()),
              bufferSize * sizeof(float));
      audioStream_.buffers.push(std::move(buffer));
    }
    in.read(reinterpret_cast<char *>(&stats_), sizeof(stats_));
    if (!in.good()) {
      throw std::runtime_error("Failed to read audio state");
    }
    audioStream_.sampleRate = sampleRate;
    audioStream_.numChannels = numChannels;
    stats_.numChannels = numChannels;
    stats_.bufferQueueSize = audioStream_.buffers.size();
    if (initialized_) {
      Reconfigure(sampleRate, numChannels);
    }
    if (audioStream_.isPlaying) {
      Start();
    }
    audioStream_.cacheHits++;
    stats_.cacheHits++;
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    stats_.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info(
        fmt::format("AudioDevice state loaded: {} buffers", bufferCount));
  } catch (const std::exception &e) {
    spdlog::error(fmt::format("AudioDevice LoadState failed: {}", e.what()));
    audioStream_.cacheMisses++;
    stats_.cacheMisses++;
  }
}

/**
 * @brief Constructs the audio system.
 * @param memory Reference to the PS4 MMU.
 */
PS4Audio::PS4Audio(PS4MMU &memory) : m_memory(memory) {
  auto start = std::chrono::steady_clock::now();
  spdlog::info(fmt::format("PS4Audio constructed"));
  auto end = std::chrono::steady_clock::now();
  m_device.GetStats().totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Initializes the audio system.
 * @return True on success, false otherwise.
 */
bool PS4Audio::Initialize() {
  auto start = std::chrono::steady_clock::now();
  try {
    bool success = m_device.Initialize(-1);
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    return success;
  } catch (const std::exception &e) {
    spdlog::error(fmt::format("PS4Audio initialization failed: {}", e.what()));
    return false;
  }
}

/**
 * @brief Shuts down the audio system.
 */
void PS4Audio::Shutdown() {
  auto start = std::chrono::steady_clock::now();
  try {
    m_device.Stop();
    spdlog::info(fmt::format("PS4Audio shutdown"));
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  } catch (const std::exception &e) {
    spdlog::error(fmt::format("PS4Audio shutdown failed: {}", e.what()));
  }
}

/**
 * @brief Processes audio samples from MMU.
 * @param samples Number of samples to process.
 * @param address MMU address for audio data.
 */
void PS4Audio::ProcessAudio(size_t samples, uint64_t address) {
  auto start = std::chrono::steady_clock::now();
  try {
    uint64_t pid = 1;
    size_t bufferSize = samples * m_device.GetNumChannels() * sizeof(float);
    std::vector<float> buffer(samples * m_device.GetNumChannels());
    if (!m_memory.ReadVirtual(address, buffer.data(), bufferSize, pid)) {
      spdlog::warn(
          fmt::format("Failed to read audio data: addr=0x{:x}", address));
      std::fill(buffer.begin(), buffer.end(), 0.0f);
    }
    m_device.QueueBuffer(buffer);
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  } catch (const std::exception &e) {
    spdlog::error(fmt::format("Process audio failed: {}", e.what()));
  }
}

/**
 * @brief Saves the audio system state.
 * @param out Output stream.
 */
void PS4Audio::SaveState(std::ostream &out) const { m_device.SaveState(out); }

/**
 * @brief Loads the audio system state.
 * @param in Input stream.
 */
void PS4Audio::LoadState(std::istream &in) { m_device.LoadState(in); }

/**
 * @brief Processes enhanced audio buffer with DSP effects.
 * @param buffer Enhanced audio buffer.
 * @return True on success, false otherwise.
 */
bool PS4Audio::ProcessEnhancedAudio(const EnhancedAudioBuffer &buffer) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_audioMutex);
  try {
    std::vector<float> processedData = buffer.data;

    // Apply 3D audio processing if enabled
    if (buffer.is3D && m_audio3DMode != Audio3DMode::DISABLED) {
      ProcessAudio3D(const_cast<EnhancedAudioBuffer &>(
                         const_cast<EnhancedAudioBuffer &>(buffer)),
                     m_listenerPosition, m_listenerOrientation, m_audio3DMode);
    }

    // Apply active DSP effects
    for (const auto &effect : m_dspEffects) {
      if (effect.enabled) {
        ApplyDSPEffect(processedData, effect);
      }
    }

    // Queue processed audio
    m_device.QueueBuffer(processedData);

    m_stats.samplesProcessed += processedData.size();
    m_stats.dspOperations++;
    m_stats.cacheHits++;

    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ProcessEnhancedAudio failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Applies DSP effect to audio data.
 * @param audioData Audio data to process.
 * @param params DSP effect parameters.
 * @return True on success, false otherwise.
 */
bool PS4Audio::ApplyDSPEffect(std::vector<float> &audioData,
                              const DSPEffectParams &params) {
  try {
    switch (params.type) {
    case DSPEffectType::REVERB:
      return ProcessReverb(audioData, params.intensity, params.feedback);
    case DSPEffectType::DELAY:
      return ProcessDelay(audioData, params.frequency, params.feedback);
    case DSPEffectType::CHORUS:
      return ProcessChorus(audioData, params.frequency, params.intensity);
    case DSPEffectType::COMPRESSOR:
      return ProcessCompressor(audioData, params.intensity, params.feedback);
    case DSPEffectType::EQUALIZER: {
      std::vector<float> bands = {params.intensity, params.frequency,
                                  params.feedback};
      return ProcessEqualizer(audioData, bands);
    }
    case DSPEffectType::LIMITER:
      return ProcessLimiter(audioData, params.intensity);
    default:
      spdlog::warn("Unknown DSP effect type: {}",
                   static_cast<int>(params.type));
      return false;
    }
  } catch (const std::exception &e) {
    spdlog::error("ApplyDSPEffect failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Processes reverb effect on audio data.
 * @param audioData Audio data to process.
 * @param intensity Reverb intensity (0.0-1.0).
 * @param feedback Reverb feedback amount (0.0-1.0).
 * @return True on success, false otherwise.
 */
bool PS4Audio::ProcessReverb(std::vector<float> &audioData, float intensity,
                             float feedback) {
  try {
    if (audioData.empty() || intensity <= 0.0f)
      return true;

    const size_t delayLength = static_cast<size_t>(48000 * 0.05f); // 50ms delay
    static std::vector<float> delayBuffer(delayLength, 0.0f);
    static size_t delayIndex = 0;

    for (size_t i = 0; i < audioData.size(); ++i) {
      float delayed = delayBuffer[delayIndex];
      float reverb = delayed * feedback * intensity;

      delayBuffer[delayIndex] = audioData[i] + reverb * 0.3f;
      audioData[i] = audioData[i] * (1.0f - intensity * 0.5f) + reverb;

      delayIndex = (delayIndex + 1) % delayLength;
    }

    m_stats.dspOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessReverb failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Processes delay effect on audio data.
 * @param audioData Audio data to process.
 * @param delayTime Delay time in seconds.
 * @param feedback Feedback amount (0.0-1.0).
 * @return True on success, false otherwise.
 */
bool PS4Audio::ProcessDelay(std::vector<float> &audioData, float delayTime,
                            float feedback) {
  try {
    if (audioData.empty() || delayTime <= 0.0f)
      return true;

    const size_t delayLength =
        static_cast<size_t>(48000 * std::min(delayTime, 1.0f));
    static std::vector<float> delayBuffer(delayLength, 0.0f);
    static size_t delayIndex = 0;

    for (size_t i = 0; i < audioData.size(); ++i) {
      float delayed = delayBuffer[delayIndex];

      delayBuffer[delayIndex] = audioData[i] + delayed * feedback;
      audioData[i] = audioData[i] + delayed * 0.5f;

      delayIndex = (delayIndex + 1) % delayLength;
    }

    m_stats.dspOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessDelay failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Processes chorus effect on audio data.
 * @param audioData Audio data to process.
 * @param rate Chorus modulation rate (Hz).
 * @param depth Chorus modulation depth (0.0-1.0).
 * @return True on success, false otherwise.
 */
bool PS4Audio::ProcessChorus(std::vector<float> &audioData, float rate,
                             float depth) {
  try {
    if (audioData.empty() || depth <= 0.0f)
      return true;

    const size_t maxDelay =
        static_cast<size_t>(48000 * 0.02f); // 20ms max delay
    static std::vector<float> delayBuffer(maxDelay, 0.0f);
    static size_t writeIndex = 0;
    static float lfoPhase = 0.0f;

    const float sampleRate = 48000.0f;
    const float lfoIncrement = 2.0f * M_PI * rate / sampleRate;

    for (size_t i = 0; i < audioData.size(); ++i) {
      // LFO for varying delay
      float lfo = std::sin(lfoPhase) * depth * 0.5f + 0.5f;
      float delayTime = lfo * (maxDelay - 1);

      size_t readIndex =
          static_cast<size_t>(writeIndex - delayTime + maxDelay) % maxDelay;
      float delayedSample = delayBuffer[readIndex];

      delayBuffer[writeIndex] = audioData[i];
      audioData[i] = audioData[i] * 0.7f + delayedSample * 0.3f;

      writeIndex = (writeIndex + 1) % maxDelay;
      lfoPhase += lfoIncrement;
      if (lfoPhase >= 2.0f * M_PI)
        lfoPhase -= 2.0f * M_PI;
    }

    m_stats.dspOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessChorus failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Processes compressor effect on audio data.
 * @param audioData Audio data to process.
 * @param threshold Compression threshold (0.0-1.0).
 * @param ratio Compression ratio (1.0+).
 * @return True on success, false otherwise.
 */
bool PS4Audio::ProcessCompressor(std::vector<float> &audioData, float threshold,
                                 float ratio) {
  try {
    if (audioData.empty() || threshold >= 1.0f || ratio <= 1.0f)
      return true;

    static float envelope = 0.0f;
    const float attack = 0.003f; // 3ms attack
    const float release = 0.1f;  // 100ms release

    for (size_t i = 0; i < audioData.size(); ++i) {
      float sample = std::abs(audioData[i]);

      // Envelope follower
      if (sample > envelope) {
        envelope = envelope + (sample - envelope) * attack;
      } else {
        envelope = envelope + (sample - envelope) * release;
      }

      // Apply compression
      if (envelope > threshold) {
        float excess = envelope - threshold;
        float compressedExcess = excess / ratio;
        float gain = (threshold + compressedExcess) / envelope;
        audioData[i] *= gain;
      }
    }

    m_stats.dspOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessCompressor failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Processes equalizer effect on audio data.
 * @param audioData Audio data to process.
 * @param bands Equalizer band gains.
 * @return True on success, false otherwise.
 */
bool PS4Audio::ProcessEqualizer(std::vector<float> &audioData,
                                const std::vector<float> &bands) {
  try {
    if (audioData.empty() || bands.empty())
      return true;

    // Simple 3-band EQ: Low, Mid, High
    static float lowState = 0.0f, midState = 0.0f, highState = 0.0f;

    const float lowGain = bands.size() > 0 ? bands[0] : 1.0f;
    const float midGain = bands.size() > 1 ? bands[1] : 1.0f;
    const float highGain = bands.size() > 2 ? bands[2] : 1.0f;

    for (size_t i = 0; i < audioData.size(); ++i) {
      float sample = audioData[i];

      // Simple IIR filters for frequency separation
      lowState = lowState * 0.99f + sample * 0.01f;
      float low = lowState * lowGain;

      midState = midState * 0.9f + (sample - lowState) * 0.1f;
      float mid = midState * midGain;

      highState = sample - lowState - midState;
      float high = highState * highGain;

      audioData[i] = low + mid + high;
    }

    m_stats.dspOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessEqualizer failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Processes limiter effect on audio data.
 * @param audioData Audio data to process.
 * @param threshold Limiter threshold (0.0-1.0).
 * @return True on success, false otherwise.
 */
bool PS4Audio::ProcessLimiter(std::vector<float> &audioData, float threshold) {
  try {
    if (audioData.empty() || threshold >= 1.0f)
      return true;

    for (size_t i = 0; i < audioData.size(); ++i) {
      float sample = audioData[i];

      // Hard limiter
      if (std::abs(sample) > threshold) {
        audioData[i] = sample > 0.0f ? threshold : -threshold;
      }
    }

    m_stats.dspOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessLimiter failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Applies HRTF processing for 3D audio.
 * @param audioData Audio data to process.
 * @param sourcePos 3D source position.
 * @return True on success, false otherwise.
 */
bool PS4Audio::ApplyHRTF(std::vector<float> &audioData,
                         const float sourcePos[3]) {
  try {
    if (audioData.empty())
      return true;

    // Calculate distance and angle
    float dx = sourcePos[0] - m_listenerPosition[0];
    float dy = sourcePos[1] - m_listenerPosition[1];
    float dz = sourcePos[2] - m_listenerPosition[2];

    float distance = std::sqrt(dx * dx + dy * dy + dz * dz);
    float azimuth = std::atan2(dz, dx);

    // Simple HRTF simulation with delay and filtering
    float leftGain = 0.5f + 0.5f * std::cos(azimuth);
    float rightGain = 0.5f - 0.5f * std::cos(azimuth);

    // Distance attenuation
    float attenuation = 1.0f / (1.0f + distance * 0.1f);
    leftGain *= attenuation;
    rightGain *= attenuation;

    // Apply gains (assuming stereo interleaved data)
    for (size_t i = 0; i < audioData.size(); i += 2) {
      if (i + 1 < audioData.size()) {
        audioData[i] *= leftGain;      // Left channel
        audioData[i + 1] *= rightGain; // Right channel
      }
    }

    m_stats.audio3DOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ApplyHRTF failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Applies surround sound processing.
 * @param audioData Audio data to process.
 * @param channels Number of channels.
 * @return True on success, false otherwise.
 */
bool PS4Audio::ApplySurroundProcessing(std::vector<float> &audioData,
                                       uint32_t channels) {
  try {
    if (audioData.empty() || channels < 2)
      return true;

    // Simple surround processing - expand stereo to multi-channel
    if (channels == 6) { // 5.1 surround
      std::vector<float> processed;
      processed.reserve(audioData.size() * 3); // 2 -> 6 channels

      for (size_t i = 0; i < audioData.size(); i += 2) {
        if (i + 1 < audioData.size()) {
          float left = audioData[i];
          float right = audioData[i + 1];
          float center = (left + right) * 0.5f;

          processed.push_back(left);         // Front Left
          processed.push_back(right);        // Front Right
          processed.push_back(center);       // Center
          processed.push_back(0.0f);         // LFE
          processed.push_back(left * 0.3f);  // Rear Left
          processed.push_back(right * 0.3f); // Rear Right
        }
      }
      audioData = std::move(processed);
    }

    m_stats.audio3DOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ApplySurroundProcessing failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Applies binaural processing for headphone 3D audio.
 * @param audioData Audio data to process.
 * @return True on success, false otherwise.
 */
bool PS4Audio::ApplyBinauralProcessing(std::vector<float> &audioData) {
  try {
    if (audioData.empty())
      return true;

    // Simple binaural processing with crossfeed
    const float crossfeedGain = 0.3f;
    const float directGain = 0.7f;

    for (size_t i = 0; i < audioData.size(); i += 2) {
      if (i + 1 < audioData.size()) {
        float left = audioData[i];
        float right = audioData[i + 1];

        // Apply crossfeed
        audioData[i] = left * directGain + right * crossfeedGain;
        audioData[i + 1] = right * directGain + left * crossfeedGain;
      }
    }

    m_stats.audio3DOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ApplyBinauralProcessing failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Processes 3D audio with specified mode.
 * @param buffer Audio buffer to process.
 * @param listenerPos Listener position.
 * @param listenerOrientation Listener orientation.
 * @param mode 3D audio processing mode.
 * @return True on success, false otherwise.
 */
bool PS4Audio::ProcessAudio3D(EnhancedAudioBuffer &buffer,
                              const float listenerPos[3],
                              const float listenerOrientation[6],
                              Audio3DMode mode) {
  try {
    switch (mode) {
    case Audio3DMode::HRTF:
      return ApplyHRTF(buffer.data, buffer.position);
    case Audio3DMode::SURROUND:
      return ApplySurroundProcessing(buffer.data, buffer.channels);
    case Audio3DMode::BINAURAL:
      return ApplyBinauralProcessing(buffer.data);
    case Audio3DMode::DISABLED:
    default:
      return true;
    }
  } catch (const std::exception &e) {
    spdlog::error("ProcessAudio3D failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Decompresses ADPCM audio format.
 * @param compressed Compressed audio data.
 * @param output Decompressed output.
 * @return True on success, false otherwise.
 */
bool PS4Audio::DecompressADPCM(const std::vector<uint8_t> &compressed,
                               std::vector<float> &output) {
  try {
    if (compressed.empty())
      return false;

    // Simple ADPCM decompression simulation
    output.clear();
    output.reserve(compressed.size() * 2);

    int16_t predictedValue = 0;
    int stepIndex = 0;
    const int stepTable[] = {7,  8,  9,  10, 11, 12, 13, 14,
                             16, 17, 19, 21, 23, 25, 28, 31};

    for (size_t i = 0; i < compressed.size(); ++i) {
      uint8_t nibbles = compressed[i];

      // Process both nibbles
      for (int j = 0; j < 2; ++j) {
        uint8_t nibble = (j == 0) ? (nibbles & 0x0F) : ((nibbles >> 4) & 0x0F);

        int step = stepTable[std::min(stepIndex, 15)];
        int diff = step >> 3;

        if (nibble & 4)
          diff += step;
        if (nibble & 2)
          diff += step >> 1;
        if (nibble & 1)
          diff += step >> 2;

        if (nibble & 8) {
          predictedValue -= diff;
        } else {
          predictedValue += diff;
        }

        predictedValue =
            std::clamp(predictedValue, static_cast<int16_t>(-32768),
                       static_cast<int16_t>(32767));
        output.push_back(static_cast<float>(predictedValue) / 32768.0f);

        stepIndex += (nibble & 8) ? -1 : 1;
        stepIndex = std::clamp(stepIndex, 0, 15);
      }
    }

    m_stats.compressionOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("DecompressADPCM failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Decompresses AT9 audio format (placeholder implementation).
 * @param compressed Compressed audio data.
 * @param output Decompressed output.
 * @return True on success, false otherwise.
 */
bool PS4Audio::DecompressAT9(const std::vector<uint8_t> &compressed,
                             std::vector<float> &output) {
  try {
    // AT9 decompression would require Sony's ATRAC9 decoder
    // For now, return silence as placeholder
    spdlog::warn("AT9 decompression not fully implemented - returning silence");
    output.clear();
    output.resize(compressed.size() * 4, 0.0f); // Estimate expansion ratio

    m_stats.compressionOperations++;
    m_stats.errorCount++; // Count as error since not fully implemented
    return false;
  } catch (const std::exception &e) {
    spdlog::error("DecompressAT9 failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Decompresses MP3 audio format (placeholder implementation).
 * @param compressed Compressed audio data.
 * @param output Decompressed output.
 * @return True on success, false otherwise.
 */
bool PS4Audio::DecompressMP3(const std::vector<uint8_t> &compressed,
                             std::vector<float> &output) {
  try {
    // MP3 decompression would require libmp3lame or similar
    spdlog::warn("MP3 decompression not fully implemented - returning silence");
    output.clear();
    output.resize(compressed.size() * 8, 0.0f); // Estimate expansion ratio

    m_stats.compressionOperations++;
    m_stats.errorCount++;
    return false;
  } catch (const std::exception &e) {
    spdlog::error("DecompressMP3 failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Decompresses AAC audio format (placeholder implementation).
 * @param compressed Compressed audio data.
 * @param output Decompressed output.
 * @return True on success, false otherwise.
 */
bool PS4Audio::DecompressAAC(const std::vector<uint8_t> &compressed,
                             std::vector<float> &output) {
  try {
    // AAC decompression would require libfaac or similar
    spdlog::warn("AAC decompression not fully implemented - returning silence");
    output.clear();
    output.resize(compressed.size() * 6, 0.0f); // Estimate expansion ratio

    m_stats.compressionOperations++;
    m_stats.errorCount++;
    return false;
  } catch (const std::exception &e) {
    spdlog::error("DecompressAAC failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Decompresses Opus audio format (placeholder implementation).
 * @param compressed Compressed audio data.
 * @param output Decompressed output.
 * @return True on success, false otherwise.
 */
bool PS4Audio::DecompressOpus(const std::vector<uint8_t> &compressed,
                              std::vector<float> &output) {
  try {
    // Opus decompression would require libopus
    spdlog::warn(
        "Opus decompression not fully implemented - returning silence");
    output.clear();
    output.resize(compressed.size() * 10, 0.0f); // Estimate expansion ratio

    m_stats.compressionOperations++;
    m_stats.errorCount++;
    return false;
  } catch (const std::exception &e) {
    spdlog::error("DecompressOpus failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Converts PCM format to float.
 * @param input Input PCM data.
 * @param inputFormat Input format.
 * @param output Output float data.
 * @return True on success, false otherwise.
 */
bool PS4Audio::ConvertPCMFormat(const std::vector<uint8_t> &input,
                                PS4AudioFormat inputFormat,
                                std::vector<float> &output) {
  try {
    output.clear();

    switch (inputFormat) {
    case PS4AudioFormat::PCM_S16: {
      output.reserve(input.size() / 2);
      for (size_t i = 0; i < input.size(); i += 2) {
        if (i + 1 < input.size()) {
          int16_t sample = static_cast<int16_t>(input[i] | (input[i + 1] << 8));
          output.push_back(static_cast<float>(sample) / 32768.0f);
        }
      }
    } break;

    case PS4AudioFormat::PCM_S24: {
      output.reserve(input.size() / 3);
      for (size_t i = 0; i < input.size(); i += 3) {
        if (i + 2 < input.size()) {
          int32_t sample = static_cast<int32_t>(input[i] | (input[i + 1] << 8) |
                                                (input[i + 2] << 16));
          if (sample & 0x800000)
            sample |= 0xFF000000; // Sign extend
          output.push_back(static_cast<float>(sample) / 8388608.0f);
        }
      }
    } break;

    case PS4AudioFormat::PCM_F32: {
      output.reserve(input.size() / 4);
      for (size_t i = 0; i < input.size(); i += 4) {
        if (i + 3 < input.size()) {
          float sample;
          std::memcpy(&sample, &input[i], sizeof(float));
          output.push_back(sample);
        }
      }
    } break;

    default:
      spdlog::error("Unsupported PCM format: {}",
                    static_cast<int>(inputFormat));
      return false;
    }

    m_stats.compressionOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ConvertPCMFormat failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Decompresses audio based on format.
 * @param compressed Compressed audio data.
 * @param format Audio format.
 * @param output Decompressed output.
 * @return True on success, false otherwise.
 */
bool PS4Audio::DecompressAudio(const std::vector<uint8_t> &compressed,
                               PS4AudioFormat format,
                               std::vector<float> &output) {
  switch (format) {
  case PS4AudioFormat::PCM_S16:
  case PS4AudioFormat::PCM_S24:
  case PS4AudioFormat::PCM_F32:
    return ConvertPCMFormat(compressed, format, output);
  case PS4AudioFormat::ADPCM:
    return DecompressADPCM(compressed, output);
  case PS4AudioFormat::AT9:
    return DecompressAT9(compressed, output);
  case PS4AudioFormat::MP3:
    return DecompressMP3(compressed, output);
  case PS4AudioFormat::AAC:
    return DecompressAAC(compressed, output);
  case PS4AudioFormat::OPUS:
    return DecompressOpus(compressed, output);
  default:
    spdlog::error("Unknown audio format: {}", static_cast<int>(format));
    return false;
  }
}

/**
 * @brief Processes multi-channel audio mixing.
 * @param channels Multiple audio channels.
 * @param mixedOutput Mixed output.
 * @return True on success, false otherwise.
 */
bool PS4Audio::ProcessMultiChannelAudio(
    const std::vector<EnhancedAudioBuffer> &channels,
    std::vector<float> &mixedOutput) {
  try {
    if (channels.empty())
      return false;

    // Find the maximum buffer size
    size_t maxSize = 0;
    for (const auto &channel : channels) {
      maxSize = std::max(maxSize, channel.data.size());
    }

    mixedOutput.clear();
    mixedOutput.resize(maxSize, 0.0f);

    // Mix all channels with priority weighting
    for (const auto &channel : channels) {
      float weight = static_cast<float>(channel.priority) / 100.0f;
      weight = std::clamp(weight, 0.1f, 1.0f);

      for (size_t i = 0; i < std::min(channel.data.size(), maxSize); ++i) {
        mixedOutput[i] += channel.data[i] * weight;
      }
    }

    // Normalize to prevent clipping
    float maxSample = 0.0f;
    for (float sample : mixedOutput) {
      maxSample = std::max(maxSample, std::abs(sample));
    }

    if (maxSample > 1.0f) {
      float normFactor = 1.0f / maxSample;
      for (float &sample : mixedOutput) {
        sample *= normFactor;
      }
    }

    m_stats.dspOperations++;
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessMultiChannelAudio failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Gets enhanced audio statistics.
 * @return Audio statistics.
 */
PS4Audio::Stats PS4Audio::GetStats() const {
  std::shared_lock<std::shared_mutex> lock(m_audioMutex);
  return m_stats;
}
} // namespace ps4
