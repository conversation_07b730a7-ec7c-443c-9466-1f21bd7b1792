<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_TIMECHANGE_HANDLER_NP(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P STYLE="font-weight: medium">pthread_timechange_handler_np –
alert timed waiting condition variables to system time changes.</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> 
</P>
<P><B>void * pthread_timechange_handler_np(void * </B><I>dummy</I><B>);</B></P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>To improve tolerance against operator or time service initiated
system clock changes.</P>
<P><B>pthread_timechange_handler_np </B>can be called by an
application when it receives a WM_TIMECHANGE message from the system.
At present it broadcasts all condition variables so that waiting
threads can wake up and re-evaluate their conditions and restart
their timed waits if required.</P>
<P><B>pthread_timechange_handler_np </B>has the same return type and
argument type as a thread routine so that it may be called directly
through pthread_create(), i.e. as a separate thread. If run as a
thread, the return code must be retrieved through <A HREF="pthread_join.html"><B>pthread_join</B>()</A>.</P>
<P>Although the <I>dummy</I> parameter is required it is not used and
any value including NULL can be given.</P>
<H2><A HREF="#toc3" NAME="sect3">Cancellation</A></H2>
<P>None.</P>
<H2><A HREF="#toc4" NAME="sect4"><FONT COLOR="#000080">Return Value</FONT></A></H2>
<P><B>pthread_timechange_handler_np</B> returns 0 on success, or an
error code.</P>
<H2><A HREF="#toc5" NAME="sect5">Errors</A></H2>
<P>The <B>pthread_timechange_handler_np</B> function returns the
following error code on error: 
</P>
<DL>
	<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EAGAIN</B> 
		</DT></DL>
</DL>
<P STYLE="margin-left: 2cm; margin-bottom: 0cm">
To indicate that not all condition variables were signalled for some
reason.</P>
<H2><A HREF="#toc6" NAME="sect6">Author</A></H2>
<P>Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Cancellation</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Errors</A>
		</P>
	<LI><P><A HREF="#sect6" NAME="toc6">Author</A> 
	</P>
</UL>
</BODY>
</HTML>
