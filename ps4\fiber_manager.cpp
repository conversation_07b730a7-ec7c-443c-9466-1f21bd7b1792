// Copyright 2025 <Copyright Owner>

#include "fiber_manager.h"
#undef max
#undef min
#include "../memory/memory_diagnostics.h"
#include <algorithm>
#include <chrono>
#include <spdlog/spdlog.h>
#include <stdexcept>

namespace ps4 {

struct FiberException : std::runtime_error {
  explicit FiberException(const std::string &msg) : std::runtime_error(msg) {}
};

static FiberManager *s_currentManager = nullptr;

/**
 * @brief Constructs a FiberManager instance.
 */
FiberManager::FiberManager() {
  auto start = std::chrono::steady_clock::now();
  s_currentManager = this;
  m_stats = Stats();
  spdlog::info("FiberManager constructed");
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Destructs the FiberManager, ensuring cleanup.
 */
FiberManager::~FiberManager() {
  auto start = std::chrono::steady_clock::now();
  Shutdown();
  s_currentManager = nullptr;
  spdlog::info("FiberManager destroyed");
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Initializes the FiberManager.
 * @return True on success, false on failure.
 */
bool FiberManager::Initialize() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fiberMutex);
  try {
#ifdef _WIN32
    m_mainFiber = ConvertThreadToFiber(nullptr);
    if (!m_mainFiber) {
      spdlog::error("Failed to convert thread to fiber: {}", GetLastError());
      throw FiberException("Main fiber initialization failed");
    }
#else
    if (getcontext(&m_mainContext) != 0) {
      spdlog::error("Failed to get main context: {}", errno);
      throw FiberException("Main context initialization failed");
    }
#endif
    m_stats = Stats();
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("FiberManager initialized");
    return true;
  } catch (const std::exception &e) {
    spdlog::error("FiberManager initialization failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Shuts down the FiberManager, releasing resources.
 */
void FiberManager::Shutdown() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fiberMutex);
  try {
    for (auto &pair : m_fibers) {
#ifdef _WIN32
      if (pair.second->handle) {
        ::DeleteFiber(pair.second->handle);
        pair.second->handle = nullptr;
      }
#else
      // ucontext_t cleanup is implicit
#endif
    }
    m_fibers.clear();
    m_nextFiberId = 1;
    m_currentFiberId = 0;
    m_lastScheduledId = 0;
#ifdef _WIN32
    if (m_mainFiber) {
      ConvertFiberToThread();
      m_mainFiber = nullptr;
    }
#endif
    m_stats = Stats();
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
    spdlog::info("FiberManager shutdown");
  } catch (const std::exception &e) {
    spdlog::error("FiberManager shutdown failed: {}", e.what());
  }
}

/**
 * @brief Creates a new fiber.
 * @param name Fiber name.
 * @param function Entry point function.
 * @param arg Argument passed to function.
 * @param priority Priority (0=highest, 255=lowest).
 * @param stackSize Stack size in bytes.
 * @return Fiber ID, or 0 on failure.
 */
uint64_t FiberManager::CreateFiber(const std::string &name,
                                   FiberFunction function, uint64_t arg,
                                   uint8_t priority, uint64_t stackSize) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fiberMutex);
  try {
    if (!function) {
      spdlog::error("Invalid fiber function for name: {}", name);
      throw FiberException("Null function pointer");
    }
    if (stackSize < 4096) {
      stackSize = 65536; // Default to 64KB if too small
    }
    uint64_t fiberId = m_nextFiberId++;
    auto fiber = std::make_unique<Fiber>();
    fiber->id = fiberId;
    fiber->name = name;
    fiber->state = FiberState::READY;
    fiber->arg = arg;
    fiber->function = std::move(function);
    fiber->priority = priority;
    fiber->stackSize = stackSize;
    fiber->stack.resize(stackSize);
    InitializeFiberContext(fiber.get());
    m_fibers.emplace(fiberId, std::move(fiber));
    // RACE CONDITION FIX: Use atomic operations for statistics
    m_stats.fiberCreations.fetch_add(1, std::memory_order_relaxed);
    m_stats.cacheHits.fetch_add(
        1, std::memory_order_relaxed); // Assume cache hit for allocation
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().RecordAllocation(
        reinterpret_cast<uint64_t>(fiber.get()), stackSize, 1 /* processId */);
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
    spdlog::info("Created fiber {}: name={}, priority={}", fiberId, name,
                 priority);
    return fiberId;
  } catch (const std::exception &e) {
    spdlog::error("Failed to create fiber for name {}: {}", name, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return 0;
  }
}

/**
 * @brief Deletes a fiber.
 * @param fiberId Fiber ID.
 * @return True on success, false if invalid or current fiber.
 */
bool FiberManager::DeleteFiber(uint64_t fiberId) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fiberMutex);
  try {
    auto it = m_fibers.find(fiberId);
    if (it == m_fibers.end()) {
      spdlog::warn("Invalid fiber ID for deletion: {}", fiberId);
      return false;
    }
    if (fiberId == m_currentFiberId) {
      spdlog::error("Cannot delete current fiber: {}", fiberId);
      return false;
    }
#ifdef _WIN32
    if (it->second->handle) {
      ::DeleteFiber(it->second->handle);
      it->second->handle = nullptr;
    }
#endif
    m_stats.fiberDeletions.fetch_add(1, std::memory_order_relaxed);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().RecordDeallocation(
        reinterpret_cast<uint64_t>(it->second.get()), 1 /* processId */);
    lock.lock();
    spdlog::info("Deleted fiber {}: name={}", fiberId, it->second->name);
    m_fibers.erase(it);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Delete fiber {} failed: {}", fiberId, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return false;
  }
}

/**
 * @brief Switches to a specified fiber.
 * @param fiberId Fiber ID.
 * @return True on success, false if invalid.
 */
bool FiberManager::SwitchToFiber(uint64_t fiberId) {
  auto start = std::chrono::steady_clock::now();
  Fiber *targetFiber = nullptr;
  uint64_t previousFiberId = 0;
  {
    std::unique_lock<std::shared_mutex> lock(m_fiberMutex);
    auto it = m_fibers.find(fiberId);
    if (it == m_fibers.end()) {
      spdlog::warn("Invalid fiber ID for switch: {}", fiberId);
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }
    targetFiber = it->second.get();
    if (!ValidateFiberTransition(targetFiber, FiberState::RUNNING)) {
      spdlog::warn("Cannot switch to fiber {} in state: {}", fiberId,
                   static_cast<int>(targetFiber->state));
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }
    previousFiberId = m_currentFiberId;
    if (m_currentFiberId != 0) {
      auto currentIt = m_fibers.find(m_currentFiberId);
      if (currentIt != m_fibers.end()) {
        currentIt->second->state = FiberState::READY;
        UpdateFiberStats(currentIt->second.get());
      }
    }
    targetFiber->state = FiberState::RUNNING;
    targetFiber->switchCount++;
    targetFiber->cacheHits++;
    m_currentFiberId = fiberId;
    // RACE CONDITION FIX: Use atomic operations for statistics
    m_stats.totalSwitches.fetch_add(1, std::memory_order_relaxed);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    spdlog::trace("Switching from fiber {} to {}", previousFiberId, fiberId);
  }
  try {
#ifdef _WIN32
    if (previousFiberId == 0) {
      ::SwitchToFiber(targetFiber->handle);
    } else {
      Fiber *currentFiber = GetFiberById(previousFiberId);
      if (currentFiber) {
        ::SwitchToFiber(targetFiber->handle);
      } else {
        spdlog::error("Previous fiber {} not found", previousFiberId);
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        return false;
      }
    }
#else
    if (previousFiberId == 0) {
      if (swapcontext(&m_mainContext, &targetFiber->context) != 0) {
        spdlog::error("Failed to switch to fiber {} from main context: {}",
                      fiberId, errno);
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        return false;
      }
    } else {
      Fiber *currentFiber = GetFiberById(previousFiberId);
      if (currentFiber) {
        if (swapcontext(&currentFiber->context, &targetFiber->context) != 0) {
          spdlog::error("Failed to switch from fiber {} to {}: {}",
                        previousFiberId, fiberId, errno);
          m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
          return false;
        }
      } else {
        spdlog::error("Previous fiber {} not found", previousFiberId);
        m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
        return false;
      }
    }
#endif
    std::unique_lock<std::shared_mutex> lock(m_fiberMutex);
    if (m_currentFiberId != 0) {
      auto it = m_fibers.find(m_currentFiberId);
      if (it != m_fibers.end()) {
        UpdateFiberStats(it->second.get());
      }
    }
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    // RACE CONDITION FIX: Use atomic operations for statistics
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Switch to fiber {} failed: {}", fiberId, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return false;
  }
}

/**
 * @brief Suspends the current fiber.
 * @return True on success, false if not in a fiber.
 */
bool FiberManager::SuspendFiber() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fiberMutex);
  try {
    if (m_currentFiberId == 0) {
      spdlog::warn("Cannot suspend from main context");
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }
    auto it = m_fibers.find(m_currentFiberId);
    if (it == m_fibers.end()) {
      spdlog::error("Current fiber {} not found", m_currentFiberId);
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }
    Fiber *fiber = it->second.get();
    if (!ValidateFiberTransition(fiber, FiberState::SUSPENDED)) {
      spdlog::warn("Cannot suspend fiber {} in state: {}", m_currentFiberId,
                   static_cast<int>(fiber->state));
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }
    fiber->state = FiberState::SUSPENDED;
    fiber->cacheHits++;
    m_stats.suspensions.fetch_add(1, std::memory_order_relaxed);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    UpdateFiberStats(fiber);
    ScheduleNextFiber();
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Suspend fiber {} failed: {}", m_currentFiberId, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return false;
  }
}

/**
 * @brief Resumes a suspended fiber.
 * @param fiberId Fiber ID.
 * @return True on success, false if invalid.
 */
bool FiberManager::ResumeFiber(uint64_t fiberId) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fiberMutex);
  try {
    auto it = m_fibers.find(fiberId);
    if (it == m_fibers.end()) {
      spdlog::warn("Invalid fiber ID for resume: {}", fiberId);
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }
    Fiber *fiber = it->second.get();
    if (!ValidateFiberTransition(fiber, FiberState::READY)) {
      spdlog::warn("Cannot resume fiber {} in state: {}", fiberId,
                   static_cast<int>(fiber->state));
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }
    fiber->state = FiberState::READY;
    fiber->cacheHits++;
    m_stats.resumptions.fetch_add(1, std::memory_order_relaxed);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    spdlog::info("Resumed fiber {}: name={}", fiberId, fiber->name);
    ScheduleNextFiber();
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Resume fiber {} failed: {}", fiberId, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return false;
  }
}

/**
 * @brief Yields the current fiber.
 */
void FiberManager::YieldFiber() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fiberMutex);
  try {
    if (m_currentFiberId == 0) {
      spdlog::trace("Yield called from main context, ignoring");
      return;
    }
    auto it = m_fibers.find(m_currentFiberId);
    if (it != m_fibers.end()) {
      it->second->state = FiberState::READY;
      it->second->cacheHits++;
      UpdateFiberStats(it->second.get());
      m_stats.yields.fetch_add(1, std::memory_order_relaxed);
      m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    }
    ScheduleNextFiber();
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("Yield fiber {} failed: {}", m_currentFiberId, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Waits for a fiber to complete.
 * @param fiberId Fiber ID to wait for.
 * @param timeoutUs Timeout in microseconds.
 * @return True if waited successfully, false if invalid or timed out.
 */
bool FiberManager::WaitForFiber(uint64_t fiberId, uint64_t timeoutUs) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fiberMutex);
  try {
    auto it = m_fibers.find(fiberId);
    if (it == m_fibers.end()) {
      spdlog::warn("Invalid fiber ID for wait: {}", fiberId);
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }
    if (m_currentFiberId == 0) {
      spdlog::error("Cannot wait from main context");
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }
    if (m_currentFiberId == fiberId) {
      spdlog::error("Fiber {} cannot wait for itself", fiberId);
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }
    auto currentIt = m_fibers.find(m_currentFiberId);
    if (currentIt != m_fibers.end()) {
      currentIt->second->state = FiberState::WAITING;
      currentIt->second->waitingOn = fiberId;
      currentIt->second->cacheHits++;
      UpdateFiberStats(currentIt->second.get());
      m_stats.waits.fetch_add(1, std::memory_order_relaxed);
      m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    }
    ScheduleNextFiber();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Wait for fiber {} failed: {}", fiberId, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return false;
  }
}

/**
 * @brief Gets the current fiber ID.
 * @return Current fiber ID.
 */
uint64_t FiberManager::GetCurrentFiberId() const {
  std::shared_lock<std::shared_mutex> lock(m_fiberMutex);
  return m_currentFiberId;
}

/**
 * @brief Gets fiber information.
 * @param fiberId Fiber ID.
 * @param name Output name.
 * @param state Output state.
 * @param priority Output priority.
 * @param executionTimeUs Output execution time.
 * @param switchCount Output switch count.
 * @return True if found, false otherwise.
 */
bool FiberManager::GetFiberInfo(uint64_t fiberId, std::string &name,
                                FiberState &state, uint8_t &priority,
                                uint64_t &executionTimeUs,
                                uint32_t &switchCount) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_fiberMutex);
  try {
    const Fiber *fiber = GetFiberById(fiberId);
    if (!fiber) {
      spdlog::warn("Invalid fiber ID for info: {}", fiberId);
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }
    name = fiber->name;
    state = fiber->state;
    priority = fiber->priority;
    executionTimeUs = fiber->executionTimeUs;
    switchCount = fiber->switchCount;
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Get fiber info {} failed: {}", fiberId, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return false;
  }
}

/**
 * @brief Sets a fiber’s priority.
 * @param fiberId Fiber ID.
 * @param priority New priority.
 * @return True on success, false if invalid.
 */
bool FiberManager::SetFiberPriority(uint64_t fiberId, uint8_t priority) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fiberMutex);
  try {
    Fiber *fiber = GetFiberById(fiberId);
    if (!fiber) {
      spdlog::warn("Invalid fiber ID for priority set: {}", fiberId);
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }
    fiber->priority = priority;
    fiber->cacheHits++;
    m_stats.priorityChanges.fetch_add(1, std::memory_order_relaxed);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    spdlog::trace("Set priority of fiber {} to {}", fiberId, priority);
    ScheduleNextFiber();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Set fiber priority {} failed: {}", fiberId, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return false;
  }
}

/**
 * @brief Gets the number of active fibers.
 * @return Number of fibers.
 */
uint32_t FiberManager::GetFiberCount() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_fiberMutex);
  uint32_t count = static_cast<uint32_t>(m_fibers.size());
  m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs.fetch_add(
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count(),
      std::memory_order_relaxed);
  return count;
}

/**
 * @brief Sets the scheduling policy.
 * @param policy Scheduling policy.
 */
void FiberManager::SetSchedulingPolicy(SchedulingPolicy policy) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fiberMutex);
  try {
    m_policy = policy;
    m_stats.policyChanges.fetch_add(1, std::memory_order_relaxed);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    spdlog::info("Set scheduling policy to {}",
                 policy == SchedulingPolicy::PRIORITY      ? "PRIORITY"
                 : policy == SchedulingPolicy::ROUND_ROBIN ? "ROUND_ROBIN"
                                                           : "ADAPTIVE");
    ScheduleNextFiber();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("Set scheduling policy failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Schedules the next fiber to run.
 */
void FiberManager::ScheduleNextFiber() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fiberMutex);
  try {
    ScheduleNextFiberInternal();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("Schedule next fiber failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Internal scheduling logic.
 */
void FiberManager::ScheduleNextFiberInternal() {
  Fiber *nextFiber = nullptr;
  if (m_policy == SchedulingPolicy::PRIORITY) {
    uint8_t minPriority = 255;
    for (auto &pair : m_fibers) {
      Fiber *fiber = pair.second.get();
      if (fiber->state == FiberState::READY && fiber->priority <= minPriority) {
        minPriority = fiber->priority;
        nextFiber = fiber;
      }
    }
  } else if (m_policy == SchedulingPolicy::ROUND_ROBIN) {
    bool foundLast = false;
    for (auto &pair : m_fibers) {
      if (foundLast && pair.second->state == FiberState::READY) {
        nextFiber = pair.second.get();
        break;
      }
      if (pair.second->id == m_lastScheduledId) {
        foundLast = true;
      }
    }
    if (!nextFiber) {
      for (auto &pair : m_fibers) {
        if (pair.second->state == FiberState::READY) {
          nextFiber = pair.second.get();
          break;
        }
      }
    }
  } else { // ADAPTIVE - Enhanced PS4-specific adaptive scheduling
    double bestScore = std::numeric_limits<double>::max();
    uint64_t currentTime =
        std::chrono::duration_cast<std::chrono::microseconds>(
            std::chrono::steady_clock::now().time_since_epoch())
            .count();

    for (auto &pair : m_fibers) {
      Fiber *fiber = pair.second.get();
      if (fiber->state == FiberState::READY) {
        // Enhanced scoring algorithm considering multiple factors
        double priorityScore = static_cast<double>(fiber->priority) * 0.3;
        double executionScore = (fiber->executionTimeUs / 1000000.0) * 0.2;
        double waitScore =
            ((currentTime - fiber->lastScheduledTime) / 1000000.0) * 0.2;
        double loadScore = fiber->loadFactor * 0.15;
        double cacheScore =
            (fiber->performance.cacheHitRatio > 0.8f) ? -0.1 : 0.1;
        double affinityScore = 0.05; // Bonus for core affinity

        // Check if fiber prefers current core for cache locality
        if (fiber->affinity.stickyScheduling && fiber->affinity.lastCore < 8) {
          affinityScore = -0.05; // Negative score (better) for cache locality
        }

        double totalScore = priorityScore + executionScore + waitScore +
                            loadScore + cacheScore + affinityScore;

        if (totalScore < bestScore) {
          bestScore = totalScore;
          nextFiber = fiber;
        }
      }
    }
  }
  if (nextFiber) {
    m_lastScheduledId = nextFiber->id;
    SwitchToFiber(nextFiber->id);
  } else {
    if (m_currentFiberId != 0) {
      auto it = m_fibers.find(m_currentFiberId);
      if (it != m_fibers.end()) {
        it->second->state = FiberState::READY;
        UpdateFiberStats(it->second.get());
      }
      m_currentFiberId = 0;
#ifdef _WIN32
      ::SwitchToFiber(m_mainFiber);
#else
      if (it != m_fibers.end() &&
          swapcontext(&it->second->context, &m_mainContext) != 0) {
        spdlog::error("Failed to switch to main context from fiber {}: {}",
                      m_currentFiberId, errno);
      }
#endif
    }
    spdlog::trace("No ready fibers, returning to main context");
  }
  m_stats.cacheHits++;
  ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
}

/**
 * @brief Retrieves a fiber by ID.
 * @param fiberId Fiber ID.
 * @return Pointer to the fiber, or nullptr if not found.
 */
Fiber *FiberManager::GetFiberById(uint64_t fiberId) {
  std::shared_lock<std::shared_mutex> lock(m_fiberMutex);
  auto it = m_fibers.find(fiberId);
  return it != m_fibers.end() ? it->second.get() : nullptr;
}

/**
 * @brief Retrieves a const fiber by ID.
 * @param fiberId Fiber ID.
 * @return Const pointer to the fiber, or nullptr if not found.
 */
const Fiber *FiberManager::GetFiberById(uint64_t fiberId) const {
  std::shared_lock<std::shared_mutex> lock(m_fiberMutex);
  auto it = m_fibers.find(fiberId);
  return it != m_fibers.end() ? it->second.get() : nullptr;
}

/**
 * @brief Initializes a fiber’s context.
 * @param fiber Pointer to the fiber.
 */
void FiberManager::InitializeFiberContext(Fiber *fiber) {
  auto start = std::chrono::steady_clock::now();
  try {
#ifdef _WIN32
    fiber->handle = ::CreateFiberEx(
        static_cast<SIZE_T>(fiber->stackSize),
        static_cast<SIZE_T>(fiber->stackSize), 0,
        [](LPVOID lpParameter) {
          FiberTrampoline(
              static_cast<uint64_t>(reinterpret_cast<uintptr_t>(lpParameter)));
        },
        reinterpret_cast<LPVOID>(static_cast<uintptr_t>(fiber->id)));
    if (!fiber->handle) {
      spdlog::error("Failed to create fiber {}: {}", fiber->id, GetLastError());
      throw FiberException("Failed to create fiber");
    }
#else
    if (getcontext(&fiber->context) != 0) {
      spdlog::error("Failed to get context for fiber {}: {}", fiber->id, errno);
      throw FiberException("Failed to get context");
    }
    fiber->context.uc_stack.ss_sp = fiber->stack.data();
    fiber->context.uc_stack.ss_size = fiber->stackSize;
    fiber->context.uc_link = &m_mainContext;
    makecontext(&fiber->context, reinterpret_cast<void (*)()>(FiberTrampoline),
                1, static_cast<uint64_t>(fiber->id));
#endif
    fiber->cacheHits++;
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("Initialize fiber context {} failed: {}", fiber->id,
                  e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    throw;
  }
}

/**
 * @brief Trampoline function for fiber execution.
 * @param fiberId Fiber ID.
 */
void FiberManager::FiberTrampoline(uint64_t fiberId) {
  auto start = std::chrono::steady_clock::now();
  if (!s_currentManager) {
    spdlog::error("Fiber trampoline called with no manager");
    return;
  }
  Fiber *fiber = s_currentManager->GetFiberById(fiberId);
  if (!fiber) {
    spdlog::error("Fiber trampoline called with invalid fiber ID: {}", fiberId);
    return;
  }
  try {
    fiber->function(fiber->arg);
    fiber->cacheHits++;
    // RACE CONDITION FIX: Use atomic operations for statistics
    s_currentManager->m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("Exception in fiber {}: {}", fiberId, e.what());
    s_currentManager->m_stats.cacheMisses.fetch_add(1,
                                                    std::memory_order_relaxed);
  }
  FiberManager *manager = s_currentManager;
  if (manager) {
    std::unique_lock<std::shared_mutex> lock(manager->m_fiberMutex);
    fiber->state = FiberState::FINISHED;
    fiber->cacheHits++;
    for (auto &pair : manager->m_fibers) {
      if (pair.second->state == FiberState::WAITING &&
          pair.second->waitingOn == fiberId) {
        pair.second->state = FiberState::READY;
        pair.second->waitingOn = 0;
        pair.second->cacheHits++;
      }
    }
    manager->UpdateFiberStats(fiber);
    manager->ScheduleNextFiber();
    auto end = std::chrono::steady_clock::now();
    // RACE CONDITION FIX: Use atomic operations for statistics
    manager->m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  }
}

/**
 * @brief Validates a fiber state transition.
 * @param fiber Pointer to the fiber.
 * @param targetState Desired state.
 * @return True if valid, false otherwise.
 */
bool FiberManager::ValidateFiberTransition(Fiber *fiber,
                                           FiberState targetState) {
  try {
    switch (targetState) {
    case FiberState::RUNNING:
      return fiber->state == FiberState::READY ||
             fiber->state == FiberState::SUSPENDED;
    case FiberState::SUSPENDED:
      return fiber->state == FiberState::RUNNING;
    case FiberState::READY:
      return fiber->state == FiberState::SUSPENDED ||
             fiber->state == FiberState::WAITING;
    case FiberState::WAITING:
      return fiber->state == FiberState::RUNNING;
    case FiberState::FINISHED:
      return fiber->state == FiberState::RUNNING;
    default:
      return false;
    }
  } catch (const std::exception &e) {
    spdlog::error("Validate fiber transition failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return false;
  }
}

/**
 * @brief Updates a fiber’s statistics.
 * @param fiber Pointer to the fiber.
 */
void FiberManager::UpdateFiberStats(Fiber *fiber) {
  try {
    static thread_local auto lastSwitch = std::chrono::steady_clock::now();
    auto now = std::chrono::steady_clock::now();
    auto duration =
        std::chrono::duration_cast<std::chrono::microseconds>(now - lastSwitch)
            .count();
    if (fiber->state == FiberState::RUNNING) {
      fiber->executionTimeUs += duration;
      // RACE CONDITION FIX: Use atomic operations for statistics
      m_stats.totalExecutionTimeUs.fetch_add(duration,
                                             std::memory_order_relaxed);
      fiber->cacheHits++;
    }
    lastSwitch = now;
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
  } catch (const std::exception &e) {
    spdlog::error("Update fiber stats for {} failed: {}", fiber->id, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Saves the FiberManager state.
 * @param out Output stream.
 */
void FiberManager::SaveState(std::ostream &out) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_fiberMutex);
  try {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    uint64_t fiberCount = m_fibers.size();
    out.write(reinterpret_cast<const char *>(&fiberCount), sizeof(fiberCount));
    for (const auto &pair : m_fibers) {
      const Fiber *fiber = pair.second.get();
      uint32_t nameLen = static_cast<uint32_t>(fiber->name.size());
      out.write(reinterpret_cast<const char *>(&fiber->id), sizeof(fiber->id));
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
      out.write(fiber->name.data(), nameLen);
      out.write(reinterpret_cast<const char *>(&fiber->state),
                sizeof(fiber->state));
      out.write(reinterpret_cast<const char *>(&fiber->arg),
                sizeof(fiber->arg));
      out.write(reinterpret_cast<const char *>(&fiber->priority),
                sizeof(fiber->priority));
      out.write(reinterpret_cast<const char *>(&fiber->stackSize),
                sizeof(fiber->stackSize));
      out.write(reinterpret_cast<const char *>(&fiber->executionTimeUs),
                sizeof(fiber->executionTimeUs));
      out.write(reinterpret_cast<const char *>(&fiber->switchCount),
                sizeof(fiber->switchCount));
      out.write(reinterpret_cast<const char *>(&fiber->waitingOn),
                sizeof(fiber->waitingOn));
      out.write(reinterpret_cast<const char *>(&fiber->cacheHits),
                sizeof(fiber->cacheHits));
      out.write(reinterpret_cast<const char *>(&fiber->cacheMisses),
                sizeof(fiber->cacheMisses));
    }
    out.write(reinterpret_cast<const char *>(&m_currentFiberId),
              sizeof(m_currentFiberId));
    out.write(reinterpret_cast<const char *>(&m_nextFiberId),
              sizeof(m_nextFiberId));
    out.write(reinterpret_cast<const char *>(&m_policy), sizeof(m_policy));
    out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));
    out.write(reinterpret_cast<const char *>(&m_lastScheduledId),
              sizeof(m_lastScheduledId));
    if (!out.good()) {
      throw std::runtime_error("Failed to write FiberManager state");
    }
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
    spdlog::info("FiberManager state saved: {} fibers", fiberCount);
  } catch (const std::exception &e) {
    spdlog::error("FiberManager SaveState failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Loads the FiberManager state.
 * @param in Input stream.
 */
void FiberManager::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fiberMutex);
  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      spdlog::error("Unsupported FiberManager state version: {}", version);
      throw std::runtime_error("Invalid FiberManager state version");
    }
    Shutdown();
    uint64_t fiberCount;
    in.read(reinterpret_cast<char *>(&fiberCount), sizeof(fiberCount));
    for (uint64_t i = 0; i < fiberCount && in.good(); ++i) {
      auto fiber = std::make_unique<Fiber>();
      uint32_t nameLen;
      in.read(reinterpret_cast<char *>(&fiber->id), sizeof(fiber->id));
      in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
      fiber->name.resize(nameLen);
      in.read(fiber->name.data(), nameLen);
      in.read(reinterpret_cast<char *>(&fiber->state), sizeof(fiber->state));
      in.read(reinterpret_cast<char *>(&fiber->arg), sizeof(fiber->arg));
      in.read(reinterpret_cast<char *>(&fiber->priority),
              sizeof(fiber->priority));
      in.read(reinterpret_cast<char *>(&fiber->stackSize),
              sizeof(fiber->stackSize));
      in.read(reinterpret_cast<char *>(&fiber->executionTimeUs),
              sizeof(fiber->executionTimeUs));
      in.read(reinterpret_cast<char *>(&fiber->switchCount),
              sizeof(fiber->switchCount));
      in.read(reinterpret_cast<char *>(&fiber->waitingOn),
              sizeof(fiber->waitingOn));
      in.read(reinterpret_cast<char *>(&fiber->cacheHits),
              sizeof(fiber->cacheHits));
      in.read(reinterpret_cast<char *>(&fiber->cacheMisses),
              sizeof(fiber->cacheMisses));
      fiber->stack.resize(fiber->stackSize);
      fiber->function = nullptr; // Function cannot be restored
      InitializeFiberContext(fiber.get());
      m_fibers.emplace(fiber->id, std::move(fiber));
      m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    }
    in.read(reinterpret_cast<char *>(&m_currentFiberId),
            sizeof(m_currentFiberId));
    in.read(reinterpret_cast<char *>(&m_nextFiberId), sizeof(m_nextFiberId));
    in.read(reinterpret_cast<char *>(&m_policy), sizeof(m_policy));
    in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));
    in.read(reinterpret_cast<char *>(&m_lastScheduledId),
            sizeof(m_lastScheduledId));
    if (!in.good()) {
      throw std::runtime_error("Failed to read FiberManager state");
    }
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
    spdlog::info("FiberManager state loaded: {} fibers", fiberCount);
  } catch (const std::exception &e) {
    spdlog::error("FiberManager LoadState failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    Shutdown();
  }
}

/**
 * @brief Retrieves scheduling statistics.
 * @return Current statistics.
 */
FiberManager::Stats FiberManager::GetStats() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_fiberMutex);

  // RACE CONDITION FIX: Create a copy with atomic loads
  Stats result;
  result.totalSwitches.store(m_stats.totalSwitches.load());
  result.totalExecutionTimeUs.store(m_stats.totalExecutionTimeUs.load());
  result.fiberCreations.store(m_stats.fiberCreations.load());
  result.fiberDeletions.store(m_stats.fiberDeletions.load());
  result.suspensions.store(m_stats.suspensions.load());
  result.resumptions.store(m_stats.resumptions.load());
  result.yields.store(m_stats.yields.load());
  result.waits.store(m_stats.waits.load());
  result.priorityChanges.store(m_stats.priorityChanges.load());
  result.policyChanges.store(m_stats.policyChanges.load());
  result.preemptions.store(m_stats.preemptions.load());
  result.loadBalanceOperations.store(m_stats.loadBalanceOperations.load());
  result.affinityChanges.store(m_stats.affinityChanges.load());
  result.quantumAdjustments.store(m_stats.quantumAdjustments.load());
  result.performanceUpdates.store(m_stats.performanceUpdates.load());
  result.averageLoadFactor.store(m_stats.averageLoadFactor.load());
  result.cacheLocalityRatio.store(m_stats.cacheLocalityRatio.load());
  result.contextSwitchOverheadUs.store(m_stats.contextSwitchOverheadUs.load());
  result.totalLatencyUs.store(m_stats.totalLatencyUs.load());
  result.cacheHits.store(m_stats.cacheHits.load());
  result.cacheMisses.store(m_stats.cacheMisses.load());

  m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
  auto end = std::chrono::steady_clock::now();
  auto latency =
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
  m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
  return result;
}

/**
 * @brief Sets CPU affinity for a fiber.
 */
bool FiberManager::SetFiberAffinity(uint64_t fiberId, uint8_t affinityMask) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fiberMutex);
  try {
    auto it = m_fibers.find(fiberId);
    if (it == m_fibers.end()) {
      spdlog::warn("SetFiberAffinity: Invalid fiber ID {}", fiberId);
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }

    Fiber *fiber = it->second.get();
    fiber->affinity.affinityMask = affinityMask;

    // Set preferred core to first available core in mask
    for (uint8_t core = 0; core < 8; ++core) {
      if (affinityMask & (1 << core)) {
        fiber->affinity.preferredCore = core;
        break;
      }
    }

    m_stats.affinityChanges.fetch_add(1, std::memory_order_relaxed);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);

    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);

    spdlog::debug("Set fiber {} affinity to mask 0x{:02x}, preferred core {}",
                  fiberId, affinityMask, fiber->affinity.preferredCore);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("SetFiberAffinity failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return false;
  }
}

/**
 * @brief Sets time quantum for a fiber.
 */
bool FiberManager::SetFiberQuantum(uint64_t fiberId, uint64_t quantumUs) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fiberMutex);
  try {
    auto it = m_fibers.find(fiberId);
    if (it == m_fibers.end()) {
      spdlog::warn("SetFiberQuantum: Invalid fiber ID {}", fiberId);
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }

    Fiber *fiber = it->second.get();
    fiber->quantumUs = std::clamp(quantumUs, m_minQuantumUs, m_maxQuantumUs);

    m_stats.quantumAdjustments.fetch_add(1, std::memory_order_relaxed);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);

    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);

    spdlog::debug("Set fiber {} quantum to {}us", fiberId, fiber->quantumUs);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("SetFiberQuantum failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return false;
  }
}

/**
 * @brief Enables or disables sticky scheduling for cache locality.
 */
bool FiberManager::EnableStickyScheduling(uint64_t fiberId, bool enabled) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fiberMutex);
  try {
    auto it = m_fibers.find(fiberId);
    if (it == m_fibers.end()) {
      spdlog::warn("EnableStickyScheduling: Invalid fiber ID {}", fiberId);
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }

    Fiber *fiber = it->second.get();
    fiber->affinity.stickyScheduling = enabled;

    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);

    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);

    spdlog::debug("Set fiber {} sticky scheduling to {}", fiberId, enabled);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("EnableStickyScheduling failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return false;
  }
}

/**
 * @brief Updates performance metrics for a fiber.
 */
void FiberManager::UpdatePerformanceMetrics(uint64_t fiberId) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_fiberMutex);
  try {
    auto it = m_fibers.find(fiberId);
    if (it == m_fibers.end()) {
      return;
    }

    Fiber *fiber = it->second.get();
    uint64_t currentTime =
        std::chrono::duration_cast<std::chrono::microseconds>(
            std::chrono::steady_clock::now().time_since_epoch())
            .count();

    // Simulate realistic performance metrics (in real implementation, these
    // would come from hardware counters)
    fiber->performance.cpuCycles += 1000000; // Simulate 1M cycles
    fiber->performance.memoryAccesses += 10000;
    fiber->performance.cacheL1Hits += 8500;
    fiber->performance.cacheL1Misses += 1500;
    fiber->performance.cacheL2Hits += 1200;
    fiber->performance.cacheL2Misses += 300;
    fiber->performance.branchPredictions += 5000;
    fiber->performance.branchMispredictions += 500;
    fiber->performance.tlbHits += 9800;
    fiber->performance.tlbMisses += 200;

    // Calculate derived metrics
    if (fiber->performance.cpuCycles > 0) {
      fiber->performance.ipc =
          static_cast<float>(fiber->performance.memoryAccesses) /
          static_cast<float>(fiber->performance.cpuCycles);
    }

    uint64_t totalCacheAccesses =
        fiber->performance.cacheL1Hits + fiber->performance.cacheL1Misses;
    if (totalCacheAccesses > 0) {
      fiber->performance.cacheHitRatio =
          static_cast<float>(fiber->performance.cacheL1Hits) /
          static_cast<float>(totalCacheAccesses);
    }

    fiber->performance.lastUpdateTime = currentTime;

    // Update load factor
    fiber->loadFactor = CalculateFiberLoadFactor(fiber);

    m_stats.performanceUpdates.fetch_add(1, std::memory_order_relaxed);

    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs.fetch_add(
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count(),
        std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("UpdatePerformanceMetrics failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Calculates load factor for a fiber.
 */
float FiberManager::CalculateFiberLoadFactor(const Fiber *fiber) const {
  try {
    if (!fiber || fiber->executionTimeUs == 0) {
      return 0.0f;
    }

    // Calculate load factor based on execution time, cache performance, and
    // scheduling behavior
    float executionTimeSeconds =
        static_cast<float>(fiber->executionTimeUs) / 1000000.0f;
    float executionFactor =
        std::min(1.0f, executionTimeSeconds); // Normalize to seconds
    float cacheFactor = fiber->performance.cacheHitRatio;
    float yieldFactor =
        (fiber->yieldCount > 0) ? 0.8f : 1.0f; // Cooperative fibers get bonus
    float preemptionPenalty = static_cast<float>(fiber->preemptionCount) * 0.1f;

    float loadFactor =
        (executionFactor * 0.5f + cacheFactor * 0.3f + yieldFactor * 0.2f) -
        preemptionPenalty;
    return std::clamp(loadFactor, 0.0f, 1.0f);
  } catch (const std::exception &e) {
    spdlog::error("CalculateFiberLoadFactor failed: {}", e.what());
    return 0.5f; // Default moderate load
  }
}

/**
 * @brief Selects optimal core for a fiber.
 */
uint8_t FiberManager::SelectOptimalCore(const Fiber *fiber) const {
  try {
    if (!fiber) {
      return 0;
    }

    // Check affinity constraints
    if (fiber->affinity.preferredCore < 8 &&
        (fiber->affinity.affinityMask & (1 << fiber->affinity.preferredCore))) {
      return fiber->affinity.preferredCore;
    }

    // Find least loaded core that matches affinity
    uint8_t bestCore = 0;
    float lowestLoad = std::numeric_limits<float>::max();

    for (uint8_t core = 0; core < 8; ++core) {
      if (fiber->affinity.affinityMask & (1 << core)) {
        float coreLoad = m_coreLoads[core].loadFactor;

        // Bonus for cache locality if sticky scheduling is enabled
        if (fiber->affinity.stickyScheduling &&
            fiber->affinity.lastCore == core) {
          coreLoad -= 0.1f; // Prefer same core for cache locality
        }

        if (coreLoad < lowestLoad) {
          lowestLoad = coreLoad;
          bestCore = core;
        }
      }
    }

    return bestCore;
  } catch (const std::exception &e) {
    spdlog::error("SelectOptimalCore failed: {}", e.what());
    return 0; // Default to core 0
  }
}

/**
 * @brief Optimizes scheduling based on current system state.
 */
void FiberManager::OptimizeScheduling() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_fiberMutex);
  try {
    uint64_t currentTime =
        std::chrono::duration_cast<std::chrono::microseconds>(
            std::chrono::steady_clock::now().time_since_epoch())
            .count();

    if (currentTime - m_lastOptimizationTime < m_optimizationIntervalUs) {
      return; // Too soon for optimization
    }

    // Update core load information
    for (auto &coreLoad : m_coreLoads) {
      coreLoad.activeFibers = 0;
      coreLoad.totalExecutionTime = 0;
    }

    // Calculate current system load
    float totalSystemLoad = 0.0f;
    uint32_t activeFibers = 0;

    for (const auto &pair : m_fibers) {
      const Fiber *fiber = pair.second.get();
      if (fiber->state != FiberState::FINISHED) {
        activeFibers++;
        totalSystemLoad += fiber->loadFactor;

        if (fiber->affinity.lastCore < 8) {
          m_coreLoads[fiber->affinity.lastCore].activeFibers++;
          m_coreLoads[fiber->affinity.lastCore].totalExecutionTime +=
              fiber->executionTimeUs;
        }
      }
    }

    // Update core load factors
    for (auto &coreLoad : m_coreLoads) {
      if (coreLoad.activeFibers > 0) {
        coreLoad.loadFactor =
            static_cast<float>(coreLoad.totalExecutionTime) /
            (static_cast<float>(coreLoad.activeFibers) * 1000000.0f);
      } else {
        coreLoad.loadFactor = 0.0f;
      }
    }

    // Update system-wide statistics
    if (activeFibers > 0) {
      m_stats.averageLoadFactor =
          totalSystemLoad / static_cast<float>(activeFibers);
    }

    // Adaptive quantum adjustment
    if (m_adaptiveQuantum) {
      for (auto &pair : m_fibers) {
        Fiber *fiber = pair.second.get();
        if (fiber->state != FiberState::FINISHED) {
          // Adjust quantum based on fiber behavior
          if (fiber->yieldCount > fiber->preemptionCount * 2) {
            // Cooperative fiber - can have longer quantum
            uint64_t newQuantum = fiber->quantumUs + 1000;
            fiber->quantumUs = std::min(newQuantum, m_maxQuantumUs);
          } else if (fiber->preemptionCount > fiber->yieldCount * 2) {
            // CPU-intensive fiber - reduce quantum
            uint64_t newQuantum = fiber->quantumUs - 1000;
            fiber->quantumUs = std::max(newQuantum, m_minQuantumUs);
          }
        }
      }
    }

    // Trigger load balancing if needed
    bool needsBalancing = false;
    for (const auto &coreLoad : m_coreLoads) {
      if (coreLoad.loadFactor > m_loadBalanceThreshold) {
        needsBalancing = true;
        break;
      }
    }

    if (needsBalancing) {
      BalanceLoad();
    }

    m_lastOptimizationTime = currentTime;

    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    spdlog::debug("Scheduling optimization completed, average load: {:.2f}",
                  m_stats.averageLoadFactor.load());
  } catch (const std::exception &e) {
    spdlog::error("OptimizeScheduling failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Balances load across CPU cores.
 */
void FiberManager::BalanceLoad() {
  try {
    // Find most and least loaded cores
    uint8_t mostLoadedCore = 0;
    uint8_t leastLoadedCore = 0;
    float maxLoad = 0.0f;
    float minLoad = std::numeric_limits<float>::max();

    for (uint8_t core = 0; core < 8; ++core) {
      float load = m_coreLoads[core].loadFactor;
      if (load > maxLoad) {
        maxLoad = load;
        mostLoadedCore = core;
      }
      if (load < minLoad) {
        minLoad = load;
        leastLoadedCore = core;
      }
    }

    // Only balance if there's significant load difference
    if (maxLoad - minLoad > 0.3f) {
      // Find a fiber to migrate from most loaded to least loaded core
      for (auto &pair : m_fibers) {
        Fiber *fiber = pair.second.get();
        if (fiber->state == FiberState::READY &&
            fiber->affinity.lastCore == mostLoadedCore &&
            (fiber->affinity.affinityMask & (1 << leastLoadedCore))) {

          // Migrate fiber to less loaded core
          fiber->affinity.preferredCore = leastLoadedCore;
          m_stats.loadBalanceOperations++;

          spdlog::debug("Load balanced fiber {} from core {} to core {}",
                        fiber->id, mostLoadedCore, leastLoadedCore);
          break; // Only migrate one fiber per balance operation
        }
      }
    }
  } catch (const std::exception &e) {
    spdlog::error("BalanceLoad failed: {}", e.what());
  }
}

} // namespace ps4