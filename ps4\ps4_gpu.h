// Copyright 2025 <Copyright Owner>

#pragma once

#include "../memory/ps4_mmu.h"
#include "../video_core/gnm_shader_translator.h"
#include "../video_core/gnm_state.h"
#include "../video_core/tile_manager.h"
#include <SDL.h>
#include <functional>
#include <memory>
#include <shared_mutex>
#include <string>
#include <unordered_map>
#include <vector>
#include <vulkan/vulkan.h>

#ifdef _WIN32
#define VK_USE_PLATFORM_WIN32_KHR
#endif
#ifdef __linux__
#define VK_USE_PLATFORM_XCB_KHR
#endif

namespace ps4 {

/**
 * @brief Exception for GPU-related errors.
 */
struct GPUException : std::runtime_error {
  explicit GPUException(const std::string &msg) : std::runtime_error(msg) {}
};

/**
 * @brief Structure for Vulkan context.
 */
struct VulkanContext {
  VkInstance instance = VK_NULL_HANDLE;             ///< Vulkan instance
  VkPhysicalDevice physicalDevice = VK_NULL_HANDLE; ///< Physical device
  VkDevice device = VK_NULL_HANDLE;                 ///< Logical device
  VkQueue graphicsQueue = VK_NULL_HANDLE;           ///< Graphics queue
  VkQueue computeQueue = VK_NULL_HANDLE;            ///< Compute queue
  VkQueue presentQueue = VK_NULL_HANDLE;            ///< Present queue
  VkCommandPool commandPool = VK_NULL_HANDLE;       ///< Command pool
  VkSwapchainKHR swapchain = VK_NULL_HANDLE;        ///< Swapchain
  std::vector<VkImage> swapchainImages;             ///< Swapchain images
  std::vector<VkImageView> swapchainImageViews;     ///< Swapchain image views
  VkExtent2D swapchainExtent = {0, 0};              ///< Swapchain extent
  VkFormat swapchainImageFormat = VK_FORMAT_UNDEFINED; ///< Swapchain format
  VkSurfaceKHR surface = VK_NULL_HANDLE;               ///< Surface
  VkDescriptorPool descriptorPool = VK_NULL_HANDLE;    ///< Descriptor pool
  VkSemaphore imageAvailableSemaphore =
      VK_NULL_HANDLE; ///< Image available semaphore
  VkSemaphore renderFinishedSemaphore =
      VK_NULL_HANDLE;                        ///< Render finished semaphore
  VkFence inFlightFence = VK_NULL_HANDLE;    ///< In-flight fence
  uint32_t graphicsQueueFamily = UINT32_MAX; ///< Graphics queue family
  uint32_t presentQueueFamily = UINT32_MAX;  ///< Present queue family
  uint32_t computeQueueFamily = UINT32_MAX;  ///< Compute queue family
  std::shared_mutex contextMutex;            ///< Mutex for thread safety
  std::vector<VkFence> in_flight_fences;     ///< In-flight fences
  uint32_t currentFrame = 0;                 ///< Current frame index
  uint64_t cacheHits = 0;   ///< Cache hits for Vulkan operations
  uint64_t cacheMisses = 0; ///< Cache misses for Vulkan operations
  std::vector<VkSemaphore>
      renderFinishedSemaphores;             ///< Render finished semaphores
  uint64_t frameCount = 0;                  ///< Frame count
  uint64_t renderLatencyUs = 0;             ///< Render latency in microseconds
  std::vector<VkFramebuffer> framebuffers;  ///< Framebuffers
  VkRenderPass renderPass = VK_NULL_HANDLE; ///< Render pass
};

/**
 * @brief Structure for shader module.
 */
struct ShaderModule {
  VkShaderModule module = VK_NULL_HANDLE; ///< Shader module
  GCNShaderType type = {};                ///< Shader type
  std::vector<uint32_t> spirvCode;        ///< SPIR-V code
};

/**
 * @brief Structure for render target.
 */
struct RenderTarget {
  uint64_t surfaceId = 0;                 ///< Surface ID
  VkImage image = VK_NULL_HANDLE;         ///< Image
  VkDeviceMemory memory = VK_NULL_HANDLE; ///< Memory
  VkImageView view = VK_NULL_HANDLE;      ///< Image view
  VkFormat format = VK_FORMAT_UNDEFINED;  ///< Format
  uint32_t width = 0;                     ///< Width
  uint32_t height = 0;                    ///< Height
  bool isDepthStencil = false;            ///< Depth-stencil flag
};

/**
 * @brief Structure for memory mapping.
 */
struct MemoryMapping {
  uint64_t gpuAddress = 0;                ///< GPU address
  uint64_t cpuAddress = 0;                ///< CPU address
  size_t size = 0;                        ///< Size
  VkBuffer buffer = VK_NULL_HANDLE;       ///< Buffer
  VkDeviceMemory memory = VK_NULL_HANDLE; ///< Memory
  void *mappedData = nullptr;             ///< Mapped data
};

/**
 * @brief Structure for graphics pipeline key.
 */
struct GraphicsPipelineKey {
  uint64_t vsShaderId = 0;                  ///< Vertex shader ID
  uint64_t psShaderId = 0;                  ///< Pixel shader ID
  uint64_t gsShaderId = 0;                  ///< Geometry shader ID
  uint64_t hsShaderId = 0;                  ///< Hull shader ID
  uint64_t dsShaderId = 0;                  ///< Domain shader ID
  uint32_t rasterizerStateHash = 0;         ///< Rasterizer state hash
  uint32_t blendStateHash = 0;              ///< Blend state hash
  uint32_t depthStencilStateHash = 0;       ///< Depth-stencil state hash
  uint32_t vertexInputHash = 0;             ///< Vertex input hash
  VkRenderPass renderPass = VK_NULL_HANDLE; ///< Render pass
  uint32_t subpassIndex = 0;                ///< Subpass index
  VkPrimitiveTopology topology =
      VK_PRIMITIVE_TOPOLOGY_TRIANGLE_LIST; ///< Topology

  bool operator==(const GraphicsPipelineKey &other) const {
    return vsShaderId == other.vsShaderId && psShaderId == other.psShaderId &&
           gsShaderId == other.gsShaderId && hsShaderId == other.hsShaderId &&
           dsShaderId == other.dsShaderId &&
           rasterizerStateHash == other.rasterizerStateHash &&
           blendStateHash == other.blendStateHash &&
           depthStencilStateHash == other.depthStencilStateHash &&
           vertexInputHash == other.vertexInputHash &&
           renderPass == other.renderPass &&
           subpassIndex == other.subpassIndex && topology == other.topology;
  }
};

/**
 * @brief Hash function for GraphicsPipelineKey.
 */
struct GraphicsPipelineKeyHash {
  std::size_t operator()(const GraphicsPipelineKey &key) const {
    size_t seed = 0;
    auto hash_combine = [&](size_t &seed, auto const &v) {
      using T = std::decay_t<decltype(v)>;
      if constexpr (std::is_enum_v<T>) {
        seed ^= std::hash<std::underlying_type_t<T>>{}(
                    static_cast<std::underlying_type_t<T>>(v)) +
                0x9e3779b9 + (seed << 6) + (seed >> 2);
      } else if constexpr (std::is_same_v<T, VkRenderPass> ||
                           std::is_pointer_v<T>) {
        seed ^= std::hash<uint64_t>{}(reinterpret_cast<uint64_t>(v)) +
                0x9e3779b9 + (seed << 6) + (seed >> 2);
      } else {
        seed ^= std::hash<T>{}(v) + 0x9e3779b9 + (seed << 6) + (seed >> 2);
      }
    };
    hash_combine(seed, key.vsShaderId);
    hash_combine(seed, key.psShaderId);
    hash_combine(seed, key.gsShaderId);
    hash_combine(seed, key.hsShaderId);
    hash_combine(seed, key.dsShaderId);
    hash_combine(seed, key.rasterizerStateHash);
    hash_combine(seed, key.blendStateHash);
    hash_combine(seed, key.depthStencilStateHash);
    hash_combine(seed, key.vertexInputHash);
    hash_combine(seed, key.renderPass);
    hash_combine(seed, key.subpassIndex);
    hash_combine(seed, key.topology);
    return seed;
  }
};

/**
 * @brief Structure for render pass key.
 */
struct RenderPassKey {
  std::vector<VkFormat> colorFormats;         ///< Color formats
  VkFormat depthFormat = VK_FORMAT_UNDEFINED; ///< Depth format
  VkAttachmentLoadOp colorLoadOp =
      VK_ATTACHMENT_LOAD_OP_DONT_CARE; ///< Color load op
  VkAttachmentStoreOp colorStoreOp =
      VK_ATTACHMENT_STORE_OP_DONT_CARE; ///< Color store op
  VkAttachmentLoadOp depthLoadOp =
      VK_ATTACHMENT_LOAD_OP_DONT_CARE; ///< Depth load op
  VkAttachmentStoreOp depthStoreOp =
      VK_ATTACHMENT_STORE_OP_DONT_CARE; ///< Depth store op
  VkImageLayout initialColorLayout =
      VK_IMAGE_LAYOUT_UNDEFINED; ///< Initial color layout
  VkImageLayout finalColorLayout =
      VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL; ///< Final color layout
  VkImageLayout initialDepthLayout =
      VK_IMAGE_LAYOUT_UNDEFINED; ///< Initial depth layout
  VkImageLayout finalDepthLayout =
      VK_IMAGE_LAYOUT_DEPTH_STENCIL_ATTACHMENT_OPTIMAL; ///< Final depth layout

  bool operator==(const RenderPassKey &other) const {
    return colorFormats == other.colorFormats &&
           depthFormat == other.depthFormat &&
           colorLoadOp == other.colorLoadOp &&
           colorStoreOp == other.colorStoreOp &&
           depthLoadOp == other.depthLoadOp &&
           depthStoreOp == other.depthStoreOp &&
           initialColorLayout == other.initialColorLayout &&
           finalColorLayout == other.finalColorLayout &&
           initialDepthLayout == other.initialDepthLayout &&
           finalDepthLayout == other.finalDepthLayout;
  }
};

/**
 * @brief Hash function for RenderPassKey.
 */
struct RenderPassKeyHash {
  std::size_t operator()(const RenderPassKey &key) const {
    size_t seed = 0;
    auto hash_combine = [&](size_t &seed, auto const &v) {
      using T = std::decay_t<decltype(v)>;
      if constexpr (std::is_enum_v<T>) {
        seed ^= std::hash<std::underlying_type_t<T>>{}(
                    static_cast<std::underlying_type_t<T>>(v)) +
                0x9e3779b9 + (seed << 6) + (seed >> 2);
      } else if constexpr (std::is_same_v<T, VkRenderPass> ||
                           std::is_pointer_v<T>) {
        seed ^= std::hash<uint64_t>{}(reinterpret_cast<uint64_t>(v)) +
                0x9e3779b9 + (seed << 6) + (seed >> 2);
      } else {
        seed ^= std::hash<T>{}(v) + 0x9e3779b9 + (seed << 6) + (seed >> 2);
      }
    };
    for (const auto &fmt : key.colorFormats) {
      hash_combine(seed, static_cast<std::underlying_type_t<VkFormat>>(fmt));
    }
    hash_combine(
        seed, static_cast<std::underlying_type_t<VkFormat>>(key.depthFormat));
    hash_combine(seed, static_cast<std::underlying_type_t<VkAttachmentLoadOp>>(
                           key.colorLoadOp));
    hash_combine(seed, static_cast<std::underlying_type_t<VkAttachmentStoreOp>>(
                           key.colorStoreOp));
    hash_combine(seed, static_cast<std::underlying_type_t<VkAttachmentLoadOp>>(
                           key.depthLoadOp));
    hash_combine(seed, static_cast<std::underlying_type_t<VkAttachmentStoreOp>>(
                           key.depthStoreOp));
    hash_combine(seed, key.initialColorLayout);
    hash_combine(seed, key.finalColorLayout);
    hash_combine(seed, key.initialDepthLayout);
    hash_combine(seed, key.finalDepthLayout);
    return seed;
  }
};

/**
 * @brief Structure for profiling results.
 */
struct ProfileResult {
  std::string label;           ///< Profile label
  uint64_t startTimestamp = 0; ///< Start timestamp
  uint64_t endTimestamp = 0;   ///< End timestamp
};

/**
 * @brief Structure for GPU statistics with atomic members to prevent race
 * conditions.
 */
struct GPUStats {
  std::atomic<uint64_t> drawCalls{0};          ///< Total draw calls
  std::atomic<uint64_t> shaderCompilations{0}; ///< Total shader compilations
  std::atomic<uint64_t> vramUsage{0};          ///< VRAM usage in bytes
  std::atomic<uint64_t> pipelineCreations{0};  ///< Total pipeline creations
  std::atomic<uint64_t> totalLatencyUs{
      0}; ///< Total operation latency (microseconds)
  std::atomic<uint64_t> cacheHits{0};   ///< Cache hits for GPU operations
  std::atomic<uint64_t> cacheMisses{0}; ///< Cache misses for GPU operations
  std::atomic<uint64_t> shaderCount{0}; ///< Number of active shaders
  std::atomic<uint64_t> drawCount{0};   ///< Total draw call count
  std::atomic<uint64_t> errorCount{0};  ///< Number of GPU errors

  // Copy constructor for atomic members
  GPUStats() = default;
  GPUStats(const GPUStats &other)
      : drawCalls(other.drawCalls.load()),
        shaderCompilations(other.shaderCompilations.load()),
        vramUsage(other.vramUsage.load()),
        pipelineCreations(other.pipelineCreations.load()),
        totalLatencyUs(other.totalLatencyUs.load()),
        cacheHits(other.cacheHits.load()),
        cacheMisses(other.cacheMisses.load()),
        shaderCount(other.shaderCount.load()),
        drawCount(other.drawCount.load()), errorCount(other.errorCount.load()) {
  }

  // Assignment operator for atomic members
  GPUStats &operator=(const GPUStats &other) {
    if (this != &other) {
      drawCalls.store(other.drawCalls.load());
      shaderCompilations.store(other.shaderCompilations.load());
      vramUsage.store(other.vramUsage.load());
      pipelineCreations.store(other.pipelineCreations.load());
      totalLatencyUs.store(other.totalLatencyUs.load());
      cacheHits.store(other.cacheHits.load());
      cacheMisses.store(other.cacheMisses.load());
      shaderCount.store(other.shaderCount.load());
      drawCount.store(other.drawCount.load());
      errorCount.store(other.errorCount.load());
    }
    return *this;
  }
};

/**
 * @brief Emulates the PS4 GPU using Vulkan.
 * @details Manages rendering, shader compilation, and memory, with thread-safe
 * access and metrics.
 */
class PS4GPU {
public:
  /**
   * @brief Constructs the GPU emulator.
   * @param memory MMU instance.
   * @param translator Shader translator.
   * @param tileManager Tile manager.
   * @param vulkanContext Shared Vulkan context.
   * @param window SDL window for swapchain creation.
   */
  PS4GPU(PS4MMU &memory, std::unique_ptr<GNMShaderTranslator> translator,
         std::unique_ptr<TileManager> tileManager, VulkanContext *vulkanContext,
         SDL_Window *window);

  /**
   * @brief Destructor, cleaning up resources.
   */
  ~PS4GPU();

  /**
   * @brief Initializes the GPU.
   * @return True on success, false otherwise.
   */
  bool Initialize();

  /**
   * @brief Shuts down the GPU, releasing resources.
   */
  void Shutdown();

  /**
   * @brief Begins a new frame.
   */
  void BeginFrame();

  /**
   * @brief Ends the current frame.
   */
  void EndFrame();

  /**
   * @brief Presents the rendered frame.
   */
  void Present();

  /**
   * @brief Sets a shader register.
   * @param stage Shader stage.
   * @param offset Register offset.
   * @param value Register value.
   */
  void SetShaderRegister(uint32_t stage, uint32_t offset, uint32_t value);

  /**
   * @brief Sets a context register.
   * @param offset Register offset.
   * @param value Register value.
   */
  void SetContextRegister(uint32_t offset, uint32_t value);

  /**
   * @brief Sets a render target.
   * @param index Target index.
   * @param surfaceId Surface ID.
   */
  void SetRenderTarget(uint32_t index, uint64_t surfaceId);

  /**
   * @brief Sets a depth render target.
   * @param surfaceId Surface ID.
   */
  void SetDepthRenderTarget(uint64_t surfaceId);

  /**
   * @brief Sets the viewport.
   * @param x Viewport x.
   * @param y Viewport y.
   * @param width Viewport width.
   * @param height Viewport height.
   * @param minDepth Minimum depth.
   * @param maxDepth Maximum depth.
   */
  void SetViewport(float x, float y, float width, float height, float minDepth,
                   float maxDepth);

  /**
   * @brief Sets the scissor rectangle.
   * @param x Scissor x.
   * @param y Scissor y.
   * @param width Scissor width.
   * @param height Scissor height.
   */
  void SetScissor(int32_t x, int32_t y, uint32_t width, uint32_t height);

  /**
   * @brief Binds a vertex buffer.
   * @param binding Binding index.
   * @param gpuAddress GPU address.
   * @param stride Buffer stride.
   */
  void BindVertexBuffer(uint32_t binding, uint64_t gpuAddress, uint32_t stride);

  /**
   * @brief Binds an index buffer.
   * @param gpuAddress GPU address.
   * @param indexType Index type.
   */
  void BindIndexBuffer(uint64_t gpuAddress, VkIndexType indexType);

  /**
   * @brief Binds a resource.
   * @param set Descriptor set.
   * @param binding Binding index.
   * @param resourceId Resource ID.
   */
  void BindResource(uint32_t set, uint32_t binding, uint64_t resourceId);

  /**
   * @brief Draws indexed geometry.
   * @param indexCount Index count.
   * @param instanceCount Instance count.
   * @param firstIndex First index.
   * @param vertexOffset Vertex offset.
   * @param firstInstance First instance.
   */
  void DrawIndex(uint32_t indexCount, uint32_t instanceCount,
                 uint32_t firstIndex, int32_t vertexOffset,
                 uint32_t firstInstance);

  /**
   * @brief Draws indexed geometry indirectly.
   * @param bufferAddress Buffer address.
   * @param drawCount Draw count.
   * @param stride Stride.
   */
  void DrawIndexIndirect(uint64_t bufferAddress, uint32_t drawCount,
                         uint32_t stride);

  /**
   * @brief Draws indexed geometry (simplified).
   * @param indexCount Index count.
   * @param instanceCount Instance count.
   * @param firstIndex First index.
   */
  void DrawIndexed(uint32_t indexCount, uint32_t instanceCount,
                   uint32_t firstIndex);

  /**
   * @brief Dispatches a compute workload.
   * @param groupCountX Group count X.
   * @param groupCountY Group count Y.
   * @param groupCountZ Group count Z.
   */
  void Dispatch(uint32_t groupCountX, uint32_t groupCountY,
                uint32_t groupCountZ);

  /**
   * @brief Dispatches a compute workload indirectly.
   * @param bufferAddress Buffer address.
   * @param offset Buffer offset.
   */
  void DispatchIndirect(uint64_t bufferAddress, uint64_t offset);

  /**
   * @brief Waits for a register or memory value.
   * @param address Address to wait on.
   * @param reference Reference value.
   * @param mask Value mask.
   * @param function Comparison function.
   * @param isMemory True if memory, false if register.
   */
  void WaitRegisterMemory(uint64_t address, uint32_t reference, uint32_t mask,
                          uint32_t function, bool isMemory);

  /**
   * @brief Acquires GPU memory.
   * @param address Memory address.
   * @param size Memory size.
   */
  void AcquireMemory(uint64_t address, uint32_t size);

  /**
   * @brief Releases GPU memory.
   * @param address Memory address.
   * @param size Memory size.
   */
  void ReleaseMemory(uint64_t address, uint32_t size);

  /**
   * @brief No-op operation.
   */
  void Nop();

  /**
   * @brief Compiles a shader.
   * @param gcnCode GCN code.
   * @param size Code size.
   * @param type Shader type.
   * @return Shader ID, or 0 on failure.
   */
  uint64_t CompileShader(const void *gcnCode, size_t size, GCNShaderType type);

  /**
   * @brief Unloads a shader.
   * @param shaderId Shader ID.
   */
  void UnloadShader(uint64_t shaderId);

  /**
   * @brief Maps CPU memory to GPU.
   * @param cpuAddress CPU address.
   * @param size Memory size.
   * @param usage Buffer usage flags.
   * @return GPU address, or 0 on failure.
   */
  uint64_t MapMemory(uint64_t cpuAddress, size_t size,
                     VkBufferUsageFlags usage);

  /**
   * @brief Unmaps GPU memory.
   * @param gpuAddress GPU address.
   */
  void UnmapMemory(uint64_t gpuAddress);

  /**
   * @brief Creates a texture.
   * @param surfaceId Surface ID.
   * @return Texture ID, or 0 on failure.
   */
  uint64_t CreateTexture(uint64_t surfaceId);

  /**
   * @brief Updates a texture.
   * @param textureId Texture ID.
   * @param data Texture data.
   * @param size Data size.
   */
  void UpdateTexture(uint64_t textureId, const void *data, size_t size);

  /**
   * @brief Deletes a texture.
   * @param textureId Texture ID.
   */
  void DeleteTexture(uint64_t textureId);

  /**
   * @brief Creates a sampler.
   * @return Sampler ID, or 0 on failure.
   */
  uint64_t CreateSampler();

  /**
   * @brief Deletes a sampler.
   * @param samplerId Sampler ID.
   */
  void DeleteSampler(uint64_t samplerId);

  /**
   * @brief Waits for the GPU to become idle.
   */
  void WaitForGPUIdle();

  /**
   * @brief Inserts a fence.
   * @param fenceValue Output fence value.
   */
  void InsertFence(uint64_t *fenceValue);

  /**
   * @brief Checks if a fence is signaled.
   * @param fenceValue Fence value.
   * @return True if signaled, false otherwise.
   */
  bool CheckFence(uint64_t fenceValue);

  /**
   * @brief Begins GPU profiling.
   * @param label Profile label.
   */
  void BeginGPUProfiler(const std::string &label);

  /**
   * @brief Ends GPU profiling.
   */
  void EndGPUProfiler();

  /**
   * @brief Gets profiling results.
   * @return Vector of profiling results.
   */
  std::vector<ProfileResult> GetProfileResults();

  /**
   * @brief Gets the Vulkan device.
   * @return Vulkan device handle.
   */
  VkDevice GetDevice() const {
    std::shared_lock<std::shared_mutex> lock(m_gpuMutex);
    return m_vulkan ? m_vulkan->device : VK_NULL_HANDLE;
  }

  /**
   * @brief Gets the current command buffer.
   * @return Current command buffer.
   */
  VkCommandBuffer GetCurrentCommandBuffer() const {
    std::shared_lock<std::shared_mutex> lock(m_gpuMutex);
    return m_commandBuffer;
  }

  /**
   * @brief Gets the GNM state.
   * @return Current GNM state.
   */
  const GNMRegisterState &GetGNMState() const {
    std::shared_lock<std::shared_mutex> lock(m_gpuMutex);
    return m_gnmState;
  }

  // Notification methods for component integration
  /**
   * @brief Notifies GPU of packet processing from command processor.
   * @param header Packet header information.
   * @param data Packet data.
   */
  void NotifyPacketProcessed(uint32_t header,
                             const std::vector<uint32_t> &data);

  /**
   * @brief Notifies GPU of shader translation completion.
   * @param type Shader type that was translated.
   * @param bytecodeHash Hash of the original bytecode.
   * @param spirvCode Translated SPIR-V code.
   */
  void NotifyShaderTranslated(GCNShaderType type, uint64_t bytecodeHash,
                              const std::vector<uint32_t> &spirvCode);

  /**
   * @brief Notifies GPU of shader translation completion (GLSL version).
   * @param type Shader type that was translated.
   * @param bytecodeHash Hash of the original bytecode.
   * @param glslCode Translated GLSL code.
   */
  void NotifyShaderTranslated(GCNShaderType type, uint64_t bytecodeHash,
                              const std::string &glslCode);
  /**
   * @brief Notifies GPU of register change for rendering updates.
   * @param regType Type of register (shader, context, etc.).
   * @param stage Shader stage (for shader registers).
   * @param offset Register offset.
   * @param value New register value.
   */
  void NotifyRegisterChange(GNMRegisterType regType, uint32_t stage,
                            uint32_t offset, uint32_t value);

  /**
   * @brief Handles shader register changes.
   * @param stage Shader stage.
   * @param offset Register offset.
   * @param value New register value.
   */
  void HandleShaderRegisterChange(uint32_t stage, uint32_t offset,
                                  uint32_t value);

  /**
   * @brief Handles context register changes.
   * @param offset Register offset.
   * @param value New register value.
   */
  void HandleContextRegisterChange(uint32_t offset, uint32_t value);

  /**
   * @brief Handles config register changes.
   * @param offset Register offset.
   * @param value New register value.
   */
  void HandleConfigRegisterChange(uint32_t offset, uint32_t value);

  /**
   * @brief Handles user config register changes.
   * @param offset Register offset.
   * @param value New register value.
   */
  void HandleUserRegisterChange(uint32_t offset, uint32_t value);

  /**
   * @brief Notifies GPU of shader execution completion.
   * @param shaderType Type of shader that was executed.
   * @param instructionCount Number of instructions executed.
   */
  void NotifyShaderExecuted(GCNShaderType shaderType,
                            uint64_t instructionCount);

  /**
   * @brief Retrieves GPU statistics.
   * @return Current statistics.
   */
  GPUStats GetStats() const;

  /**
   * @brief Clears the shader cache.
   * Destroys all shader modules and resets statistics.
   */
  void ClearShaderCache();

  /**
   * @brief Clears the render target cache.
   * Destroys all render targets and associated resources.
   */
  void ClearRenderTargetCache();

  /**
   * @brief Saves the GPU state to a stream.
   * @param out Output stream.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the GPU state from a stream.
   * @param in Input stream.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Gets the mutable GNM register state.
   * @return Reference to the GNM register state.
   */
  GNMRegisterState &GetMutableGNMState() { return m_gnmState; }
  /**
   * @brief Sets the TileManager instance.
   * @param tileManager Unique_ptr to new TileManager.
   */
  void SetTileManager(std::unique_ptr<TileManager> tileManager);

  /**
   * @brief Gets the TileManager instance.
   * @return Reference to the TileManager.
   */
  TileManager &GetTileManager();

  /**
   * @brief Gets the GNM register state used internally by the GPU.
   * @return Reference to the GNMRegisterState.
   */
  GNMRegisterState &GetRegisterState();

private:
  /**
   * @brief Creates the swapchain.
   * @return True on success, false otherwise.
   */
  bool CreateSwapchain();
  /**
   * @brief Creates swapchain image views.
   * @return True on success, false otherwise.
   */
  bool CreateSwapchainImageViews();

  /**
   * @brief Recreates the swapchain.
   * @return True on success, false otherwise.
   */
  bool RecreateSwapchain();

  /**
   * @brief Creates the command pool.
   * @return True on success, false otherwise.
   */
  bool CreateCommandPool();

  /**
   * @brief Creates the descriptor pool.
   * @return True on success, false otherwise.
   */
  bool CreateDescriptorPool();

  /**
   * @brief Creates synchronization objects.
   * @return True on success, false otherwise.
   */
  bool CreateSyncObjects();

  /**
   * @brief Creates the default render pass.
   * @return True on success, false otherwise.
   */
  bool CreateDefaultRenderPass();

  /**
   * @brief Creates framebuffers.
   * @return True on success, false otherwise.
   */
  bool CreateFramebuffers();
  /**
   * @brief Gets or creates a graphics pipeline.
   * @return Pipeline handle.
   */
  VkPipeline GetOrCreateGraphicsPipeline();

  /**
   * @brief Builds graphics pipeline key from current GNM register state.
   * @return Graphics pipeline key.
   */
  GraphicsPipelineKey BuildGraphicsPipelineKey();

  /**
   * @brief Gets or creates a compute pipeline.
   * @return Pipeline handle.
   */
  VkPipeline GetOrCreateComputePipeline();

  /**
   * @brief Gets or creates a render pass.
   * @param key Render pass key.
   * @return Render pass handle.
   */
  VkRenderPass GetOrCreateRenderPass(const RenderPassKey &key);

  /**
   * @brief Gets or creates a framebuffer.
   * @param colorTargetIds Color target IDs.
   * @param depthTargetId Depth target ID.
   * @return Framebuffer handle.
   */
  VkFramebuffer
  GetOrCreateFramebuffer(const std::vector<uint64_t> &colorTargetIds,
                         uint64_t depthTargetId);

  /**
   * @brief Gets or creates a descriptor set layout.
   * @return Descriptor set layout handle.
   */
  VkDescriptorSetLayout GetOrCreateDescriptorSetLayout();

  /**
   * @brief Allocates a descriptor set.
   * @param layout Descriptor set layout.
   * @return Descriptor set handle.
   */
  VkDescriptorSet AllocateDescriptorSet(VkDescriptorSetLayout layout);

  /**
   * @brief Updates the descriptor set.
   */
  void UpdateDescriptorSet();

  /**
   * @brief Finds a memory type.
   * @param typeFilter Type filter.
   * @param properties Memory properties.
   * @return Memory type index.
   */
  uint32_t FindMemoryType(uint32_t typeFilter,
                          VkMemoryPropertyFlags properties);

  /**
   * @brief Creates a buffer.
   * @param size Buffer size.
   * @param usage Buffer usage.
   * @param properties Memory properties.
   * @param memory Output memory handle.
   * @return Buffer handle.
   */
  VkBuffer CreateBuffer(uint64_t size, VkBufferUsageFlags usage,
                        VkMemoryPropertyFlags properties,
                        VkDeviceMemory &memory);

  /**
   * @brief Begins a command buffer.
   */
  void BeginCommandBuffer();

  /**
   * @brief Submits a command buffer.
   */
  void SubmitCommandBuffer();

  /**
   * @brief Transitions an image layout.
   * @param image Image handle.
   * @param format Image format.
   * @param oldLayout Old layout.
   * @param newLayout New layout.
   */
  void TransitionImageLayout(VkImage image, VkFormat format,
                             VkImageLayout oldLayout, VkImageLayout newLayout);

  /**
   * @brief Copies a buffer to an image.
   * @param buffer Buffer handle.
   * @param image Image handle.
   * @param width Image width.
   * @param height Image height.
   */
  void CopyBufferToImage(VkBuffer buffer, VkImage image, uint32_t width,
                         uint32_t height);

  /**
   * @brief Translates GCN shader to SPIR-V.
   * @param gcnCode GCN code.
   * @param size Code size.
   * @param stage Shader stage.
   * @return Shader ID, or 0 on failure.
   */
  uint64_t TranslateGCNShader(const void *gcnCode, size_t size,
                              VkShaderStageFlagBits stage);
  PS4MMU &m_memory;        ///< Reference to MMU
  VulkanContext *m_vulkan; ///< Vulkan context
  SDL_Window *m_window;    ///< SDL window for swapchain creation
  std::unique_ptr<GNMShaderTranslator>
      m_shaderTranslator;                      ///< Shader translator
  std::unique_ptr<TileManager> m_tileManager;  ///< Tile manager
  GNMRegisterState m_gnmState;                 ///< GNM state
  GNMRegisterState *m_registerState = nullptr; ///< Pointer to register state
  VkCommandBuffer m_commandBuffer = VK_NULL_HANDLE; ///< Current command buffer
  uint32_t m_currentFrame = 0;                      ///< Current frame index
  uint32_t m_currentSwapchainImageIndex = 0; ///< Current swapchain image index
  std::unordered_map<uint64_t, ShaderModule>
      m_shaderModules; ///< Shader modules
  std::unordered_map<uint64_t, RenderTarget>
      m_renderTargets;                                      ///< Render targets
  std::unordered_map<uint64_t, VkImageView> m_textureViews; ///< Texture views
  std::unordered_map<uint64_t, VkSampler> m_samplers;       ///< Samplers
  std::unordered_map<uint64_t, MemoryMapping>
      m_memoryMappings; ///< Memory mappings
  std::unordered_map<GraphicsPipelineKey, VkPipeline, GraphicsPipelineKeyHash>
      m_graphicsPipelineCache; ///< Graphics pipeline cache
  std::unordered_map<uint64_t, VkPipeline>
      m_computePipelineCache; ///< Compute pipeline cache
  std::unordered_map<RenderPassKey, VkRenderPass, RenderPassKeyHash>
      m_renderPassCache; ///< Render pass cache
  std::unordered_map<uint64_t, VkFramebuffer>
      m_framebufferCache;                              ///< Framebuffer cache
  VkRenderPass m_currentRenderPass = VK_NULL_HANDLE;   ///< Current render pass
  VkFramebuffer m_currentFramebuffer = VK_NULL_HANDLE; ///< Current framebuffer
  VkPipelineLayout m_currentPipelineLayout =
      VK_NULL_HANDLE; ///< Current pipeline layout
  VkPipeline m_currentGraphicsPipeline =
      VK_NULL_HANDLE; ///< Current graphics pipeline
  VkPipeline m_currentComputePipeline =
      VK_NULL_HANDLE; ///< Current compute pipeline
  std::vector<VkDescriptorSet>
      m_currentDescriptorSets;                    ///< Current descriptor sets
  VkBuffer m_currentIndexBuffer = VK_NULL_HANDLE; ///< Current index buffer
  VkIndexType m_currentIndexType = VK_INDEX_TYPE_UINT16; ///< Current index type
  std::vector<VkBuffer> m_currentVertexBuffers; ///< Current vertex buffers
  std::vector<VkDeviceSize>
      m_currentVertexBufferOffsets; ///< Current vertex buffer offsets
  std::unordered_map<uint64_t, VkFence> m_fences; ///< Fences
  uint64_t m_nextFenceValue = 1;                  ///< Next fence value
  std::unordered_map<std::string, VkQueryPool>
      m_profileQueryPools;                     ///< Profiling query pools
  std::vector<ProfileResult> m_profileResults; ///< Profiling results
  float m_timestampPeriod = 1.0f;              ///< Timestamp period

  // State dirty flags for efficient updates
  bool m_pipelineStateDirty = true;     ///< Pipeline state needs update
  bool m_viewportStateDirty = true;     ///< Viewport state needs update
  bool m_renderTargetStateDirty = true; ///< Render target state needs update

  mutable std::shared_mutex m_gpuMutex; ///< Mutex for thread safety
  mutable GPUStats m_stats;             ///< GPU statistics (mutable)
};

} // namespace ps4