#pragma once
#include "decoded_instruction.h"
#include "register.h"
#include <array>
#include <cstdint>
#include <string>
#include <unordered_map>

namespace x86_64 {

enum DecoderError {
  Success,
  InvalidInstruction,
  InvalidOpcode,
  InvalidOperand,
  BufferOverflow,
  BufferTooSmall,
  IncompleteInstruction
};

struct DecoderErrorInfo {
  DecoderError error;
  std::string context;
};

struct OpcodeInfo {
  InstructionType type;
  uint8_t operandCount;
  std::array<DecodedInstruction::Operand::Type, 4> operandTypes;
};

class InstructionDecoder {
public:
  InstructionDecoder();

  DecoderErrorInfo Decode(uint64_t address, const uint8_t *buffer,
                          size_t bufferSize, DecodedInstruction &instr);

private:
  void AddOpcode(uint8_t opcode, InstructionType type, uint8_t operandCount,
                 std::array<DecodedInstruction::Operand::Type, 4> types);
  void AddTwoByteOpcode(uint8_t opcode, InstructionType type,
                        uint8_t operandCount,
                        std::array<DecodedInstruction::Operand::Type, 4> types);
  void
  AddThreeByteOpcode(uint16_t opcode, InstructionType type,
                     uint8_t operandCount,
                     std::array<DecodedInstruction::Operand::Type, 4> types);

  Register ResolveRegister(uint8_t baseRegField, uint8_t rexExtBit);

  void ParsePrefixes(const uint8_t *&buffer, size_t &remaining,
                     DecodedInstruction &instr);

  void ParseVEX(const uint8_t *&buffer, size_t &remaining,
                DecodedInstruction &instr);

  void ParseModRM(const uint8_t *&buffer, size_t &remaining,
                  DecodedInstruction &instr, uint8_t modrmByte);

  void ParseOpcode(const uint8_t *&buffer, size_t &remaining,
                   DecodedInstruction &instr);

  // Initialization methods
  void InitializeSingleByteOpcodes();
  void InitializeTwoByteOpcodes();
  void InitializeThreeByteOpcodes();
  void InitializeVEXOpcodes();
  void InitializeEVEXOpcodes();
  void InitializeFPUOpcodes();
  void InitializeGroupOpcodes();

  // Opcode tables
  std::unordered_map<uint8_t, OpcodeInfo> singleByteOpcodes;
  std::unordered_map<uint8_t, OpcodeInfo> twoByteOpcodes;
  std::unordered_map<uint16_t, OpcodeInfo>
      threeByteOpcodes; // 0x0F38xx, 0x0F3Axx
  std::unordered_map<uint32_t, OpcodeInfo> vexOpcodes;
  std::unordered_map<uint32_t, OpcodeInfo> evexOpcodes;
  std::unordered_map<uint16_t, OpcodeInfo> fpuOpcodes;
  std::unordered_map<uint16_t, OpcodeInfo> groupOpcodes;
  std::unordered_map<InstructionType, uint64_t> m_stats;
};

} // namespace x86_64