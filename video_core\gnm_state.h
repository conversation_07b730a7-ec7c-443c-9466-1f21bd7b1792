// Copyright 2025 <Copyright Owner>

#pragma once

#include <array>
#include <cstdint>
#include <functional>
#include <istream>
#include <ostream>
#include <shared_mutex>
#include <string>
#include <unordered_map>
#include <vector>

namespace ps4 {

// Forward declarations
class PS4GPU;

/**
 * @brief GNM register types.
 */
enum class GNMRegisterType {
  SHADER_REG,  ///< Shader registers (per stage)
  CONTEXT_REG, ///< Context registers (rendering state)
  CONFIG_REG,  ///< Configuration registers
  USER_REG     ///< User-defined registers
};

// Callback function types
using RegisterChangeCallback =
    std::function<void(GNMRegisterType, uint32_t, uint32_t, uint32_t)>;

/**
 * @brief Exception for GNM register state errors.
 */
struct GNMException : std::runtime_error {
  explicit GNMException(const std::string &msg) : std::runtime_error(msg) {}
};

/**
 * @brief Cache entry for register accesses.
 */
struct RegisterCacheEntry {
  GNMRegisterType type;     ///< Register type
  uint32_t stage;           ///< Shader stage (for SHADER_REG)
  uint32_t offset;          ///< Register offset
  uint32_t value;           ///< Cached value
  uint64_t cacheHits = 0;   ///< Cache hits for this entry
  uint64_t cacheMisses = 0; ///< Cache misses for this entry
};

/**
 * @brief Statistics for register state operations.
 */
struct GNMRegisterStateStats {
  uint64_t accessCount = 0;    ///< Total register accesses
  uint64_t totalLatencyUs = 0; ///< Total latency in microseconds
  uint64_t cacheHits = 0;      ///< Cache hits for register accesses
  uint64_t cacheMisses = 0;    ///< Cache misses for register accesses
  uint64_t errorCount = 0;     ///< Total errors encountered
};

/**
 * @brief Manages GNM register state for shaders and rendering.
 * @details Provides thread-safe access to shader, context, config, and user
 *          registers, with caching to reduce access overhead. Integrates with
 *          PS4GPU for rendering and GNMShaderTranslator for shader translation.
 *          Supports serialization with versioning and multi-core diagnostics.
 */
class GNMRegisterState {
public:
  /**
   * @brief Constructs the register state manager.
   * @details Initializes register arrays and metrics. Thread-safe.
   */
  GNMRegisterState();

  /**
   * @brief Destructor, cleaning up resources.
   * @details Thread-safe.
   */
  ~GNMRegisterState();

  /**
   * @brief Initializes the register state manager.
   * @return True on success, false on failure.
   * @throws GNMException on initialization errors.
   * @details Populates register names and resets metrics. Thread-safe.
   */
  bool Initialize();

  /**
   * @brief Shuts down the register state manager.
   * @details Clears register names and cache. Thread-safe.
   */
  void Shutdown();

  static constexpr size_t MAX_SHADER_REGS =
      0x1000;                                ///< Max shader registers per stage
  static constexpr size_t SHADER_STAGES = 4; ///< Number of shader stages
  static constexpr size_t MAX_CONTEXT_REGS = 0x400; ///< Max context registers
  static constexpr size_t MAX_CONFIG_REGS = 0x200;  ///< Max config registers
  static constexpr size_t MAX_USER_REGS = 0x100;    ///< Max user registers

  /**
   * @brief Gets a shader register value.
   * @param stage Shader stage (0 to SHADER_STAGES-1).
   * @param offset Register offset.
   * @return Register value.
   * @throws GNMException on invalid stage or offset.
   * @details Thread-safe (read-only). Checks cache first, updates metrics.
   */
  uint32_t GetShaderRegister(uint32_t stage, uint32_t offset) const;

  /**
   * @brief Sets a shader register value.
   * @param stage Shader stage (0 to SHADER_STAGES-1).
   * @param offset Register offset.
   * @param value Value to set.
   * @throws GNMException on invalid stage, offset, or value.
   * @details Thread-safe. Updates cache and metrics.
   */
  void SetShaderRegister(uint32_t stage, uint32_t offset, uint32_t value);

  /**
   * @brief Gets a context register value.
   * @param offset Register offset.
   * @return Register value.
   * @throws GNMException on invalid offset.
   * @details Thread-safe (read-only). Checks cache first, updates metrics.
   */
  uint32_t GetContextRegister(uint32_t offset) const;

  /**
   * @brief Sets a context register value.
   * @param offset Register offset.
   * @param value Value to set.
   * @throws GNMException on invalid offset or value.
   * @details Thread-safe. Updates cache and metrics.
   */
  void SetContextRegister(uint32_t offset, uint32_t value);

  /**
   * @brief Gets a config register value.
   * @param offset Register offset.
   * @return Register value.
   * @throws GNMException on invalid offset.
   * @details Thread-safe (read-only). Checks cache first, updates metrics.
   */
  uint32_t GetConfigRegister(uint32_t offset) const;

  /**
   * @brief Sets a config register value.
   * @param offset Register offset.
   * @param value Value to set.
   * @throws GNMException on invalid offset or value.
   * @details Thread-safe. Updates cache and metrics.
   */
  void SetConfigRegister(uint32_t offset, uint32_t value);

  /**
   * @brief Gets a user register value.
   * @param offset Register offset.
   * @return Register value.
   * @throws GNMException on invalid offset.
   * @details Thread-safe (read-only). Checks cache first, updates metrics.
   */
  uint32_t GetUserRegister(uint32_t offset) const;

  /**
   * @brief Sets a user register value.
   * @param offset Register offset.
   * @param value Value to set.
   * @throws GNMException on invalid offset or value.
   * @details Thread-safe. Updates cache and metrics.
   */
  void SetUserRegister(uint32_t offset, uint32_t value);

  /**
   * @brief Gets the shader base address for a stage.
   * @param stage Shader stage (0 to SHADER_STAGES-1).
   * @return Shader base address.
   * @throws GNMException on invalid stage.
   * @details Thread-safe (read-only). Checks cache, updates metrics.
   */
  uint32_t GetShaderBase(uint32_t stage) const;

  /**
   * @brief Gets a render target address.
   * @param index Render target index (0 to 7).
   * @return Render target address.
   * @throws GNMException on invalid index.
   * @details Thread-safe (read-only). Checks cache, updates metrics.
   */
  uint32_t GetRenderTarget(uint32_t index) const;

  /**
   * @brief Gets the viewport parameters.
   * @param x Output X coordinate.
   * @param y Output Y coordinate.
   * @param width Output width.
   * @param height Output height.
   * @param minDepth Output minimum depth.
   * @param maxDepth Output maximum depth.
   * @details Thread-safe (read-only). Checks cache, updates metrics.
   */
  void GetViewport(float &x, float &y, float &width, float &height,
                   float &minDepth, float &maxDepth) const;

  /**
   * @brief Retrieves a cached register value.
   * @param type Register type.
   * @param stage Shader stage (for SHADER_REG).
   * @param offset Register offset.
   * @param value Output cached value (if found).
   * @return True if cached, false otherwise.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  bool GetCachedRegister(GNMRegisterType type, uint32_t stage, uint32_t offset,
                         uint32_t &value) const;

  /**
   * @brief Clears the register cache.
   * @details Thread-safe. Resets cache metrics.
   */
  void ClearRegisterCache();

  /**
   * @brief Dumps the register state to a string.
   * @param nonZeroOnly If true, only non-zero registers are included.
   * @return State dump string.
   * @details Thread-safe (read-only). Updates metrics.
   */
  std::string DumpState(bool nonZeroOnly = true) const;

  /**
   * @brief Retrieves register state statistics.
   * @return Current statistics.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  GNMRegisterStateStats GetStats() const;

  /**
   * @brief Saves the register state to a stream.
   * @param out Output stream.
   * @details Thread-safe (read-only). Serializes with versioning (version 1).
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the register state from a stream.
   * @param in Input stream.
   * @details Thread-safe. Expects version 1 serialization format.
   * @throws GNMException on invalid state data.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Sets the register change callback.
   * @param callback Callback function to notify PS4GPU of register changes.
   * @details Thread-safe. Used for notifying PS4GPU when registers are
   * modified.
   */
  void SetRegisterChangeCallback(const RegisterChangeCallback &callback);

private:
  /**
   * @brief Initializes register name mappings.
   * @details Thread-safe. Populates shader, context, config, and user register
   * names.
   */
  void InitRegisterNames();

  /**
   * @brief Validates a register access.
   * @param type Register type.
   * @param stage Shader stage (for SHADER_REG).
   * @param offset Register offset.
   * @param value Value to set (for writes).
   * @return True if valid, false otherwise.
   * @details Thread-safe (read-only). Inline for performance.
   */
  inline bool ValidateRegister(GNMRegisterType type, uint32_t stage,
                               uint32_t offset, uint32_t value) const {
    switch (type) {
    case GNMRegisterType::SHADER_REG:
      return stage < SHADER_STAGES && offset < MAX_SHADER_REGS;
    case GNMRegisterType::CONTEXT_REG:
      return offset < MAX_CONTEXT_REGS;
    case GNMRegisterType::CONFIG_REG:
      return offset < MAX_CONFIG_REGS;
    case GNMRegisterType::USER_REG:
      return offset < MAX_USER_REGS;
    default:
      return false;
    }
  }

  std::array<std::array<uint32_t, MAX_SHADER_REGS>, SHADER_STAGES>
      m_shaderRegisters; ///< Shader register arrays
  std::array<uint32_t, MAX_CONTEXT_REGS>
      m_contextRegisters; ///< Context registers
  std::array<uint32_t, MAX_CONFIG_REGS> m_configRegisters; ///< Config registers
  std::array<uint32_t, MAX_USER_REGS> m_userRegisters;     ///< User registers
  std::unordered_map<uint32_t, std::string>
      m_shaderRegNames; ///< Shader register names
  std::unordered_map<uint32_t, std::string>
      m_contextRegNames; ///< Context register names
  std::unordered_map<uint32_t, std::string>
      m_configRegNames; ///< Config register names
  std::unordered_map<uint32_t, std::string>
      m_userRegNames; ///< User register names
  mutable std::unordered_map<uint64_t, RegisterCacheEntry>
      m_registerCache; ///< Register cache

  RegisterChangeCallback
      m_registerChangeCallback; ///< Callback for register changes

  mutable std::shared_mutex m_regMutex;  ///< Mutex for thread safety
  mutable GNMRegisterStateStats m_stats; ///< Register state statistics
};

} // namespace ps4