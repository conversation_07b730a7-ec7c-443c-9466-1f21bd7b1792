// Copyright 2025 <Copyright Owner>

#include "x86_64_jit_compiler.h"
#include "../cpu/instruction_decoder.h"
#include "../cpu/x86_64_cpu.h"
#include "x86_64_jit_helpers.h"
#include <algorithm>
#include <chrono>
#include <cstring>
#include <emmintrin.h>
#include <immintrin.h>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <string>
#include <vector>

#ifdef _WIN32
#include <windows.h>
#else
#include <sys/mman.h>
#endif

namespace x86_64 {
struct JITCompileException : std::runtime_error {
  explicit JITCompileException(const std::string &msg)
      : std::runtime_error(msg) {}
};

/**
 * @brief Constructs the JIT compiler.
 * @param cpu Pointer to the associated X86_64CPU instance.
 */
X86_64JITCompiler::X86_64JITCompiler(X86_64CPU *cpu) : m_cpu(cpu) {
  if (!cpu) {
    spdlog::error("JIT compiler: null CPU pointer");
    throw JITCompileException("Null CPU pointer");
  }

  // CRITICAL FIX: Ensure all statistics are properly initialized to zero
  m_stats = {};
  m_stats.blocksCompiled = 0;
  m_stats.executions = 0;
  m_stats.cacheClears = 0;
  m_stats.simdInstructions = 0;
  m_stats.compilationLatencyUs = 0;
  m_stats.cacheHits = 0;
  m_stats.cacheMisses = 0;

  spdlog::info(
      "JIT compiler initialized for CPU 0x{:x} with zero-initialized stats",
      reinterpret_cast<uintptr_t>(cpu));
}

/**
 * @brief Destructs the JIT compiler, cleaning up allocated resources.
 */
X86_64JITCompiler::~X86_64JITCompiler() noexcept { ClearCache(); }

/**
 * @brief Checks if JIT compilation is available on the current platform.
 * @return True if JIT compilation is available, false otherwise.
 */
bool X86_64JITCompiler::IsAvailable() const {
#ifdef _WIN32 // On Windows, check if VirtualAlloc with PAGE_EXECUTE_READWRITE
              // is available
  SYSTEM_INFO sysInfo;
  GetSystemInfo(&sysInfo);
  return sysInfo.wProcessorArchitecture == PROCESSOR_ARCHITECTURE_AMD64;
#else
  // On Unix-like systems, check if mmap with PROT_EXEC is available
  return true; // Assume available on Unix-like systems for x86_64
#endif
}

/**
 * @brief Compiles a basic block starting at the given program counter.
 * @param pc Starting program counter.
 * @return True on successful compilation, false otherwise.
 */
bool X86_64JITCompiler::CompileBlock(uint64_t pc) {
  auto start = std::chrono::steady_clock::now();
  std::lock_guard<std::mutex> lock(m_cacheMutex);
  try {
    if (m_compiledBlocks.find(pc) != m_compiledBlocks.end()) {
      m_compiledBlocks[pc].lastUsed = m_cycleCount++;
      m_stats.cacheHits++;
      spdlog::trace("Reusing cached JIT block at 0x{:x}", pc);
      return true;
    }
    m_stats.cacheMisses++;

    InstructionDecoder decoder;
    std::vector<DecodedInstruction> instructions;
    uint64_t currentPc = pc;
    size_t instructionCount = 0;
    constexpr size_t MAX_INSTRUCTIONS = 64;

    while (instructionCount < MAX_INSTRUCTIONS) {
      std::vector<uint8_t> buffer(16);
      try {
        // CRITICAL FIX: Add bounds checking for memory reads
        if (currentPc == 0 || currentPc == UINT64_MAX) {
          spdlog::error("JIT compile: invalid PC address 0x{:x}", currentPc);
          return false;
        }
        m_cpu->GetMemory().ReadVirt(currentPc, buffer.data(), buffer.size(),
                                    m_cpu->GetProcessId());
        DecodedInstruction instr;
        DecoderErrorInfo error =
            decoder.Decode(currentPc, buffer.data(), buffer.size(), instr);
        if (error.error != DecoderError::Success) {
          spdlog::error("JIT decode failed at 0x{:x}: {}", currentPc,
                        error.context);
          return false;
        }
        instructions.push_back(instr);
        currentPc += instr.length;
        instructionCount++;
        if (instr.instType == InstructionType::Jump ||
            instr.instType == InstructionType::Ret ||
            instr.instType == InstructionType::Call ||
            instr.instType == InstructionType::Int ||
            instr.instType == InstructionType::Iret ||
            instr.instType == InstructionType::Jcc) {
          break;
        }
      } catch (const std::exception &e) {
        spdlog::error("JIT fetch failed at 0x{:x}: {}", currentPc, e.what());
        return false;
      }
    }

    std::vector<uint8_t> code;
    AllocateRegisters(instructions);

    // Prologue: Save caller-saved registers
    code.insert(code.end(), {
                                0x53,       // push rbx
                                0x41, 0x54, // push r12
                                0x41, 0x55, // push r13
                                0x41, 0x56, // push r14
                                0x55        // push rbp
                            });

    for (size_t i = 0; i < instructions.size(); ++i) {
      const auto &instr = instructions[i];
      uint64_t nextRip = currentPc + instr.length;
      switch (instr.instType) {
      case InstructionType::Nop:
        code.push_back(0x90);
        break;
      case InstructionType::Mov: {
        constexpr uint8_t SCRATCH_REG_1 = 0; // RAX
        EmitLoadOperand(instr.operands[1], SCRATCH_REG_1, nextRip, code);
        EmitStoreOperand(instr.operands[0], SCRATCH_REG_1, nextRip, code);
        break;
      }
      case InstructionType::Add: {
        constexpr uint8_t SCRATCH_REG_1 = 0; // RAX
        constexpr uint8_t SCRATCH_REG_2 = 1; // RBX
        EmitLoadOperand(instr.operands[0], SCRATCH_REG_1, nextRip, code);
        EmitLoadOperand(instr.operands[1], SCRATCH_REG_2, nextRip, code);
        // ADD RAX, RBX
        code.insert(code.end(), {0x48, 0x01, 0xD8});
        EmitStoreOperand(instr.operands[0], SCRATCH_REG_1, nextRip, code);
        break;
      }
      case InstructionType::Sub: {
        constexpr uint8_t SCRATCH_REG_1 = 0; // RAX
        constexpr uint8_t SCRATCH_REG_2 = 1; // RBX
        EmitLoadOperand(instr.operands[0], SCRATCH_REG_1, nextRip, code);
        EmitLoadOperand(instr.operands[1], SCRATCH_REG_2, nextRip, code);
        // SUB RAX, RBX
        code.insert(code.end(), {0x48, 0x29, 0xD8});
        EmitStoreOperand(instr.operands[0], SCRATCH_REG_1, nextRip, code);
        break;
      }
      case InstructionType::Push: {
        constexpr uint8_t SCRATCH_REG_1 = 0; // RAX
        EmitLoadOperand(instr.operands[0], SCRATCH_REG_1, nextRip, code);
        // PUSH RAX
        code.insert(code.end(), {0x50});
        break;
      }
      case InstructionType::Pop: {
        constexpr uint8_t SCRATCH_REG_1 = 0; // RAX
        // POP RAX
        code.insert(code.end(), {0x58});
        EmitStoreOperand(instr.operands[0], SCRATCH_REG_1, nextRip, code);
        break;
      }
      case InstructionType::Addps:
      case InstructionType::Subps:
      case InstructionType::Mulps: {
        if (m_simdOptimizationsEnabled) {
          // CRITICAL FIX: Ensure SIMD instruction counting is properly
          // initialized
          m_stats.simdInstructions++;
          if (!CompileSIMD(instr, code)) {
            spdlog::error("Failed to compile SIMD instruction {} at 0x{:x}",
                          static_cast<int>(instr.instType), pc);
            return false;
          }
        } else {
          spdlog::warn("SIMD instruction {} at 0x{:x} ignored (SIMD "
                       "optimizations disabled)",
                       static_cast<int>(instr.instType), pc);
          return false;
        }
        break;
      }
      default:
        spdlog::error("Unsupported JIT instruction {} at 0x{:x}",
                      static_cast<int>(instr.instType), pc);
        return false;
      }
    }

    // Epilogue: Restore registers and return
    code.insert(code.end(), {
                                0x5D,       // pop rbp
                                0x41, 0x5E, // pop r14
                                0x41, 0x5D, // pop r13
                                0x41, 0x5C, // pop r12
                                0x5B,       // pop rbx
                                0xC3        // ret
                            });

    OptimizeBlock(code); // CRITICAL FIX: Enhanced validation for code size
                         // before allocation
    if (code.empty()) {
      spdlog::error("JIT compile: generated code is empty for block at 0x{:x}",
                    pc);
      return false;
    }

    // Multiple size validation checks for robustness
    constexpr size_t MIN_CODE_SIZE = 16; // Minimum reasonable code size
    constexpr size_t MAX_BLOCK_SIZE = 1024 * 1024; // 1MB limit per block
    constexpr size_t WARN_SIZE = 64 * 1024;        // Warning threshold (64KB)

    if (code.size() < MIN_CODE_SIZE) {
      spdlog::error("JIT compile: generated code too small ({} bytes) for "
                    "block at 0x{:x}, minimum {} bytes",
                    code.size(), pc, MIN_CODE_SIZE);
      return false;
    }

    if (code.size() > MAX_BLOCK_SIZE) {
      spdlog::error("JIT compile: generated code too large ({} bytes) for "
                    "block at 0x{:x}, maximum {} bytes",
                    code.size(), pc, MAX_BLOCK_SIZE);
      return false;
    }

    if (code.size() > WARN_SIZE) {
      spdlog::warn("JIT compile: large code block ({} bytes) at 0x{:x}, "
                   "consider optimization",
                   code.size(), pc);
    }

    // Validate instruction alignment and consistency
    size_t instructionBytes = 0;
    for (const auto &instr : instructions) {
      instructionBytes += instr.length;
    }

    // Sanity check: generated code should be reasonably proportional to source
    if (code.size() > instructionBytes * 10) {
      spdlog::warn(
          "JIT compile: generated code size ({} bytes) seems "
          "disproportionately large compared to source ({} bytes) at 0x{:x}",
          code.size(), instructionBytes, pc);
    }

    uint8_t *executableCode = AllocateExecutableMemory(code.size());
    if (!executableCode) {
      spdlog::error("JIT compile: failed to allocate executable memory for "
                    "block at 0x{:x}",
                    pc);
      return false;
    }
    std::memcpy(executableCode, code.data(), code.size());

    // CRITICAL FIX: Fix up relative addresses for CALL instructions
    FixupRelativeAddresses(executableCode, code.size());

    m_totalCodeSize += code.size();
    if (m_totalCodeSize > MAX_CODE_SIZE) {
      ClearCache();
    }

    auto end = std::chrono::steady_clock::now();
    uint64_t compilationTimeUs =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.compilationLatencyUs += compilationTimeUs;

    // CRITICAL FIX: Calculate pipeline latency including instruction fetch
    // cycles
    uint32_t branchCount = static_cast<uint32_t>(std::count_if(
        instructions.begin(), instructions.end(), [](const auto &i) {
          return i.instType == InstructionType::Jump ||
                 i.instType == InstructionType::Jcc ||
                 i.instType == InstructionType::Call ||
                 i.instType == InstructionType::Ret;
        }));

    // Estimate pipeline latency: base cycles + instruction fetch cycles +
    // branch penalty
    uint64_t estimatedPipelineLatency =
        instructions.size() * 2;                     // Base execution cycles
    estimatedPipelineLatency += instructions.size(); // Instruction fetch cycles
    estimatedPipelineLatency += branchCount * 3; // Branch misprediction penalty

    m_compiledBlocks[pc] = {
        executableCode,
        code.size(),
        m_cycleCount++,
        branchCount,
        0,
        estimatedPipelineLatency, // Store estimated pipeline latency
        false,
        compilationTimeUs};
    m_stats.blocksCompiled++;
    spdlog::info(
        "Compiled JIT block at 0x{:x}, size={} bytes, branches={}, time={}us",
        pc, code.size(), m_compiledBlocks[pc].branchCount, compilationTimeUs);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("JIT compilation failed at 0x{:x}: {}", pc, e.what());
    return false;
  }
}

/**
 * @brief Executes compiled code at the given program counter.
 * @param pc Starting program counter.
 * @return True if executed, false if no compiled code exists.
 */
bool X86_64JITCompiler::ExecuteCompiledCode(uint64_t pc) {
  auto start = std::chrono::steady_clock::now();
  std::lock_guard<std::mutex> lock(m_cacheMutex);
  try {
    auto it = m_compiledBlocks.find(pc);
    if (it == m_compiledBlocks.end() || !it->second.code) {
      m_stats.cacheMisses++;
      return false;
    }
    m_stats.cacheHits++;
    using JitFunc = void (*)(X86_64CPU *);
    JitFunc func = reinterpret_cast<JitFunc>(it->second.code);
    m_stats.executions++;
    it->second.executionCount++;
    func(m_cpu);
    auto end = std::chrono::steady_clock::now();
    uint64_t executionTimeUs =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    it->second.totalCycles += executionTimeUs;
    spdlog::trace("Executed JIT block at 0x{:x}, time={}us", pc,
                  executionTimeUs);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("JIT execution failed at 0x{:x}: {}", pc, e.what());
    return false;
  }
}

/**
 * @brief Clears the entire JIT cache.
 */
void X86_64JITCompiler::ClearCache() {
  std::lock_guard<std::mutex> lock(m_cacheMutex);
  try {
    for (auto &entry : m_compiledBlocks) {
      FreeExecutableMemory(entry.second.code, entry.second.size);
    }
    m_compiledBlocks.clear();
    m_totalCodeSize = 0;
    m_stats.cacheClears++;
    spdlog::info("JIT cache cleared");
  } catch (const std::exception &e) {
    spdlog::error("JIT cache clear failed: {}", e.what());
  }
}

/**
 * @brief Invalidates a compiled block at the given address.
 * @param pc Block address.
 */
void X86_64JITCompiler::InvalidateBlock(uint64_t pc) {
  std::lock_guard<std::mutex> lock(m_cacheMutex);
  try {
    auto it = m_compiledBlocks.find(pc);
    if (it != m_compiledBlocks.end()) {
      FreeExecutableMemory(it->second.code, it->second.size);
      m_totalCodeSize -= it->second.size;
      m_compiledBlocks.erase(it);
      spdlog::trace("Invalidated JIT block at 0x{:x}", pc);
    }
  } catch (const std::exception &e) {
    spdlog::error("JIT block invalidation failed at 0x{:x}: {}", pc, e.what());
  }
}

/**
 * @brief Profiles a block to identify hot code paths.
 * @param pc Block address.
 * @param cycles Cycles spent in the block.
 */
void X86_64JITCompiler::ProfileBlock(uint64_t pc, uint64_t cycles) {
  std::lock_guard<std::mutex> lock(m_cacheMutex);
  auto it = m_compiledBlocks.find(pc);
  if (it != m_compiledBlocks.end()) {
    it->second.executionCount++;
    it->second.totalCycles += cycles;
    if (it->second.executionCount > 1000) { // Threshold
      it->second.isHotPath = true;
    }
    spdlog::trace("Profiled JIT block at 0x{:x}, executions={}, cycles={}", pc,
                  it->second.executionCount, it->second.totalCycles);
  }
}

/**
 * @brief Recompiles a hot block with higher optimization levels.
 * @param pc Block address.
 * @param optimizationLevel Optimization level to apply.
 * @return True on successful recompilation, false otherwise.
 */
bool X86_64JITCompiler::RecompileHotBlock(uint64_t pc, int optimizationLevel) {
  std::lock_guard<std::mutex> lock(m_cacheMutex);
  try {
    InvalidateBlock(pc);
    bool success = CompileBlock(pc);
    if (success && m_tieredCompilationEnabled) {
      auto it = m_compiledBlocks.find(pc);
      if (it != m_compiledBlocks.end()) {
        it->second.isHotPath = true;
        spdlog::info(
            "Recompiled hot JIT block at 0x{:x}, optimization level={}", pc,
            optimizationLevel);
      }
    }
    return success;
  } catch (const std::exception &e) {
    spdlog::error("Hot block recompilation failed at 0x{:x}: {}", pc, e.what());
    return false;
  }
}

/**
 * @brief Gets the cache usage ratio.
 * @return Usage ratio (0.0 to 1.0).
 */
float X86_64JITCompiler::GetCacheUsage() const {
  std::lock_guard<std::mutex> lock(m_cacheMutex);
  return static_cast<float>(m_totalCodeSize) / MAX_CODE_SIZE;
}

/**
 * @brief Retrieves the top N hot blocks for optimization reports.
 * @param count Number of hot blocks to retrieve.
 * @return Vector of hot blocks and their execution counts.
 */
std::vector<std::pair<uint64_t, uint32_t>>
X86_64JITCompiler::GetHotBlocks(size_t count) const {
  std::lock_guard<std::mutex> lock(m_cacheMutex);
  std::vector<std::pair<uint64_t, uint32_t>> hotBlocks;
  for (const auto &entry : m_compiledBlocks) {
    if (entry.second.isHotPath) {
      hotBlocks.emplace_back(entry.first, entry.second.executionCount);
    }
  }
  std::sort(hotBlocks.begin(), hotBlocks.end(),
            [](const auto &a, const auto &b) { return a.second > b.second; });
  if (hotBlocks.size() > count) {
    hotBlocks.resize(count);
  }
  return hotBlocks;
}

/**
 * @brief Allocates executable memory for JIT code.
 * @param size Size in bytes.
 * @return Pointer to allocated memory.
 */
uint8_t *X86_64JITCompiler::AllocateExecutableMemory(size_t size) {
  try {
    // CRITICAL FIX: Enhanced size validation and alignment
    if (size == 0) {
      spdlog::error("AllocateExecutableMemory: invalid size 0");
      return nullptr;
    }
    if (size > MAX_CODE_SIZE) {
      spdlog::error("AllocateExecutableMemory: size {} exceeds maximum {}",
                    size, MAX_CODE_SIZE);
      return nullptr;
    }

    // Additional validation for reasonable size limits
    constexpr size_t MIN_ALLOC_SIZE = 16;                // Minimum allocation
    constexpr size_t MAX_SINGLE_ALLOC = 2 * 1024 * 1024; // 2MB per allocation

    if (size < MIN_ALLOC_SIZE) {
      spdlog::error("AllocateExecutableMemory: size {} below minimum {}", size,
                    MIN_ALLOC_SIZE);
      return nullptr;
    }

    if (size > MAX_SINGLE_ALLOC) {
      spdlog::error("AllocateExecutableMemory: size {} exceeds single "
                    "allocation limit {}",
                    size, MAX_SINGLE_ALLOC);
      return nullptr;
    }

    // Enhanced alignment: align to page boundary for better performance and
    // security
    const size_t pageSize = 4096;
    size_t alignedSize = (size + pageSize - 1) & ~(pageSize - 1);

    // Additional security: ensure alignment doesn't cause overflow
    if (alignedSize < size) {
      spdlog::error(
          "AllocateExecutableMemory: size alignment overflow for size {}",
          size);
      return nullptr;
    }

    // For larger allocations, use 64KB alignment for better performance
    if (alignedSize >= 64 * 1024) {
      const size_t largePageSize = 64 * 1024;
      alignedSize = (size + largePageSize - 1) & ~(largePageSize - 1);

      if (alignedSize < size) {
        spdlog::error("AllocateExecutableMemory: large page alignment overflow "
                      "for size {}",
                      size);
        return nullptr;
      }
    }

#ifdef _WIN32
    void *mem = VirtualAlloc(nullptr, alignedSize, MEM_COMMIT | MEM_RESERVE,
                             PAGE_EXECUTE_READWRITE);
    if (!mem) {
      DWORD error = GetLastError();
      spdlog::error("VirtualAlloc failed with error {} for size {}", error,
                    alignedSize);
      return nullptr;
    }

    // Additional Windows-specific validation
    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQuery(mem, &mbi, sizeof(mbi)) == sizeof(mbi)) {
      if (mbi.State != MEM_COMMIT || mbi.Protect != PAGE_EXECUTE_READWRITE) {
        spdlog::error("VirtualAlloc returned memory with incorrect properties");
        VirtualFree(mem, 0, MEM_RELEASE);
        return nullptr;
      }
    }

    spdlog::trace(
        "Allocated executable memory: size={}, aligned={}, addr=0x{:x}", size,
        alignedSize, reinterpret_cast<uintptr_t>(mem));
    return static_cast<uint8_t *>(mem);
#else
    void *mem = mmap(nullptr, alignedSize, PROT_READ | PROT_WRITE | PROT_EXEC,
                     MAP_PRIVATE | MAP_ANONYMOUS, -1, 0);
    if (mem == MAP_FAILED) {
      spdlog::error("mmap failed with errno {} for size {}", errno,
                    alignedSize);
      return nullptr;
    }

    // Validate the returned memory alignment
    uintptr_t addr = reinterpret_cast<uintptr_t>(mem);
    if ((addr & (pageSize - 1)) != 0) {
      spdlog::error("mmap returned unaligned memory: addr=0x{:x}, pageSize={}",
                    addr, pageSize);
      munmap(mem, alignedSize);
      return nullptr;
    }

    spdlog::trace(
        "Allocated executable memory: size={}, aligned={}, addr=0x{:x}", size,
        alignedSize, addr);
    return static_cast<uint8_t *>(mem);
#endif
  } catch (const std::exception &e) {
    spdlog::error("Memory allocation failed: {}", e.what());
    return nullptr;
  }
}

/**
 * @brief Frees executable memory.
 * @param ptr Pointer to memory.
 * @param size Size in bytes.
 */
void X86_64JITCompiler::FreeExecutableMemory(uint8_t *ptr, size_t size) {
  try {
    if (!ptr) {
      spdlog::warn("FreeExecutableMemory: null pointer passed");
      return;
    }
    if (size == 0) {
      spdlog::warn("FreeExecutableMemory: zero size passed for ptr 0x{:x}",
                   reinterpret_cast<uintptr_t>(ptr));
      return;
    }

    // Align size to page boundary (same as allocation)
    const size_t pageSize = 4096;
    size_t alignedSize = (size + pageSize - 1) & ~(pageSize - 1);

#ifdef _WIN32
    if (!VirtualFree(ptr, 0, MEM_RELEASE)) {
      DWORD error = GetLastError();
      spdlog::error("VirtualFree failed with error {} for ptr 0x{:x}", error,
                    reinterpret_cast<uintptr_t>(ptr));
    }
#else
    if (munmap(ptr, alignedSize) != 0) {
      spdlog::error("munmap failed with errno {} for ptr 0x{:x}", errno,
                    reinterpret_cast<uintptr_t>(ptr));
    }
#endif
  } catch (const std::exception &e) {
    spdlog::error("Memory free failed: {}", e.what());
  }
}

/**
 * @brief Emits code to load an operand into a scratch register.
 * @param operand The operand to load.
 * @param scratchReg Scratch register index.
 * @param nextRip Next instruction pointer.
 * @param code Output code vector.
 */
void X86_64JITCompiler::EmitLoadOperand(
    const DecodedInstruction::Operand &operand, uint8_t scratchReg,
    uint64_t nextRip, std::vector<uint8_t> &code) {
  try {
    switch (operand.type) {
    case DecodedInstruction::Operand::Type::IMMEDIATE: {
      code.insert(code.end(),
                  {0x48, static_cast<uint8_t>(0xB8 | (scratchReg & 0x7))});
      for (uint8_t i = 0; i < 8; ++i) {
        code.push_back((operand.immediate >> (8 * i)) & 0xFF);
      }
      break;
    }
    case DecodedInstruction::Operand::Type::REGISTER: {
      uint32_t offset = offsetof(CPUContext, registers) +
                        static_cast<uint32_t>(operand.reg) * 8;
      code.insert(
          code.end(),
          {0x48, 0x8B, static_cast<uint8_t>(0x80 | (scratchReg & 0x7))});
      code.push_back(offset & 0xFF);
      code.push_back((offset >> 8) & 0xFF);
      code.push_back((offset >> 16) & 0xFF);
      code.push_back((offset >> 24) & 0xFF);
      break;
    }
    case DecodedInstruction::Operand::Type::MEMORY: {
      uint64_t addr =
          operand.memory.base == Register::NONE ? operand.immediate : 0;
      code.insert(code.end(), {0x48, 0xBA});
      for (int i = 0; i < 8; ++i) {
        code.push_back((addr >> (8 * i)) & 0xFF);
      }
      code.insert(code.end(), {0x4C, static_cast<uint8_t>(0x89), 0xC0});
      auto helper = operand.size == 1   ? (void *)&jitReadMem8
                    : operand.size == 2 ? (void *)&jitReadMem16
                    : operand.size == 4 ? (void *)&jitReadMem32
                                        : (void *)&jitReadMem64;
      code.push_back(0xE8);
      // CRITICAL FIX: Correct relative addressing calculation for CALL
      // instructions The call site address should be calculated based on where
      // the instruction will be in the final executable memory, not in the
      // temporary code vector
      size_t callInstrOffset = code.size() - 1; // Position of the 0xE8 byte
      // Reserve space for the 4-byte relative offset
      code.insert(code.end(), 4, 0x00);
      // Calculate relative offset - this will be fixed up when the code is
      // allocated For now, store the helper address directly and fix it up
      // later
      *reinterpret_cast<void **>(&code[code.size() - 4]) = helper;
      break;
    }
    default:
      throw JITCompileException("Unsupported operand for load");
    }
  } catch (const std::exception &e) {
    spdlog::error("EmitLoadOperand failed: {}", e.what());
    throw JITCompileException("Operand load emission failure");
  }
}

/**
 * @brief Emits code to store a scratch register into an operand.
 * @param operand The operand to store.
 * @param scratchReg Scratch register index.
 * @param nextRip Next instruction pointer.
 * @param code Output code vector.
 */
void X86_64JITCompiler::EmitStoreOperand(
    const DecodedInstruction::Operand &operand, uint8_t scratchReg,
    uint64_t nextRip, std::vector<uint8_t> &code) {
  try {
    switch (operand.type) {
    case DecodedInstruction::Operand::Type::REGISTER: {
      uint32_t offset = offsetof(CPUContext, registers) +
                        static_cast<uint32_t>(operand.reg) * 8;
      code.insert(
          code.end(),
          {0x48, 0x89, static_cast<uint8_t>(0x80 | (scratchReg & 0x7))});
      code.push_back(offset & 0xFF);
      code.push_back((offset >> 8) & 0xFF);
      code.push_back((offset >> 16) & 0xFF);
      code.push_back((offset >> 24) & 0xFF);
      break;
    }
    case DecodedInstruction::Operand::Type::MEMORY: {
      uint64_t addr =
          operand.memory.base == Register::NONE ? operand.immediate : 0;
      code.insert(code.end(), {0x48, 0xBA});
      for (int i = 0; i < 8; ++i) {
        code.push_back((addr >> (8 * i)) & 0xFF);
      }
      code.insert(
          code.end(),
          {0x4C, static_cast<uint8_t>(0x89 | ((scratchReg & 0x7) << 3)), 0xC0});
      auto helper = operand.size == 1   ? (void *)&jitWriteMem8
                    : operand.size == 2 ? (void *)&jitWriteMem16
                    : operand.size == 4 ? (void *)&jitWriteMem32
                                        : (void *)&jitWriteMem64;
      code.push_back(0xE8);
      // CRITICAL FIX: Correct relative addressing calculation for CALL
      // instructions The call site address should be calculated based on where
      // the instruction will be in the final executable memory, not in the
      // temporary code vector
      size_t callInstrOffset = code.size() - 1; // Position of the 0xE8 byte
      // Reserve space for the 4-byte relative offset
      code.insert(code.end(), 4, 0x00);
      // Calculate relative offset - this will be fixed up when the code is
      // allocated For now, store the helper address directly and fix it up
      // later
      *reinterpret_cast<void **>(&code[code.size() - 4]) = helper;
      break;
    }
    default:
      throw JITCompileException("Unsupported operand for store");
    }
  } catch (const std::exception &e) {
    spdlog::error("EmitStoreOperand failed: {}", e.what());
    throw JITCompileException("Operand store emission failure");
  }
}

/**
 * @brief Compiles a SIMD instruction.
 * @param instr The SIMD instruction.
 * @param code Output code vector.
 * @return True on success, false otherwise.
 */
bool X86_64JITCompiler::CompileSIMD(const DecodedInstruction &instr,
                                    std::vector<uint8_t> &code) {
  try {
    // CRITICAL FIX: Enhanced SIMD operand validation
    if (instr.operandCount < 2) {
      spdlog::error("SIMD instruction {} requires at least 2 operands, got {}",
                    static_cast<int>(instr.instType), instr.operandCount);
      return false;
    }

    // CRITICAL FIX: Properly handle SIMD operand sizes - check for both bits
    // and bytes
    uint32_t sizeBits = instr.operands[0].size;
    uint32_t sizeBytes = sizeBits / 8;

    // Support both 128-bit (XMM) and 256-bit (YMM) SIMD operations
    // Also handle cases where size might be specified in bytes instead of bits
    if (sizeBits != 128 && sizeBits != 256 && sizeBits != 16 &&
        sizeBits != 32) {
      spdlog::error("Invalid SIMD instruction size: {} bits ({} bytes) for "
                    "instruction {}",
                    sizeBits, sizeBytes, static_cast<int>(instr.instType));
      return false;
    }

    // Normalize size to bits if it was specified in bytes
    if (sizeBits == 16)
      sizeBits = 128; // 16 bytes = 128 bits
    if (sizeBits == 32)
      sizeBits = 256; // 32 bytes = 256 bits
    sizeBytes = sizeBits / 8;

    void *helper = nullptr;
    if (instr.instType == InstructionType::Addps) {
      helper = (void *)&jitAddps;
    } else if (instr.instType == InstructionType::Subps) {
      helper = (void *)&jitSubps;
    } else if (instr.instType == InstructionType::Mulps) {
      helper = (void *)&jitMulps;
    } else {
      spdlog::error("Unsupported SIMD instruction type: {}",
                    static_cast<int>(instr.instType));
      return false;
    }

    // Emit the CALL instruction
    code.push_back(0xE8);
    // CRITICAL FIX: Correct relative addressing calculation for CALL
    // instructions The call site address should be calculated based on where
    // the instruction will be in the final executable memory, not in the
    // temporary code vector
    size_t callInstrOffset = code.size() - 1; // Position of the 0xE8 byte
    // Reserve space for the 4-byte relative offset
    code.insert(code.end(), 4, 0x00);
    // Calculate relative offset - this will be fixed up when the code is
    // allocated For now, store the helper address directly and fix it up later
    *reinterpret_cast<void **>(&code[code.size() - 4]) = helper;

    spdlog::trace("Compiled SIMD instruction {} with size {} bits",
                  static_cast<int>(instr.instType), sizeBits);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("SIMD compilation failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Allocates registers for a set of instructions.
 * @param instructions Instructions to process.
 */
void X86_64JITCompiler::AllocateRegisters(
    std::vector<DecodedInstruction> &instructions) {

  if (instructions.empty()) {
    spdlog::trace("AllocateRegisters: no instructions to process");
    return;
  }

  try {
    // ENHANCEMENT: Initialize register states
    m_registerStates.clear();
    m_registerStates.resize(NUM_PHYSICAL_REGS);
    for (auto &state : m_registerStates) {
      state.isAvailable = true;
      state.lastUsed = 0;
      state.boundVReg = Register::NONE;
    }

    // ENHANCEMENT: Perform live range analysis
    std::vector<LiveRange> liveRanges;
    PerformLiveRangeAnalysis(instructions, liveRanges);

    // ENHANCEMENT: Apply linear scan register allocation
    if (!LinearScanRegisterAllocation(liveRanges)) {
      spdlog::warn("Register allocation failed, falling back to spilling");
    }

    spdlog::trace("Allocated registers for {} instructions with {} live ranges",
                  instructions.size(), liveRanges.size());

  } catch (const std::exception &e) {
    spdlog::error("Register allocation failed: {}", e.what());

    // FALLBACK: Use simple allocation if sophisticated algorithm fails
    std::unordered_map<Register, uint8_t> regMap;
    uint8_t nextScratch = 0;
    for (auto &instr : instructions) {
      for (auto &operand : instr.operands) {
        if (operand.type == DecodedInstruction::Operand::Type::REGISTER &&
            regMap.find(operand.reg) == regMap.end()) {
          regMap[operand.reg] = nextScratch++;
          if (nextScratch > 3) {
            nextScratch = 0;
          }
        }
      }
    }
    spdlog::trace("Used fallback allocation for {} registers", regMap.size());
  }
}

/**
 * @brief Performs live range analysis on instructions.
 * @param instructions Instructions to analyze.
 * @param liveRanges Output live ranges.
 */
void X86_64JITCompiler::PerformLiveRangeAnalysis(
    const std::vector<DecodedInstruction> &instructions,
    std::vector<LiveRange> &liveRanges) {

  std::unordered_map<Register, LiveRange *> activeRanges;

  for (uint32_t i = 0; i < instructions.size(); ++i) {
    const auto &instr = instructions[i];

    // Process each operand
    for (const auto &operand : instr.operands) {
      if (operand.type != DecodedInstruction::Operand::Type::REGISTER) {
        continue;
      }

      Register reg = operand.reg;
      if (reg == Register::NONE) {
        continue;
      }

      // Check if this register already has an active range
      auto it = activeRanges.find(reg);
      if (it != activeRanges.end()) {
        // Extend existing range
        it->second->end = i;
      } else {
        // Create new live range
        LiveRange range;
        range.start = i;
        range.end = i;
        range.virtualReg = reg;
        range.physicalReg = 0xFF; // Unassigned
        range.isSpilled = false;
        range.spillSlot = 0;

        // Calculate priority based on loop depth and frequency
        // Higher priority for registers used in loops or frequently
        range.priority = 1.0f;
        if (i > 0 && i < instructions.size() - 1) {
          // Simple heuristic: higher priority for registers used multiple times
          uint32_t useCount = 0;
          for (uint32_t j = 0; j < instructions.size(); ++j) {
            for (const auto &op : instructions[j].operands) {
              if (op.type == DecodedInstruction::Operand::Type::REGISTER &&
                  op.reg == reg) {
                useCount++;
              }
            }
          }
          range.priority = static_cast<float>(useCount) / instructions.size();
        }

        liveRanges.push_back(range);
        activeRanges[reg] = &liveRanges.back();
      }
    }
  }

  // Sort live ranges by start time for linear scan algorithm
  std::sort(
      liveRanges.begin(), liveRanges.end(),
      [](const LiveRange &a, const LiveRange &b) { return a.start < b.start; });

  spdlog::trace("Live range analysis complete: {} ranges identified",
                liveRanges.size());
}

/**
 * @brief Performs linear scan register allocation.
 * @param liveRanges Live ranges to allocate.
 * @return True if allocation succeeded, false if spilling required.
 */
bool X86_64JITCompiler::LinearScanRegisterAllocation(
    std::vector<LiveRange> &liveRanges) {
  std::vector<LiveRange *> active;
  bool allAllocated = true;

  // Reserve some registers for special purposes
  constexpr uint8_t RESERVED_REGS = 4; // RSP, RBP, and 2 scratch registers
  constexpr uint8_t USABLE_REGS = NUM_PHYSICAL_REGS - RESERVED_REGS;

  for (auto &range : liveRanges) {
    // Remove expired ranges from active list
    active.erase(std::remove_if(active.begin(), active.end(),
                                [&range](const LiveRange *activeRange) {
                                  return activeRange->end < range.start;
                                }),
                 active.end());

    // Try to find an available register
    uint8_t allocatedReg = 0xFF;
    for (uint8_t reg = 0; reg < USABLE_REGS; ++reg) {
      bool regInUse = false;
      for (const auto *activeRange : active) {
        if (activeRange->physicalReg == reg) {
          regInUse = true;
          break;
        }
      }

      if (!regInUse) {
        allocatedReg = reg;
        break;
      }
    }

    if (allocatedReg != 0xFF) {
      // Successfully allocated register
      range.physicalReg = allocatedReg;
      range.isSpilled = false;
      active.push_back(&range);

      spdlog::trace("Allocated physical register {} to virtual register {} "
                    "(range {}-{})",
                    allocatedReg, static_cast<int>(range.virtualReg),
                    range.start, range.end);
    } else {
      // Need to spill - find the range with lowest priority or furthest end
      LiveRange *spillCandidate = nullptr;
      float lowestPriority = range.priority;

      for (auto *activeRange : active) {
        if (activeRange->priority < lowestPriority ||
            (activeRange->priority == lowestPriority &&
             activeRange->end > range.end)) {
          spillCandidate = activeRange;
          lowestPriority = activeRange->priority;
        }
      }

      if (spillCandidate) {
        // Spill the candidate and allocate its register to current range
        spillCandidate->isSpilled = true;
        spillCandidate->spillSlot = static_cast<uint32_t>(active.size());

        range.physicalReg = spillCandidate->physicalReg;
        range.isSpilled = false;

        // Remove spilled range from active and add current range
        active.erase(std::remove(active.begin(), active.end(), spillCandidate),
                     active.end());
        active.push_back(&range);

        spdlog::trace("Spilled virtual register {} to allocate register {} "
                      "to virtual register {}",
                      static_cast<int>(spillCandidate->virtualReg),
                      range.physicalReg, static_cast<int>(range.virtualReg));
        allAllocated = false;
      } else {
        // Spill current range
        range.isSpilled = true;
        range.spillSlot = static_cast<uint32_t>(active.size());
        range.physicalReg = 0xFF;

        spdlog::trace("Spilled virtual register {} (no suitable candidates)",
                      static_cast<int>(range.virtualReg));
        allAllocated = false;
      }
    }
  }

  return allAllocated;
}

/**
 * @brief Generates spill code for a register range.
 * @param range Live range that needs spilling.
 * @param code Output code vector.
 */
void X86_64JITCompiler::GenerateSpillCode(const LiveRange &range,
                                          std::vector<uint8_t> &code) {
  if (!range.isSpilled) {
    return;
  }

  try {
    // Calculate spill slot offset from stack pointer
    uint32_t spillOffset = range.spillSlot * 8; // 8 bytes per spill slot

    // Generate store instruction: MOV [RSP + offset], reg
    // MOV [RSP + disp32], r64 -> REX.W + 89 /r + disp32
    code.insert(code.end(),
                {
                    0x48, // REX.W prefix
                    0x89, // MOV r/m64, r64 opcode
                    0x84, // ModR/M: mod=10, reg=000, r/m=100 (RSP with disp32)
                    0x24  // SIB: scale=00, index=100 (none), base=100 (RSP)
                });

    // Add 32-bit displacement (little-endian)
    code.push_back(spillOffset & 0xFF);
    code.push_back((spillOffset >> 8) & 0xFF);
    code.push_back((spillOffset >> 16) & 0xFF);
    code.push_back((spillOffset >> 24) & 0xFF);

    spdlog::trace("Generated spill code for register {} at offset {}",
                  static_cast<int>(range.virtualReg), spillOffset);

  } catch (const std::exception &e) {
    spdlog::error("Failed to generate spill code: {}", e.what());
  }
}

/**
 * @brief Optimizes a compiled code block.
 * @param code Code vector to optimize.
 */
void X86_64JITCompiler::OptimizeBlock(std::vector<uint8_t> &code) {
  try {
    if (code.empty()) {
      spdlog::trace("OptimizeBlock: empty code block");
      return;
    }

    size_t originalSize = code.size();

    // ENHANCEMENT: Apply multiple optimization passes based on configuration
    if (m_tieredCompilationEnabled) {
      // Apply comprehensive optimizations
      DeadCodeElimination(code);
      ConstantFoldingAndPropagation(code);
      CommonSubexpressionElimination(code);
      InstructionCombining(code);
      LoopOptimizations(code);
      ControlFlowOptimizations(code);
    } else {
      // Apply basic peephole optimizations (original implementation)
      size_t i = 1;
      while (i < code.size()) {
        if (code[i] == 0x90 && code[i - 1] == 0x90) {
          // Remove consecutive NOPs
          code.erase(code.begin() + i);
        } else if (i >= 3 && code[i - 3] == 0x48 &&
                   code[i - 2] == 0x89 && // MOV reg, reg
                   code[i - 1] == code[i] && (code[i] & 0xC0) == 0xC0) {
          // Remove redundant MOV instructions
          code.erase(code.begin() + i - 3, code.begin() + i);
          i -= 3;
        } else {
          ++i;
        }
      }
    }

    size_t optimizedSize = code.size();
    float reductionPercent =
        originalSize > 0
            ? ((float)(originalSize - optimizedSize) / originalSize) * 100.0f
            : 0.0f;

    spdlog::trace("Optimized block: {} -> {} bytes ({:.1f}% reduction)",
                  originalSize, optimizedSize, reductionPercent);

  } catch (const std::exception &e) {
    spdlog::error("Block optimization failed: {}", e.what());
  }
}

/**
 * @brief Performs dead code elimination.
 * @param code Code vector to optimize.
 */
void X86_64JITCompiler::DeadCodeElimination(std::vector<uint8_t> &code) {
  try {
    // Simple dead code elimination - remove unreachable code after
    // unconditional jumps
    size_t i = 0;
    while (i < code.size() - 1) {
      // Check for unconditional jump instructions
      if ((code[i] == 0xEB) || // JMP rel8
          (code[i] == 0xE9) || // JMP rel32
          (code[i] == 0xC3)) { // RET

        // Find next label or end of block
        size_t nextLabel = i + 1;
        if (code[i] == 0xEB && i + 2 < code.size()) {
          nextLabel = i + 2; // Skip rel8
        } else if (code[i] == 0xE9 && i + 5 < code.size()) {
          nextLabel = i + 5; // Skip rel32
        }

        // Remove dead code between jump and next potential entry point
        while (nextLabel < code.size() && code[nextLabel] == 0x90) {
          code.erase(code.begin() + nextLabel);
        }
        i = nextLabel;
      } else {
        ++i;
      }
    }

    spdlog::trace("Dead code elimination completed");
  } catch (const std::exception &e) {
    spdlog::error("Dead code elimination failed: {}", e.what());
  }
}

/**
 * @brief Performs common subexpression elimination.
 * @param code Code vector to optimize.
 */
void X86_64JITCompiler::CommonSubexpressionElimination(
    std::vector<uint8_t> &code) {
  try {
    // Simple CSE for repeated load operations
    std::unordered_map<uint64_t, size_t> seenInstructions;

    size_t i = 0;
    while (i < code.size() - 3) {
      // Look for repeated MOV patterns
      if (code[i] == 0x48 && code[i + 1] == 0x8B) { // MOV r64, r/m64
        uint64_t instrPattern = 0;
        for (int j = 0; j < 4 && i + j < code.size(); ++j) {
          instrPattern |= (static_cast<uint64_t>(code[i + j]) << (j * 8));
        }

        auto it = seenInstructions.find(instrPattern);
        if (it != seenInstructions.end() && (i - it->second) < 32) {
          // Replace with NOP if instruction was seen recently
          for (int j = 0; j < 4 && i + j < code.size(); ++j) {
            code[i + j] = 0x90; // NOP
          }
          spdlog::trace("Eliminated common subexpression at offset {}", i);
        } else {
          seenInstructions[instrPattern] = i;
        }
      }
      ++i;
    }

    spdlog::trace("Common subexpression elimination completed");
  } catch (const std::exception &e) {
    spdlog::error("Common subexpression elimination failed: {}", e.what());
  }
}

/**
 * @brief Performs constant folding and propagation.
 * @param code Code vector to optimize.
 */
void X86_64JITCompiler::ConstantFoldingAndPropagation(
    std::vector<uint8_t> &code) {
  try {
    // Simple constant folding for immediate arithmetic operations
    size_t i = 0;
    while (i < code.size() - 6) {
      // Look for ADD/SUB with immediate zero
      if ((code[i] == 0x48 && code[i + 1] == 0x83 && code[i + 2] == 0xC0 &&
           code[i + 3] == 0x00) || // ADD RAX, 0
          (code[i] == 0x48 && code[i + 1] == 0x83 && code[i + 2] == 0xE8 &&
           code[i + 3] == 0x00)) { // SUB RAX, 0

        // Replace with NOPs
        for (int j = 0; j < 4; ++j) {
          code[i + j] = 0x90;
        }
        spdlog::trace("Folded constant operation at offset {}", i);
      }
      // Look for MUL by 1 or 0
      else if (code[i] == 0x48 && code[i + 1] == 0x6B &&
               code[i + 3] == 0x01) { // IMUL r64, r/m64, 1
        // Replace MUL by 1 with MOV (if source and dest are different)
        code[i + 1] = 0x89; // Change to MOV
        code[i + 3] = 0x90; // NOP the immediate
        spdlog::trace("Optimized multiply by 1 at offset {}", i);
      }
      ++i;
    }

    spdlog::trace("Constant folding and propagation completed");
  } catch (const std::exception &e) {
    spdlog::error("Constant folding and propagation failed: {}", e.what());
  }
}

/**
 * @brief Performs instruction combining and strength reduction.
 * @param code Code vector to optimize.
 */
void X86_64JITCompiler::InstructionCombining(std::vector<uint8_t> &code) {
  try {
    size_t i = 0;
    while (i < code.size() - 8) {
      // Combine LEA + ADD into single LEA with larger displacement
      if (code[i] == 0x48 && code[i + 1] == 0x8D && // LEA r64, [r64 + disp]
          i + 7 < code.size() && code[i + 7] == 0x48 &&
          code[i + 8] == 0x83) { // ADD r64, imm8

        // Check if the registers match
        if ((code[i + 2] & 0x38) == (code[i + 9] & 0x38)) {
          // Combine the displacements
          int32_t leaDisp = *reinterpret_cast<int32_t *>(&code[i + 3]);
          int8_t addImm = static_cast<int8_t>(code[i + 10]);
          int32_t combinedDisp = leaDisp + addImm;

          // Update LEA displacement
          *reinterpret_cast<int32_t *>(&code[i + 3]) = combinedDisp;

          // Replace ADD with NOPs
          for (int j = 7; j < 11 && i + j < code.size(); ++j) {
            code[i + j] = 0x90;
          }

          spdlog::trace("Combined LEA+ADD at offset {}", i);
        }
      }

      // Strength reduction: MUL by power of 2 -> SHL
      else if (code[i] == 0x48 &&
               code[i + 1] == 0x6B) { // IMUL r64, r/m64, imm8
        int8_t multiplier = static_cast<int8_t>(code[i + 3]);
        if (multiplier > 0 && (multiplier & (multiplier - 1)) == 0) {
          // Power of 2, convert to shift
          uint8_t shiftAmount = 0;
          int8_t temp = multiplier;
          while (temp > 1) {
            temp >>= 1;
            shiftAmount++;
          }

          if (shiftAmount > 0 && shiftAmount < 64) {
            code[i + 1] = 0xC1; // SHL opcode
            code[i + 3] = shiftAmount;
            spdlog::trace("Converted MUL by {} to SHL by {} at offset {}",
                          multiplier, shiftAmount, i);
          }
        }
      }

      ++i;
    }

    spdlog::trace("Instruction combining completed");
  } catch (const std::exception &e) {
    spdlog::error("Instruction combining failed: {}", e.what());
  }
}

/**
 * @brief Performs loop optimizations.
 * @param code Code vector to optimize.
 */
void X86_64JITCompiler::LoopOptimizations(std::vector<uint8_t> &code) {
  try {
    // Simple loop optimization - unroll small loops
    size_t i = 0;
    while (i < code.size() - 8) {
      // Look for simple counting loops (DEC + JNZ pattern)
      if (code[i] == 0x48 && code[i + 1] == 0xFF &&     // DEC r64
          i + 4 < code.size() && code[i + 4] == 0x75) { // JNZ rel8

        int8_t jumpOffset = static_cast<int8_t>(code[i + 5]);

        // Simple heuristic: if jump goes back only a few instructions, consider
        // unrolling
        if (jumpOffset < 0 && jumpOffset > -16) {
          size_t loopStart = i + 6 + jumpOffset;
          size_t loopSize = i - loopStart;

          // Only unroll very small loops (< 8 bytes)
          if (loopSize > 0 && loopSize < 8) {
            // Replace JNZ with NOP and duplicate loop body once
            code[i + 4] = 0x90; // NOP
            code[i + 5] = 0x90; // NOP

            // Insert loop body copy before the DEC
            std::vector<uint8_t> loopBody(code.begin() + loopStart,
                                          code.begin() + i);
            code.insert(code.begin() + i, loopBody.begin(), loopBody.end());

            spdlog::trace("Unrolled small loop at offset {}", loopStart);
            i += loopSize; // Skip over the duplicated code
          }
        }
      }
      ++i;
    }

    spdlog::trace("Loop optimizations completed");
  } catch (const std::exception &e) {
    spdlog::error("Loop optimizations failed: {}", e.what());
  }
}

/**
 * @brief Performs control flow optimizations.
 * @param code Code vector to optimize.
 */
void X86_64JITCompiler::ControlFlowOptimizations(std::vector<uint8_t> &code) {
  try {
    size_t i = 0;
    while (i < code.size() - 2) {
      // Optimize conditional jumps over unconditional jumps
      if (code[i] >= 0x70 && code[i] <= 0x7F && // Conditional jump (Jcc rel8)
          i + 2 < code.size() && code[i + 2] == 0xEB) { // Followed by JMP rel8

        // Invert the condition and make it jump to the JMP target
        uint8_t invertedCondition = code[i] ^ 0x01; // Flip the condition bit
        int8_t jmpOffset = static_cast<int8_t>(code[i + 3]);
        int8_t newOffset = static_cast<int8_t>(code[i + 1]) + 2 + jmpOffset;

        if (newOffset >= -128 && newOffset <= 127) {
          code[i] = invertedCondition;
          code[i + 1] = static_cast<uint8_t>(newOffset);
          code[i + 2] = 0x90; // NOP the JMP
          code[i + 3] = 0x90; // NOP the JMP offset

          spdlog::trace("Optimized conditional jump chain at offset {}", i);
        }
      }

      // Remove redundant jumps to next instruction
      else if (code[i] == 0xEB && code[i + 1] == 0x00) { // JMP +0
        code[i] = 0x90;                                  // NOP
        code[i + 1] = 0x90;                              // NOP
        spdlog::trace("Removed redundant jump at offset {}", i);
      }

      ++i;
    }

    spdlog::trace("Control flow optimizations completed");
  } catch (const std::exception &e) {
    spdlog::error("Control flow optimizations failed: {}", e.what());
  }
}

/**
 * @brief Fixes up relative addresses in compiled code.
 * @param executableCode Pointer to executable memory.
 * @param codeSize Size of the code.
 */
void X86_64JITCompiler::FixupRelativeAddresses(uint8_t *executableCode,
                                               size_t codeSize) {
  try {
    size_t fixupsApplied = 0;

    // CRITICAL FIX: Improved relative addressing calculation for CALL
    // instructions Scan through the code looking for CALL instructions (0xE8)
    // that need fixup
    for (size_t i = 0; i < codeSize - 4; ++i) {
      if (executableCode[i] == 0xE8) {
        // Found a CALL instruction, check if it needs fixup
        // The next 4 bytes contain either a relative offset or a helper
        // function pointer
        void **helperPtr = reinterpret_cast<void **>(&executableCode[i + 1]);
        void *helper = *helperPtr;

        // Check if this looks like a function pointer (high address)
        uintptr_t helperAddr = reinterpret_cast<uintptr_t>(helper);

        // CRITICAL FIX: Better detection of function pointers vs relative
        // offsets Function pointers are typically in high memory (> 64KB) and
        // aligned. Also check for valid code segment addresses.
        if (helperAddr > 0x10000 && (helperAddr & 0x3) == 0) {
          // Calculate the correct relative offset
          // The call site is the address immediately after the CALL instruction
          // (i + 5) - this is where RIP will point when the call executes
          uint8_t *callSite = &executableCode[i + 5];
          uintptr_t callSiteAddr = reinterpret_cast<uintptr_t>(callSite);

          // Calculate the relative offset (signed 32-bit)
          // This is: target_address - next_instruction_address
          int64_t offset64 = static_cast<int64_t>(helperAddr) -
                             static_cast<int64_t>(callSiteAddr);

          // Check if the offset fits in a 32-bit signed integer
          if (offset64 >= INT32_MIN && offset64 <= INT32_MAX) {
            int32_t relativeOffset = static_cast<int32_t>(offset64);

            // Write the correct relative offset in little-endian format
            *reinterpret_cast<int32_t *>(&executableCode[i + 1]) =
                relativeOffset;

            fixupsApplied++;
            spdlog::trace("Fixed up CALL at offset {}: helper=0x{:x}, "
                          "callSite=0x{:x}, relative=0x{:x}",
                          i, helperAddr, callSiteAddr,
                          static_cast<uint32_t>(relativeOffset));
          } else {
            spdlog::error("CALL instruction at offset {} has target 0x{:x} "
                          "that is too far away "
                          "(offset=0x{:x}, max=0x{:x})",
                          i, helperAddr, static_cast<uint64_t>(offset64),
                          static_cast<uint64_t>(INT32_MAX));
            // For targets that are too far, we could implement a trampoline
            // but for now, we'll leave it as is and let it fail at runtime
          }
        }
      }
    }

    spdlog::info(
        "Completed relative address fixup for {} bytes, applied {} fixups",
        codeSize, fixupsApplied);
  } catch (const std::exception &e) {
    spdlog::error("Relative address fixup failed: {}", e.what());
  }
}

/**
 * @brief Saves the JIT cache state to a stream.
 * @param out Output stream.
 */
void X86_64JITCompiler::SaveState(std::ostream &out) const {
  std::lock_guard<std::mutex> lock(m_cacheMutex);
  try {
    // CRITICAL FIX: Add stream validation before writing
    if (!out.good()) {
      spdlog::error("JIT SaveState: output stream is not in good state");
      return;
    }

    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    if (!out.good()) {
      spdlog::error("JIT SaveState: failed to write version");
      return;
    }

    out.write(reinterpret_cast<const char *>(&m_totalCodeSize),
              sizeof(m_totalCodeSize));
    if (!out.good()) {
      spdlog::error("JIT SaveState: failed to write total code size");
      return;
    }

    out.write(reinterpret_cast<const char *>(&m_cycleCount),
              sizeof(m_cycleCount));
    if (!out.good()) {
      spdlog::error("JIT SaveState: failed to write cycle count");
      return;
    }

    out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));
    if (!out.good()) {
      spdlog::error("JIT SaveState: failed to write stats");
      return;
    }

    uint64_t blockCount = m_compiledBlocks.size();
    out.write(reinterpret_cast<const char *>(&blockCount), sizeof(blockCount));
    if (!out.good()) {
      spdlog::error("JIT SaveState: failed to write block count");
      return;
    }
    for (const auto &[pc, info] : m_compiledBlocks) {
      out.write(reinterpret_cast<const char *>(&pc), sizeof(pc));
      out.write(reinterpret_cast<const char *>(&info.size), sizeof(info.size));
      out.write(reinterpret_cast<const char *>(&info.lastUsed),
                sizeof(info.lastUsed));
      out.write(reinterpret_cast<const char *>(&info.branchCount),
                sizeof(info.branchCount));
      out.write(reinterpret_cast<const char *>(&info.executionCount),
                sizeof(info.executionCount));
      out.write(reinterpret_cast<const char *>(&info.totalCycles),
                sizeof(info.totalCycles));
      out.write(reinterpret_cast<const char *>(&info.isHotPath),
                sizeof(info.isHotPath));
      out.write(reinterpret_cast<const char *>(&info.compilationTimeUs),
                sizeof(info.compilationTimeUs));
      out.write(reinterpret_cast<const char *>(info.code), info.size);
    }
    spdlog::info("JIT cache state saved: {} blocks", blockCount);
  } catch (const std::exception &e) {
    spdlog::error("JIT SaveState failed: {}", e.what());
  }
}

/**
 * @brief Loads the JIT cache state from a stream.
 * @param in Input stream.
 */
void X86_64JITCompiler::LoadState(std::istream &in) {
  std::lock_guard<std::mutex> lock(m_cacheMutex);
  try {
    // CRITICAL FIX: Add stream validation before reading
    if (!in.good()) {
      spdlog::error("JIT LoadState: input stream is not in good state");
      return;
    }

    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (!in.good()) {
      spdlog::error("JIT LoadState: failed to read version");
      return;
    }
    if (version != 1) {
      spdlog::error("Unsupported JIT cache state version: {}", version);
      throw JITCompileException("Invalid JIT cache state version");
    }

    ClearCache();

    in.read(reinterpret_cast<char *>(&m_totalCodeSize),
            sizeof(m_totalCodeSize));
    if (!in.good()) {
      spdlog::error("JIT LoadState: failed to read total code size");
      return;
    }

    in.read(reinterpret_cast<char *>(&m_cycleCount), sizeof(m_cycleCount));
    if (!in.good()) {
      spdlog::error("JIT LoadState: failed to read cycle count");
      return;
    }

    in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));
    if (!in.good()) {
      spdlog::error("JIT LoadState: failed to read stats");
      return;
    }

    uint64_t blockCount;
    in.read(reinterpret_cast<char *>(&blockCount), sizeof(blockCount));
    if (!in.good()) {
      spdlog::error("JIT LoadState: failed to read block count");
      return;
    }

    // CRITICAL FIX: Add sanity check for block count
    if (blockCount > 100000) { // Reasonable upper limit
      spdlog::error("JIT LoadState: suspicious block count {}, aborting",
                    blockCount);
      ClearCache();
      return;
    }
    for (uint64_t i = 0; i < blockCount && in.good(); ++i) {
      uint64_t pc;
      CompiledBlockInfo info;
      in.read(reinterpret_cast<char *>(&pc), sizeof(pc));
      in.read(reinterpret_cast<char *>(&info.size), sizeof(info.size));
      in.read(reinterpret_cast<char *>(&info.lastUsed), sizeof(info.lastUsed));
      in.read(reinterpret_cast<char *>(&info.branchCount),
              sizeof(info.branchCount));
      in.read(reinterpret_cast<char *>(&info.executionCount),
              sizeof(info.executionCount));
      in.read(reinterpret_cast<char *>(&info.totalCycles),
              sizeof(info.totalCycles));
      in.read(reinterpret_cast<char *>(&info.isHotPath),
              sizeof(info.isHotPath));
      in.read(reinterpret_cast<char *>(&info.compilationTimeUs),
              sizeof(info.compilationTimeUs));
      info.code = AllocateExecutableMemory(info.size);
      in.read(reinterpret_cast<char *>(info.code), info.size);
      m_compiledBlocks[pc] = info;
    }
    spdlog::info("JIT cache state loaded: {} blocks", blockCount);
  } catch (const std::exception &e) {
    spdlog::error("JIT LoadState failed: {}", e.what());
    ClearCache();
  }
}

/**
 * @brief Determines if optimization should be used for a block.
 * @param pc Program counter of the block.
 * @param level Optimization level to check.
 * @return True if optimization should be applied.
 */
bool X86_64JITCompiler::ShouldUseOptimization(uint64_t pc,
                                              OptimizationLevel level) {
  auto it = m_compiledBlocks.find(pc);
  if (it == m_compiledBlocks.end()) {
    return level == OptimizationLevel::Basic; // Default to basic for new blocks
  }

  const CompiledBlockInfo &info = it->second;

  // Apply optimization based on execution frequency and block characteristics
  switch (level) {
  case OptimizationLevel::None:
    return false;

  case OptimizationLevel::Basic:
    return true; // Always apply basic optimizations

  case OptimizationLevel::Aggressive:
    return info.executionCount > 100 || info.isHotPath;
  case OptimizationLevel::Maximum:
    return info.executionCount > 1000 && info.isHotPath &&
           (info.totalCycles /
            (std::max)(1u, static_cast<uint32_t>(info.executionCount))) > 50;

  default:
    return false;
  }
}

/**
 * @brief Determines the optimization level for a block.
 * @param pc Program counter of the block.
 * @return Appropriate optimization level.
 */
X86_64JITCompiler::OptimizationLevel
X86_64JITCompiler::DetermineOptimizationLevel(uint64_t pc) {
  auto it = m_compiledBlocks.find(pc);
  if (it == m_compiledBlocks.end()) {
    return OptimizationLevel::Basic;
  }

  const CompiledBlockInfo &info = it->second;

  // Tiered compilation strategy
  if (info.executionCount < 10) {
    return OptimizationLevel::Basic;
  } else if (info.executionCount < 100) {
    return OptimizationLevel::Aggressive;
  } else if (info.isHotPath && info.executionCount > 500) {
    return OptimizationLevel::Maximum;
  } else {
    return OptimizationLevel::Aggressive;
  }
}

/**
 * @brief Applies optimization passes based on the specified level.
 * @param code Code vector to optimize.
 * @param level Optimization level to apply.
 */
void X86_64JITCompiler::ApplyOptimizationPasses(std::vector<uint8_t> &code,
                                                OptimizationLevel level) {
  try {
    switch (level) {
    case OptimizationLevel::None:
      // No optimizations
      break;

    case OptimizationLevel::Basic:
      // Basic peephole optimizations
      DeadCodeElimination(code);
      ConstantFoldingAndPropagation(code);
      break;

    case OptimizationLevel::Aggressive:
      // More comprehensive optimizations
      DeadCodeElimination(code);
      ConstantFoldingAndPropagation(code);
      CommonSubexpressionElimination(code);
      InstructionCombining(code);
      break;

    case OptimizationLevel::Maximum:
      // All optimizations including expensive ones
      DeadCodeElimination(code);
      ConstantFoldingAndPropagation(code);
      CommonSubexpressionElimination(code);
      InstructionCombining(code);
      LoopOptimizations(code);
      ControlFlowOptimizations(code);
      break;
    }

    spdlog::trace("Applied optimization level {} to {} byte block",
                  static_cast<int>(level), code.size());

  } catch (const std::exception &e) {
    spdlog::error("Optimization pass failed: {}", e.what());
  }
}

/**
 * @brief Enhanced instruction emission dispatcher.
 * @param instr Instruction to emit.
 * @param code Output code vector.
 */
void X86_64JITCompiler::EmitInstruction(const DecodedInstruction &instr,
                                        std::vector<uint8_t> &code) {
  try {
    switch (instr.instType) {
    // Arithmetic instructions
    case InstructionType::Add:
    case InstructionType::Sub:
    case InstructionType::Mul:
    case InstructionType::Div:
    case InstructionType::And:
    case InstructionType::Or:
    case InstructionType::Xor:
      EmitArithmeticInstruction(instr, code);
      break;

    // Memory instructions
    case InstructionType::Mov:
    case InstructionType::Lea:
    case InstructionType::Push:
    case InstructionType::Pop:
      EmitMemoryInstruction(instr, code);
      break; // Control flow instructions
    case InstructionType::Jump:
    case InstructionType::Call:
    case InstructionType::Ret:
      EmitControlFlowInstruction(instr, code);
      break;

    // SIMD instructions
    case InstructionType::Addps:
    case InstructionType::Subps:
    case InstructionType::Mulps:
    case InstructionType::Movaps:
    case InstructionType::Movups:
      EmitSIMDInstruction(instr, code);
      break;

    default:
      spdlog::warn("Unsupported instruction type: {}",
                   static_cast<int>(instr.instType));
      // Fallback to helper function call
      code.insert(code.end(),
                  {0xE8, 0x00, 0x00, 0x00, 0x00}); // CALL placeholder
      break;
    }

  } catch (const std::exception &e) {
    spdlog::error("Instruction emission failed: {}", e.what());
  }
}

/**
 * @brief Emits arithmetic instructions.
 * @param instr Arithmetic instruction.
 * @param code Output code vector.
 */
void X86_64JITCompiler::EmitArithmeticInstruction(
    const DecodedInstruction &instr, std::vector<uint8_t> &code) {
  if (instr.operandCount < 2) {
    spdlog::error("Arithmetic instruction requires at least 2 operands");
    return;
  }

  try {
    uint8_t opcode = 0x00;
    bool needsREX = false;

    // Determine opcode based on instruction type
    switch (instr.instType) {
    case InstructionType::Add:
      opcode = 0x01; // ADD r/m64, r64
      needsREX = true;
      break;
    case InstructionType::Sub:
      opcode = 0x29; // SUB r/m64, r64
      needsREX = true;
      break;
    case InstructionType::And:
      opcode = 0x21; // AND r/m64, r64
      needsREX = true;
      break;
    case InstructionType::Or:
      opcode = 0x09; // OR r/m64, r64
      needsREX = true;
      break;
    case InstructionType::Xor:
      opcode = 0x31; // XOR r/m64, r64
      needsREX = true;
      break;
    default:
      spdlog::warn("Unsupported arithmetic instruction: {}",
                   static_cast<int>(instr.instType));
      return;
    }

    // Emit REX prefix for 64-bit operations
    if (needsREX) {
      code.push_back(0x48); // REX.W
    }

    // Emit opcode
    code.push_back(opcode);

    // Emit ModR/M and addressing mode
    const auto &dest = instr.operands[0];
    const auto &src = instr.operands[1];

    if (dest.type == DecodedInstruction::Operand::Type::REGISTER &&
        src.type == DecodedInstruction::Operand::Type::REGISTER) {
      // Register to register operation
      uint8_t modrm = 0xC0 | (static_cast<uint8_t>(src.reg) << 3) |
                      static_cast<uint8_t>(dest.reg);
      code.push_back(modrm);
    } else {
      // Handle memory operands
      EmitAddressingMode(dest, static_cast<uint8_t>(src.reg), code);
    }

    spdlog::trace("Emitted arithmetic instruction: opcode=0x{:02X}", opcode);

  } catch (const std::exception &e) {
    spdlog::error("Arithmetic instruction emission failed: {}", e.what());
  }
}

/**
 * @brief Emits memory instructions.
 * @param instr Memory instruction.
 * @param code Output code vector.
 */
void X86_64JITCompiler::EmitMemoryInstruction(const DecodedInstruction &instr,
                                              std::vector<uint8_t> &code) {
  try {
    switch (instr.instType) {
    case InstructionType::Mov: {
      if (instr.operandCount < 2) {
        spdlog::error("MOV instruction requires 2 operands");
        return;
      }

      const auto &dest = instr.operands[0];
      const auto &src = instr.operands[1];

      // REX prefix for 64-bit operation
      code.push_back(0x48);

      if (src.type == DecodedInstruction::Operand::Type::IMMEDIATE) {
        // MOV r64, imm64
        code.push_back(0xB8 + static_cast<uint8_t>(dest.reg)); // MOV reg, imm64
        EmitImmediate(src.immediate, 8, code);
      } else {
        // MOV r64, r/m64
        code.push_back(0x8B);
        uint8_t modrm = 0xC0 | (static_cast<uint8_t>(dest.reg) << 3) |
                        static_cast<uint8_t>(src.reg);
        code.push_back(modrm);
      }
      break;
    }

    case InstructionType::Lea: {
      if (instr.operandCount < 2) {
        spdlog::error("LEA instruction requires 2 operands");
        return;
      }

      code.push_back(0x48); // REX.W
      code.push_back(0x8D); // LEA opcode

      const auto &dest = instr.operands[0];
      const auto &src = instr.operands[1];
      EmitAddressingMode(src, static_cast<uint8_t>(dest.reg), code);
      break;
    }
    case InstructionType::Push: {
      if (instr.operandCount < 1) {
        spdlog::error("PUSH instruction requires 1 operand");
        return;
      }

      const auto &src = instr.operands[0];
      if (src.type == DecodedInstruction::Operand::Type::REGISTER) {
        code.push_back(0x50 + static_cast<uint8_t>(src.reg)); // PUSH r64
      } else if (src.type == DecodedInstruction::Operand::Type::IMMEDIATE) {
        code.push_back(0x68); // PUSH imm32
        EmitImmediate(src.immediate, 4, code);
      }
      break;
    }
    case InstructionType::Pop: {
      if (instr.operandCount < 1) {
        spdlog::error("POP instruction requires 1 operand");
        return;
      }

      const auto &dest = instr.operands[0];
      if (dest.type == DecodedInstruction::Operand::Type::REGISTER) {
        code.push_back(0x58 + static_cast<uint8_t>(dest.reg)); // POP r64
      }
      break;
    }

    default:
      spdlog::warn("Unsupported memory instruction: {}",
                   static_cast<int>(instr.instType));
      break;
    }

    spdlog::trace("Emitted memory instruction: {}",
                  static_cast<int>(instr.instType));

  } catch (const std::exception &e) {
    spdlog::error("Memory instruction emission failed: {}", e.what());
  }
}

/**
 * @brief Emits control flow instructions.
 * @param instr Control flow instruction.
 * @param code Output code vector.
 */
void X86_64JITCompiler::EmitControlFlowInstruction(
    const DecodedInstruction &instr, std::vector<uint8_t> &code) {
  try {
    switch (instr.instType) {
    case InstructionType::Jump: {
      if (instr.operandCount < 1) {
        spdlog::error("JMP instruction requires 1 operand");
        return;
      }

      const auto &target = instr.operands[0];
      if (target.type == DecodedInstruction::Operand::Type::IMMEDIATE) {
        int64_t offset = static_cast<int64_t>(target.immediate);
        if (offset >= -128 && offset <= 127) {
          code.push_back(0xEB); // JMP rel8
          code.push_back(static_cast<uint8_t>(offset));
        } else {
          code.push_back(0xE9); // JMP rel32
          EmitImmediate(static_cast<uint64_t>(offset), 4, code);
        }
      }
      break;
    }
    case InstructionType::Call: {
      if (instr.operandCount < 1) {
        spdlog::error("CALL instruction requires 1 operand");
        return;
      }

      code.push_back(0xE8); // CALL rel32
      // Placeholder for relative offset (will be fixed up later)
      code.insert(code.end(), {0x00, 0x00, 0x00, 0x00});
      break;
    }

    case InstructionType::Ret: {
      code.push_back(0xC3); // RET
      break;
    }

    default:
      spdlog::warn("Unsupported control flow instruction: {}",
                   static_cast<int>(instr.instType));
      break;
    }

    spdlog::trace("Emitted control flow instruction: {}",
                  static_cast<int>(instr.instType));

  } catch (const std::exception &e) {
    spdlog::error("Control flow instruction emission failed: {}", e.what());
  }
}

/**
 * @brief Emits SIMD instructions with native code generation when possible.
 * @param instr SIMD instruction.
 * @param code Output code vector.
 */
void X86_64JITCompiler::EmitSIMDInstruction(const DecodedInstruction &instr,
                                            std::vector<uint8_t> &code) {
  try {
    if (!m_simdOptimizationsEnabled) {
      // Fall back to original helper function approach
      CompileSIMD(instr, code);
      return;
    }

    // Emit native SIMD instructions when possible
    switch (instr.instType) {
    case InstructionType::Addps: {
      // ADDPS xmm1, xmm2/m128 -> 0F 58 /r
      code.insert(code.end(), {0x0F, 0x58});
      if (instr.operandCount >= 2) {
        uint8_t modrm = 0xC0 |
                        (static_cast<uint8_t>(instr.operands[0].reg) << 3) |
                        static_cast<uint8_t>(instr.operands[1].reg);
        code.push_back(modrm);
      }
      break;
    }
    case InstructionType::Subps: {
      // SUBPS xmm1, xmm2/m128 -> 0F 5C /r
      code.insert(code.end(), {0x0F, 0x5C});
      if (instr.operandCount >= 2) {
        uint8_t modrm = 0xC0 |
                        (static_cast<uint8_t>(instr.operands[0].reg) << 3) |
                        static_cast<uint8_t>(instr.operands[1].reg);
        code.push_back(modrm);
      }
      break;
    }
    case InstructionType::Mulps: {
      // MULPS xmm1, xmm2/m128 -> 0F 59 /r
      code.insert(code.end(), {0x0F, 0x59});
      if (instr.operandCount >= 2) {
        uint8_t modrm = 0xC0 |
                        (static_cast<uint8_t>(instr.operands[0].reg) << 3) |
                        static_cast<uint8_t>(instr.operands[1].reg);
        code.push_back(modrm);
      }
      break;
    }

    case InstructionType::Movaps: {
      // MOVAPS xmm1, xmm2/m128 -> 0F 28 /r      code.insert(code.end(), {0x0F,
      // 0x28});
      if (instr.operandCount >= 2) {
        uint8_t modrm = 0xC0 |
                        (static_cast<uint8_t>(instr.operands[0].reg) << 3) |
                        static_cast<uint8_t>(instr.operands[1].reg);
        code.push_back(modrm);
      }
      break;
    }
    case InstructionType::Movups: {
      // MOVUPS xmm1, xmm2/m128 -> 0F 10 /r
      code.insert(code.end(), {0x0F, 0x10});
      if (instr.operandCount >= 2) {
        uint8_t modrm = 0xC0 |
                        (static_cast<uint8_t>(instr.operands[0].reg) << 3) |
                        static_cast<uint8_t>(instr.operands[1].reg);
        code.push_back(modrm);
      }
      break;
    }

    default:
      // Fall back to helper function for unsupported SIMD instructions
      CompileSIMD(instr, code);
      break;
    }

    spdlog::trace("Emitted native SIMD instruction: {}",
                  static_cast<int>(instr.instType));

  } catch (const std::exception &e) {
    spdlog::error("SIMD instruction emission failed: {}", e.what());
    // Fall back to helper function approach
    CompileSIMD(instr, code);
  }
}

/**
 * @brief Emits addressing mode encoding for memory operands.
 * @param operand Memory operand.
 * @param regField Register field for ModR/M byte.
 * @param code Output code vector.
 */
void X86_64JITCompiler::EmitAddressingMode(
    const DecodedInstruction::Operand &operand, uint8_t regField,
    std::vector<uint8_t> &code) {
  try {
    if (operand.type == DecodedInstruction::Operand::Type::MEMORY) {
      if (operand.memory.base != Register::NONE) {
        // Base + displacement addressing
        uint8_t baseReg = static_cast<uint8_t>(operand.memory.base);

        if (operand.memory.displacement == 0) {
          // [base] - no displacement
          uint8_t modrm = (regField << 3) | baseReg;
          code.push_back(modrm);
        } else if (operand.memory.displacement >= -128 &&
                   operand.memory.displacement <= 127) {
          // [base + disp8]
          uint8_t modrm = 0x40 | (regField << 3) | baseReg;
          code.push_back(modrm);
          code.push_back(static_cast<uint8_t>(operand.memory.displacement));
        } else {
          // [base + disp32]
          uint8_t modrm = 0x80 | (regField << 3) | baseReg;
          code.push_back(modrm);
          EmitImmediate(static_cast<uint64_t>(operand.memory.displacement), 4,
                        code);
        }

        // Handle index register (SIB byte) if present
        if (operand.memory.index != Register::NONE) {
          uint8_t indexReg = static_cast<uint8_t>(operand.memory.index);
          uint8_t scale = operand.memory.scale;
          uint8_t scaleField = 0;

          switch (scale) {
          case 1:
            scaleField = 0;
            break;
          case 2:
            scaleField = 1;
            break;
          case 4:
            scaleField = 2;
            break;
          case 8:
            scaleField = 3;
            break;
          default:
            scaleField = 0;
            break;
          }

          uint8_t sib = (scaleField << 6) | (indexReg << 3) | baseReg;
          code.push_back(sib);
        }
      } else {
        // Absolute addressing [disp32]
        uint8_t modrm = (regField << 3) | 0x05; // ModR/M for disp32
        code.push_back(modrm);
        EmitImmediate(static_cast<uint64_t>(operand.memory.displacement), 4,
                      code);
      }
    } else if (operand.type == DecodedInstruction::Operand::Type::REGISTER) {
      // Register addressing
      uint8_t modrm =
          0xC0 | (regField << 3) | static_cast<uint8_t>(operand.reg);
      code.push_back(modrm);
    }

  } catch (const std::exception &e) {
    spdlog::error("Addressing mode emission failed: {}", e.what());
  }
}

/**
 * @brief Emits immediate values in little-endian format.
 * @param immediate Immediate value.
 * @param size Size in bytes (1, 2, 4, or 8).
 * @param code Output code vector.
 */
void X86_64JITCompiler::EmitImmediate(uint64_t immediate, uint8_t size,
                                      std::vector<uint8_t> &code) {
  try {
    switch (size) {
    case 1:
      code.push_back(static_cast<uint8_t>(immediate));
      break;
    case 2:
      code.push_back(static_cast<uint8_t>(immediate));
      code.push_back(static_cast<uint8_t>(immediate >> 8));
      break;
    case 4:
      for (int i = 0; i < 4; ++i) {
        code.push_back(static_cast<uint8_t>(immediate >> (i * 8)));
      }
      break;
    case 8:
      for (int i = 0; i < 8; ++i) {
        code.push_back(static_cast<uint8_t>(immediate >> (i * 8)));
      }
      break;
    default:
      spdlog::error("Invalid immediate size: {}", size);
      break;
    }

  } catch (const std::exception &e) {
    spdlog::error("Immediate emission failed: {}", e.what());
  }
}
} // namespace x86_64