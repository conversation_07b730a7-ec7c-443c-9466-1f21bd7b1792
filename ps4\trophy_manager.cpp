// Copyright 2025 <Copyright Owner>

#include "trophy_manager.h"
#include <chrono>
#include <filesystem>
#include <fstream>
#include <pugixml.hpp>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <string>
#include <vector>

// Helper functions to convert strings to enums
static ps4::TrophyGrade StringToTrophyGrade(const std::string &str) {
  if (str == "BRONZE")
    return ps4::TrophyGrade::BRONZE;
  if (str == "SILVER")
    return ps4::TrophyGrade::SILVER;
  if (str == "GOLD")
    return ps4::TrophyGrade::GOLD;
  if (str == "PLATINUM")
    return ps4::TrophyGrade::PLATINUM;
  return ps4::TrophyGrade::BRONZE;
}

static ps4::TrophyType StringToTrophyType(const std::string &str) {
  if (str == "HIDDEN")
    return ps4::TrophyType::HIDDEN;
  return ps4::TrophyType::NORMAL;
}

namespace ps4 {
static TrophyManager g_trophyManager;

/**
 * @brief Constructs the trophy manager.
 */
TrophyManager::TrophyManager() {
  auto start = std::chrono::steady_clock::now();
  m_stats = TrophyStats();
  spdlog::info("TrophyManager constructed");
  auto end = std::chrono::steady_clock::now();
  m_stats.saveLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Destructor, cleaning up resources.
 */
TrophyManager::~TrophyManager() {
  auto start = std::chrono::steady_clock::now();
  Shutdown();
  spdlog::info("TrophyManager destroyed");
  auto end = std::chrono::steady_clock::now();
  m_stats.saveLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Initializes the trophy manager.
 * @param userId User ID for trophy data.
 * @return True on success, false otherwise.
 */
bool TrophyManager::Initialize(const std::string &userId) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_trophyMutex);
  try {
    m_userId = userId;
    m_stats = TrophyStats();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.saveLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("TrophyManager initialized for user {}, latency={}us", userId,
                 m_stats.saveLatencyUs);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("TrophyManager initialization failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Loads a trophy set from an XML file.
 * @param path XML file path.
 * @param outSet Output trophy set.
 * @return True on success, false otherwise.
 */
bool TrophyManager::LoadTrophySetFromXml(const std::string &path,
                                         TrophySet &outSet) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_trophyMutex);
  try {
    pugi::xml_document doc;
    lock.unlock();
    auto result = doc.load_file(path.c_str());
    lock.lock();
    if (!result) {
      spdlog::error("Failed to load trophy XML: {}", result.description());
      m_stats.cacheMisses++;
      throw TrophyException("Failed to load trophy XML: " +
                            std::string(result.description()));
    }
    auto root = doc.child("TrophySet");
    if (!root) {
      spdlog::error("Invalid trophy XML format: missing <TrophySet>");
      m_stats.cacheMisses++;
      throw TrophyException("Invalid trophy XML format: missing <TrophySet>");
    }
    outSet.id = root.attribute("id").as_string();
    outSet.name = root.attribute("name").as_string();
    outSet.description = root.attribute("description").as_string();
    outSet.initialized = true;
    outSet.trophies.clear();
    for (auto trophyNode : root.child("Trophies").children("Trophy")) {
      Trophy t;
      t.id = trophyNode.attribute("id").as_uint();
      t.name = trophyNode.child("Name").text().as_string();
      t.description = trophyNode.child("Description").text().as_string();
      std::string gradeStr = trophyNode.child("Grade").text().as_string();
      t.grade = StringToTrophyGrade(gradeStr);
      std::string typeStr = trophyNode.child("Type").text().as_string();
      t.type = StringToTrophyType(typeStr);
      t.unlocked = false;
      t.unlockTime = 0;
      t.iconPath = trophyNode.child("Icon").text().as_string();
      outSet.trophies.push_back(t);
    }
    std::string saveFile = "trophies/" + m_userId + "/" + outSet.id + ".sav";
    lock.unlock();
    if (std::filesystem::exists(saveFile)) {
      std::ifstream file(saveFile, std::ios::binary);
      if (file) {
        uint32_t numTrophies;
        file.read(reinterpret_cast<char *>(&numTrophies), sizeof(numTrophies));
        lock.lock();
        for (uint32_t i = 0; i < numTrophies && i < outSet.trophies.size();
             ++i) {
          bool unlocked;
          uint64_t unlockTime;
          lock.unlock();
          file.read(reinterpret_cast<char *>(&unlocked), sizeof(unlocked));
          file.read(reinterpret_cast<char *>(&unlockTime), sizeof(unlockTime));
          lock.lock();
          outSet.trophies[i].unlocked = unlocked;
          outSet.trophies[i].unlockTime = unlockTime;
        }
        lock.unlock();
        file.close();
        lock.lock();
      }
    }
    UpdateTrophySetProgress(outSet);
    m_trophySets[outSet.id] = outSet;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.saveLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Loaded trophy set {}: {} ({} trophies), latency={}us",
                 outSet.id, outSet.name, outSet.trophies.size(),
                 m_stats.saveLatencyUs);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("LoadTrophySetFromXml failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Unlocks a trophy in a set.
 * @param setId Trophy set ID.
 * @param trophyId Trophy ID.
 * @return True on success, false otherwise.
 */
bool TrophyManager::UnlockTrophy(const std::string &setId, uint32_t trophyId) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_trophyMutex);
  try {
    auto it = m_trophySets.find(setId);
    if (it == m_trophySets.end()) {
      spdlog::error("Trophy set not found: {}", setId);
      m_stats.cacheMisses++;
      throw TrophyException("Trophy set not found");
    }
    TrophySet &set = it->second;
    if (trophyId >= set.trophies.size()) {
      spdlog::error("Invalid trophy ID: {}", trophyId);
      m_stats.cacheMisses++;
      throw TrophyException("Invalid trophy ID");
    }
    Trophy &trophy = set.trophies[trophyId];
    if (trophy.unlocked) {
      spdlog::trace("Trophy {} already unlocked in set {}", trophyId, setId);
      trophy.cacheHits++;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.saveLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      return true;
    }
    trophy.unlocked = true;
    trophy.unlockTime =
        std::chrono::system_clock::now().time_since_epoch().count();
    trophy.cacheHits++;
    m_stats.totalUnlocks++;
    UpdateTrophySetProgress(set);
    if (trophy.grade == TrophyGrade::PLATINUM) {
      spdlog::info("Unlocked platinum trophy {} in set {}", trophy.name, setId);
    } else {
      bool allUnlocked = true;
      for (const auto &t : set.trophies) {
        if (t.grade != TrophyGrade::PLATINUM && !t.unlocked) {
          allUnlocked = false;
          break;
        }
      }
      if (allUnlocked) {
        for (auto &t : set.trophies) {
          if (t.grade == TrophyGrade::PLATINUM && !t.unlocked) {
            t.unlocked = true;
            t.unlockTime = trophy.unlockTime;
            t.cacheHits++;
            m_stats.totalUnlocks++;
            spdlog::info("Unlocked platinum trophy {} in set {}", t.name,
                         setId);
            break;
          }
        }
      }
    }
    lock.unlock();
    if (!SaveTrophySetToXml(setId)) {
      spdlog::error("Failed to save trophy set {}", setId);
      m_stats.cacheMisses++;
    }
    lock.lock();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.saveLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Unlocked trophy {} (ID {}) in set {}, latency={}us",
                 trophy.name, trophyId, setId, m_stats.saveLatencyUs);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("UnlockTrophy failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Retrieves trophy information.
 * @param setId Trophy set ID.
 * @param trophyId Trophy ID.
 * @param outTrophy Output trophy data.
 * @return True on success, false otherwise.
 */
bool TrophyManager::GetTrophyInfo(const std::string &setId, uint32_t trophyId,
                                  Trophy &outTrophy) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_trophyMutex);
  try {
    auto it = m_trophySets.find(setId);
    if (it == m_trophySets.end()) {
      spdlog::error("Trophy set not found: {}", setId);
      m_stats.cacheMisses++;
      return false;
    }
    const TrophySet &set = it->second;
    if (trophyId >= set.trophies.size()) {
      spdlog::error("Invalid trophy ID: {}", trophyId);
      m_stats.cacheMisses++;
      return false;
    }
    outTrophy = set.trophies[trophyId];
    auto end = std::chrono::steady_clock::now();
    m_stats.saveLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "GetTrophyInfo: Retrieved trophy ID {} from set {}, latency={}us",
        trophyId, setId, m_stats.saveLatencyUs);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("GetTrophyInfo failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Retrieves trophy information by user ID and trophy ID.
 * @param userId User ID.
 * @param trophyId Trophy ID.
 * @param outTrophy Output trophy data.
 * @return True on success, false otherwise.
 */
bool TrophyManager::GetTrophyInfo(const std::string &userId, uint32_t trophyId,
                                  Trophy &outTrophy) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_trophyMutex);
  try {
    if (userId != m_userId) {
      spdlog::error("Invalid user ID: {}", userId);
      m_stats.cacheMisses++;
      return false;
    }
    for (const auto &[setId, set] : m_trophySets) {
      for (const auto &trophy : set.trophies) {
        if (trophy.id == trophyId) {
          outTrophy = trophy;
          outTrophy.cacheHits++;
          m_stats.cacheHits++;
          auto end = std::chrono::steady_clock::now();
          m_stats.saveLatencyUs +=
              std::chrono::duration_cast<std::chrono::microseconds>(end - start)
                  .count();
          spdlog::trace(
              "GetTrophyInfo: Retrieved trophy ID {} for user {}, latency={}us",
              trophyId, userId, m_stats.saveLatencyUs);
          return true;
        }
      }
    }
    spdlog::error("Trophy ID {} not found for user {}", trophyId, userId);
    m_stats.cacheMisses++;
    return false;
  } catch (const std::exception &e) {
    spdlog::error("GetTrophyInfo (user) failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Loads a trophy icon.
 * @param setId Trophy set ID.
 * @param trophyId Trophy ID.
 * @param outIcon Output icon data.
 * @return True on success, false otherwise.
 */
bool TrophyManager::LoadTrophyIcon(const std::string &setId, uint32_t trophyId,
                                   std::vector<uint8_t> &outIcon) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_trophyMutex);
  try {
    auto it = m_trophySets.find(setId);
    if (it == m_trophySets.end()) {
      spdlog::error("Trophy set not found: {}", setId);
      m_stats.cacheMisses++;
      return false;
    }
    TrophySet &set = it->second;
    if (trophyId >= set.trophies.size()) {
      spdlog::error("Invalid trophy ID: {}", trophyId);
      m_stats.cacheMisses++;
      return false;
    }
    std::string path = set.trophies[trophyId].iconPath;
    set.trophies[trophyId].cacheHits++;
    lock.unlock();
    std::ifstream file(path, std::ios::binary);
    if (!file) {
      spdlog::error("Failed to load trophy icon: {}", path);
      m_stats.cacheMisses++;
      return false;
    }
    outIcon.assign((std::istreambuf_iterator<char>(file)), {});
    lock.lock();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.saveLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "LoadTrophyIcon: Loaded icon for set {}, id {}, size={}, latency={}us",
        setId, trophyId, outIcon.size(), m_stats.saveLatencyUs);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("LoadTrophyIcon failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Enhanced online trophy synchronization with real network support.
 */
bool TrophyManager::SyncTrophiesOnlineEnhanced(const std::string &setId,
                                               const std::string &serverUrl) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_trophyMutex);
  try {
    auto setIt = m_trophySets.find(setId);
    if (setIt == m_trophySets.end()) {
      spdlog::error("Trophy set not found for enhanced online sync: {}", setId);
      m_stats.cacheMisses++;
      return false;
    }

    TrophySet &trophySet = setIt->second;
    auto &metadata = m_trophyMetadata[setId];

    // Update sync status
    metadata.syncStatus = SyncStatus::SYNCING;

    spdlog::info("Starting enhanced online sync for trophy set: {} ({})",
                 trophySet.title, setId);

    // Prepare sync data with integrity verification
    std::string syncData = "{";
    syncData += "\"setId\":\"" + setId + "\",";
    syncData += "\"userId\":\"" + m_userId + "\",";
    syncData += "\"checksum\":\"" + CalculateChecksum(trophySet) + "\",";
    syncData += "\"trophies\":[";

    bool first = true;
    int unlockedCount = 0;
    for (const auto &trophy : trophySet.trophies) {
      if (trophy.unlocked) {
        if (!first)
          syncData += ",";
        syncData += "{";
        syncData += "\"id\":" + std::to_string(trophy.id) + ",";
        syncData += "\"unlockTime\":" + std::to_string(trophy.unlockTime);
        syncData += "}";
        first = false;
        unlockedCount++;
      }
    }
    syncData += "]}";

    // Attempt sync with retry logic
    bool syncSuccess = RetryOperation(
        [&]() {
          std::string response;
          if (!SendHTTPRequest(serverUrl + "trophies/sync", syncData,
                               response)) {
            m_stats.networkErrors++;
            return false;
          }

          std::string status, message;
          if (!ParseServerResponse(response, status, message)) {
            spdlog::error("Failed to parse server response for set {}", setId);
            return false;
          }

          if (status != "success") {
            spdlog::error("Server rejected sync for set {}: {}", setId,
                          message);
            return false;
          }

          return true;
        },
        m_maxRetries);

    if (syncSuccess) {
      metadata.syncStatus = SyncStatus::SYNCED;
      metadata.lastModified =
          std::chrono::system_clock::now().time_since_epoch().count();
      m_stats.syncSuccesses++;
      m_stats.syncOperations++;

      spdlog::info("Successfully synced {} unlocked trophies for set {}",
                   unlockedCount, setId);
    } else {
      metadata.syncStatus = SyncStatus::SYNC_FAILED;
      m_stats.syncFailures++;
      spdlog::error("Failed to sync trophies for set {} after {} retries",
                    setId, m_maxRetries);
      return false;
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.saveLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return true;
  } catch (const std::exception &e) {
    spdlog::error("SyncTrophiesOnline failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Gets the unlock progress for a trophy set.
 * @param setId Trophy set ID.
 * @return Progress percentage (0-100).
 */
uint32_t
TrophyManager::GetTrophyUnlockProgress(const std::string &setId) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_trophyMutex);
  try {
    auto it = m_trophySets.find(setId);
    if (it == m_trophySets.end()) {
      spdlog::error("Trophy set not found: {}", setId);
      m_stats.cacheMisses++;
      return 0;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.saveLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return it->second.progress;
  } catch (const std::exception &e) {
    spdlog::error("GetTrophyUnlockProgress failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}

/**
 * @brief Retrieves trophy manager statistics.
 * @return Current statistics.
 */
TrophyStats TrophyManager::GetStats() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_trophyMutex);
  try {
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.saveLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return m_stats;
  } catch (const std::exception &e) {
    spdlog::error("GetStats failed: {}", e.what());
    m_stats.cacheMisses++;
    return m_stats;
  }
}

/**
 * @brief Updates the progress of a trophy set.
 * @param set Trophy set to update.
 */
void TrophyManager::UpdateTrophySetProgress(TrophySet &set) {
  auto start = std::chrono::steady_clock::now();
  try {
    set.totalCount = static_cast<uint32_t>(set.trophies.size());
    set.platinum = 0;
    set.gold = 0;
    set.silver = 0;
    set.bronze = 0;
    set.unlockedCount = 0;

    for (const Trophy &trophy : set.trophies) {
      switch (trophy.grade) {
      case TrophyGrade::PLATINUM:
        set.platinum++;
        break;
      case TrophyGrade::GOLD:
        set.gold++;
        break;
      case TrophyGrade::SILVER:
        set.silver++;
        break;
      case TrophyGrade::BRONZE:
        set.bronze++;
        break;
      }
      if (trophy.unlocked) {
        set.unlockedCount++;
      }
    }

    set.progress =
        set.totalCount > 0 ? (set.unlockedCount * 100) / set.totalCount : 0;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.saveLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace(
        "UpdateTrophySetProgress: Updated set {}, progress={}%, latency={}us",
        set.id, set.progress, m_stats.saveLatencyUs);
  } catch (const std::exception &e) {
    spdlog::error("UpdateTrophySetProgress failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Saves a trophy set to an XML file.
 * @param setId Trophy set ID.
 * @return True on success, false otherwise.
 */
bool TrophyManager::SaveTrophySetToXml(const std::string &setId) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_trophyMutex);
  try {
    auto it = m_trophySets.find(setId);
    if (it == m_trophySets.end()) {
      spdlog::error("Trophy set not found for save: {}", setId);
      m_stats.cacheMisses++;
      return false;
    }
    const TrophySet &set = it->second;
    std::string saveFile = "trophies/" + m_userId + "/" + setId + ".sav";
    lock.unlock();
    std::filesystem::create_directories(
        std::filesystem::path(saveFile).parent_path());
    std::ofstream file(saveFile, std::ios::binary);
    if (!file) {
      spdlog::error("Failed to open trophy save file: {}", saveFile);
      m_stats.cacheMisses++;
      throw TrophyException("Failed to open trophy save file");
    }
    uint32_t numTrophies = static_cast<uint32_t>(set.trophies.size());
    file.write(reinterpret_cast<const char *>(&numTrophies),
               sizeof(numTrophies));
    for (const Trophy &trophy : set.trophies) {
      file.write(reinterpret_cast<const char *>(&trophy.unlocked),
                 sizeof(trophy.unlocked));
      file.write(reinterpret_cast<const char *>(&trophy.unlockTime),
                 sizeof(trophy.unlockTime));
    }
    file.close();
    if (!file.good()) {
      spdlog::error("Failed to write trophy save file: {}", saveFile);
      m_stats.cacheMisses++;
      return false;
    }
    lock.lock();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.saveLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Saved trophy data for {}: {} trophies, latency={}us", setId,
                 numTrophies, m_stats.saveLatencyUs);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("SaveTrophySetToXml failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Shuts down the trophy manager.
 */
void TrophyManager::Shutdown() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_trophyMutex);
  try {
    m_trophySets.clear();
    m_userId.clear();
    m_stats = TrophyStats();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.saveLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("TrophyManager shutdown, latency={}us", m_stats.saveLatencyUs);
  } catch (const std::exception &e) {
    spdlog::error("Shutdown failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Saves the trophy manager state to a stream.
 * @param out Output stream.
 */
void TrophyManager::SaveState(std::ostream &out) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_trophyMutex);
  try {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    uint32_t userIdLen = static_cast<uint32_t>(m_userId.size());
    out.write(reinterpret_cast<const char *>(&userIdLen), sizeof(userIdLen));
    out.write(m_userId.data(), userIdLen);
    uint64_t setCount = m_trophySets.size();
    out.write(reinterpret_cast<const char *>(&setCount), sizeof(setCount));
    for (const auto &[id, set] : m_trophySets) {
      uint32_t idLen = static_cast<uint32_t>(id.size());
      out.write(reinterpret_cast<const char *>(&idLen), sizeof(idLen));
      out.write(id.data(), idLen);
      uint32_t nameLen = static_cast<uint32_t>(set.name.size());
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
      out.write(set.name.data(), nameLen);
      uint32_t descLen = static_cast<uint32_t>(set.description.size());
      out.write(reinterpret_cast<const char *>(&descLen), sizeof(descLen));
      out.write(set.description.data(), descLen);
      out.write(reinterpret_cast<const char *>(&set.initialized),
                sizeof(set.initialized));
      uint64_t trophyCount = set.trophies.size();
      out.write(reinterpret_cast<const char *>(&trophyCount),
                sizeof(trophyCount));
      for (const auto &trophy : set.trophies) {
        out.write(reinterpret_cast<const char *>(&trophy.id),
                  sizeof(trophy.id));
        uint32_t trophyNameLen = static_cast<uint32_t>(trophy.name.size());
        out.write(reinterpret_cast<const char *>(&trophyNameLen),
                  sizeof(trophyNameLen));
        out.write(trophy.name.data(), trophyNameLen);
        uint32_t trophyDescLen =
            static_cast<uint32_t>(trophy.description.size());
        out.write(reinterpret_cast<const char *>(&trophyDescLen),
                  sizeof(trophyDescLen));
        out.write(trophy.description.data(), trophyDescLen);
        out.write(reinterpret_cast<const char *>(&trophy.grade),
                  sizeof(trophy.grade));
        out.write(reinterpret_cast<const char *>(&trophy.type),
                  sizeof(trophy.type));
        out.write(reinterpret_cast<const char *>(&trophy.unlocked),
                  sizeof(trophy.unlocked));
        out.write(reinterpret_cast<const char *>(&trophy.unlockTime),
                  sizeof(trophy.unlockTime));
        uint32_t iconPathLen = static_cast<uint32_t>(trophy.iconPath.size());
        out.write(reinterpret_cast<const char *>(&iconPathLen),
                  sizeof(iconPathLen));
        out.write(trophy.iconPath.data(), iconPathLen);
        out.write(reinterpret_cast<const char *>(&trophy.cacheHits),
                  sizeof(trophy.cacheHits));
        out.write(reinterpret_cast<const char *>(&trophy.cacheMisses),
                  sizeof(trophy.cacheMisses));
      }
      out.write(reinterpret_cast<const char *>(&set.unlockedCount),
                sizeof(set.unlockedCount));
      out.write(reinterpret_cast<const char *>(&set.totalCount),
                sizeof(set.totalCount));
      out.write(reinterpret_cast<const char *>(&set.platinum),
                sizeof(set.platinum));
      out.write(reinterpret_cast<const char *>(&set.gold), sizeof(set.gold));
      out.write(reinterpret_cast<const char *>(&set.silver),
                sizeof(set.silver));
      out.write(reinterpret_cast<const char *>(&set.bronze),
                sizeof(set.bronze));
      out.write(reinterpret_cast<const char *>(&set.progress),
                sizeof(set.progress));
    }
    out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));
    if (!out.good()) {
      throw std::runtime_error("Failed to write trophy manager state");
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.saveLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("SaveState: Saved trophy manager state, sets={}, latency={}us",
                 setCount, m_stats.saveLatencyUs);
  } catch (const std::exception &e) {
    spdlog::error("SaveState failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Loads the trophy manager state from a stream.
 * @param in Input stream.
 */
void TrophyManager::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_trophyMutex);
  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      spdlog::error("Unsupported trophy manager state version: {}", version);
      throw std::runtime_error("Invalid trophy manager state version");
    }
    uint32_t userIdLen;
    in.read(reinterpret_cast<char *>(&userIdLen), sizeof(userIdLen));
    m_userId.resize(userIdLen);
    in.read(m_userId.data(), userIdLen);
    m_trophySets.clear();
    uint64_t setCount;
    in.read(reinterpret_cast<char *>(&setCount), sizeof(setCount));
    for (uint64_t i = 0; i < setCount && in.good(); ++i) {
      TrophySet set;
      uint32_t idLen;
      in.read(reinterpret_cast<char *>(&idLen), sizeof(idLen));
      std::string id(idLen, '\0');
      in.read(id.data(), idLen);
      uint32_t nameLen;
      in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
      set.name.resize(nameLen);
      in.read(set.name.data(), nameLen);
      uint32_t descLen;
      in.read(reinterpret_cast<char *>(&descLen), sizeof(descLen));
      set.description.resize(descLen);
      in.read(set.description.data(), descLen);
      in.read(reinterpret_cast<char *>(&set.initialized),
              sizeof(set.initialized));
      uint64_t trophyCount;
      in.read(reinterpret_cast<char *>(&trophyCount), sizeof(trophyCount));
      set.trophies.resize(trophyCount);
      for (uint64_t j = 0; j < trophyCount && in.good(); ++j) {
        Trophy &trophy = set.trophies[j];
        in.read(reinterpret_cast<char *>(&trophy.id), sizeof(trophy.id));
        uint32_t trophyNameLen;
        in.read(reinterpret_cast<char *>(&trophyNameLen),
                sizeof(trophyNameLen));
        trophy.name.resize(trophyNameLen);
        in.read(trophy.name.data(), trophyNameLen);
        uint32_t trophyDescLen;
        in.read(reinterpret_cast<char *>(&trophyDescLen),
                sizeof(trophyDescLen));
        trophy.description.resize(trophyDescLen);
        in.read(trophy.description.data(), trophyDescLen);
        in.read(reinterpret_cast<char *>(&trophy.grade), sizeof(trophy.grade));
        in.read(reinterpret_cast<char *>(&trophy.type), sizeof(trophy.type));
        in.read(reinterpret_cast<char *>(&trophy.unlocked),
                sizeof(trophy.unlocked));
        in.read(reinterpret_cast<char *>(&trophy.unlockTime),
                sizeof(trophy.unlockTime));
        uint32_t iconPathLen;
        in.read(reinterpret_cast<char *>(&iconPathLen), sizeof(iconPathLen));
        trophy.iconPath.resize(iconPathLen);
        in.read(trophy.iconPath.data(), iconPathLen);
        in.read(reinterpret_cast<char *>(&trophy.cacheHits),
                sizeof(trophy.cacheHits));
        in.read(reinterpret_cast<char *>(&trophy.cacheMisses),
                sizeof(trophy.cacheMisses));
      }
      in.read(reinterpret_cast<char *>(&set.unlockedCount),
              sizeof(set.unlockedCount));
      in.read(reinterpret_cast<char *>(&set.totalCount),
              sizeof(set.totalCount));
      in.read(reinterpret_cast<char *>(&set.platinum), sizeof(set.platinum));
      in.read(reinterpret_cast<char *>(&set.gold), sizeof(set.gold));
      in.read(reinterpret_cast<char *>(&set.silver), sizeof(set.silver));
      in.read(reinterpret_cast<char *>(&set.bronze), sizeof(set.bronze));
      in.read(reinterpret_cast<char *>(&set.progress), sizeof(set.progress));
      m_trophySets[id] = set;
    }
    in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));
    if (!in.good()) {
      throw std::runtime_error("Failed to read trophy manager state");
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.saveLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info(
        "LoadState: Loaded trophy manager state, sets={}, latency={}us",
        m_trophySets.size(), m_stats.saveLatencyUs);
  } catch (const std::exception &e) {
    spdlog::error("LoadState failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

extern "C" {
/**
 * @brief Initializes the trophy system.
 * @return 0 on success, -1 on failure.
 */
int sceNpTrophyInit() {
  try {
    return g_trophyManager.Initialize("default_user") ? 0 : -1;
  } catch (const std::exception &e) {
    spdlog::error("sceNpTrophyInit failed: {}", e.what());
    return -1;
  }
}

/**
 * @brief Creates a trophy context.
 * @param context Output context ID.
 * @param titleId Title ID.
 * @param titleSecret Title secret.
 * @param userData User data.
 * @return 0 on success, -1 on failure.
 */
int sceNpTrophyCreateContext(uint32_t *context, const char *titleId,
                             const char *titleSecret, void *userData) {
  try {
    if (!context || !titleId) {
      spdlog::error("Invalid parameters for trophy context creation");
      return -1;
    }
    *context = 1; // Simplified context ID
    spdlog::trace("Created trophy context for title {}", titleId);
    return 0;
  } catch (const std::exception &e) {
    spdlog::error("sceNpTrophyCreateContext failed: {}", e.what());
    return -1;
  }
}

/**
 * @brief Creates a trophy handle.
 * @param handle Output handle ID.
 * @return 0 on success, -1 on failure.
 */
int sceNpTrophyCreateHandle(uint32_t *handle) {
  try {
    if (!handle) {
      spdlog::error("Invalid handle pointer for trophy handle creation");
      return -1;
    }
    *handle = 1; // Simplified handle ID
    spdlog::trace("Created trophy handle");
    return 0;
  } catch (const std::exception &e) {
    spdlog::error("sceNpTrophyCreateHandle failed: {}", e.what());
    return -1;
  }
}

/**
 * @brief Registers a trophy context.
 * @param context Context ID.
 * @param handle Handle ID.
 * @param titleId Title ID.
 * @return 0 on success, -1 on failure.
 */
int sceNpTrophyRegisterContext(uint32_t context, uint32_t handle,
                               const char *titleId) {
  try {
    if (!titleId) {
      spdlog::error("Invalid title ID for trophy registration");
      return -1;
    }
    std::string trophyPath = "trophies/" + std::string(titleId) + "/TROPHY.TRP";
    TrophySet set;
    return g_trophyManager.LoadTrophySetFromXml(trophyPath, set) ? 0 : -1;
  } catch (const std::exception &e) {
    spdlog::error("sceNpTrophyRegisterContext failed: {}", e.what());
    return -1;
  }
}

/**
 * @brief Retrieves trophy information.
 * @param context Context ID.
 * @param handle Handle ID.
 * @param trophyId Trophy ID.
 * @param trophyInfo Output trophy info.
 * @return 0 on success, -1 on failure.
 */
int sceNpTrophyGetTrophyInfo(uint32_t context, uint32_t handle,
                             uint32_t trophyId, void *trophyInfo) {
  try {
    if (!trophyInfo) {
      spdlog::error("Invalid trophy info pointer");
      return -1;
    }
    Trophy trophy;
    if (!g_trophyManager.GetTrophyInfo("NPWR12345_00", trophyId, trophy)) {
      spdlog::error("Failed to get trophy info: id={}", trophyId);
      return -1;
    }
    // Simplified: Assume trophyInfo is a Trophy struct
    *static_cast<Trophy *>(trophyInfo) = trophy;
    spdlog::trace("Retrieved trophy info: id={}", trophyId);
    return 0;
  } catch (const std::exception &e) {
    spdlog::error("sceNpTrophyGetTrophyInfo failed: {}", e.what());
    return -1;
  }
}

/**
 * @brief Unlocks a trophy.
 * @param context Context ID.
 * @param handle Handle ID.
 * @param trophyId Trophy ID.
 * @param platinumId Output platinum ID.
 * @return 0 on success, -1 on failure.
 */
int sceNpTrophyUnlockTrophy(uint32_t context, uint32_t handle,
                            uint32_t trophyId, uint32_t *platinumId) {
  try {
    bool result = g_trophyManager.UnlockTrophy("NPWR12345_00", trophyId);
    if (result && platinumId) {
      Trophy platinum;
      if (g_trophyManager.GetTrophyInfo("NPWR12345_00", 0, platinum) &&
          platinum.unlocked) {
        *platinumId = 0;
      } else {
        *platinumId = 0xFFFFFFFF;
      }
    }
    spdlog::trace("Trophy unlock attempted: id={}, result={}", trophyId,
                  result);
    return result ? 0 : -1;
  } catch (const std::exception &e) {
    spdlog::error("sceNpTrophyUnlockTrophy failed: {}", e.what());
    return -1;
  }
}
} // extern "C"

/**
 * @brief Validates XML structure for robustness.
 */
bool TrophyManager::ValidateXMLStructure(const std::string &xmlPath) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_trophyMutex);
  try {
    pugi::xml_document doc;
    auto result = doc.load_file(xmlPath.c_str());
    if (!result) {
      spdlog::error("Failed to load XML for validation: {}",
                    result.description());
      m_stats.xmlParseErrors++;
      return false;
    }

    // Validate root element
    auto root = doc.child("trophyconf");
    if (!ValidateXMLNode(root, "trophyconf")) {
      spdlog::error("Invalid root element in XML: {}", xmlPath);
      m_stats.xmlValidationErrors++;
      return false;
    }

    // Validate required attributes
    if (!ValidateXMLAttribute(root, "npcommid") ||
        !ValidateXMLAttribute(root, "npcommver")) {
      spdlog::error("Missing required attributes in XML: {}", xmlPath);
      m_stats.xmlValidationErrors++;
      return false;
    }

    // Validate trophy elements
    for (auto trophy : root.children("trophy")) {
      if (!ValidateXMLNode(trophy, "trophy") ||
          !ValidateXMLAttribute(trophy, "id") ||
          !ValidateXMLAttribute(trophy, "ttype") ||
          !ValidateXMLAttribute(trophy, "gid")) {
        spdlog::error("Invalid trophy element in XML: {}", xmlPath);
        m_stats.xmlValidationErrors++;
        return false;
      }

      // Validate trophy content
      if (!trophy.child("name") || !trophy.child("detail")) {
        spdlog::error("Missing trophy name or detail in XML: {}", xmlPath);
        m_stats.xmlValidationErrors++;
        return false;
      }
    }

    auto end = std::chrono::steady_clock::now();
    m_stats.saveLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    spdlog::debug("XML validation successful for: {}", xmlPath);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ValidateXMLStructure failed: {}", e.what());
    m_stats.xmlParseErrors++;
    return false;
  }
}

/**
 * @brief Creates backup of trophy data.
 */
bool TrophyManager::BackupTrophyData(const std::string &setId) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_trophyMutex);
  try {
    auto setIt = m_trophySets.find(setId);
    if (setIt == m_trophySets.end()) {
      spdlog::error("Trophy set not found for backup: {}", setId);
      m_stats.cacheMisses++;
      return false;
    }

    // Create backup directory if it doesn't exist
    std::filesystem::create_directories(m_backupDirectory);

    std::string timestamp = GenerateTimestamp();
    std::string backupPath =
        m_backupDirectory + setId + "_" + timestamp + ".backup";

    if (CreateBackup(setId, backupPath)) {
      m_stats.backupOperations++;
      spdlog::info("Created backup for trophy set {} at {}", setId, backupPath);

      auto end = std::chrono::steady_clock::now();
      m_stats.saveLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      return true;
    } else {
      spdlog::error("Failed to create backup for trophy set {}", setId);
      return false;
    }
  } catch (const std::exception &e) {
    spdlog::error("BackupTrophyData failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Calculates checksum for data integrity.
 */
std::string TrophyManager::CalculateChecksum(const TrophySet &set) {
  try {
    // Simple checksum calculation (in production, use SHA-256 or similar)
    uint64_t checksum = 0;

    // Include set metadata
    for (char c : set.id)
      checksum += static_cast<uint64_t>(c);
    for (char c : set.name)
      checksum += static_cast<uint64_t>(c);

    // Include trophy data
    for (const auto &trophy : set.trophies) {
      checksum += trophy.id;
      checksum += static_cast<uint64_t>(trophy.grade);
      checksum += trophy.unlocked ? 1 : 0;
      checksum += trophy.unlockTime;
    }

    return std::to_string(checksum);
  } catch (const std::exception &e) {
    spdlog::error("CalculateChecksum failed: {}", e.what());
    return "0";
  }
}

/**
 * @brief Verifies data integrity.
 */
bool TrophyManager::VerifyDataIntegrity(const std::string &setId) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_trophyMutex);
  try {
    auto setIt = m_trophySets.find(setId);
    if (setIt == m_trophySets.end()) {
      spdlog::error("Trophy set not found for integrity check: {}", setId);
      return false;
    }

    const auto &set = setIt->second;
    auto metadataIt = m_trophyMetadata.find(setId);
    if (metadataIt == m_trophyMetadata.end()) {
      spdlog::warn("No metadata found for trophy set {}, creating new", setId);
      return true; // No previous checksum to compare
    }

    std::string currentChecksum = CalculateChecksum(set);
    std::string storedChecksum = metadataIt->second.checksum;

    if (currentChecksum != storedChecksum) {
      spdlog::error("Data integrity check failed for set {}: {} != {}", setId,
                    currentChecksum, storedChecksum);
      m_stats.dataCorruptions++;
      return false;
    }

    auto end = std::chrono::steady_clock::now();
    m_stats.saveLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    spdlog::debug("Data integrity verified for trophy set {}", setId);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("VerifyDataIntegrity failed: {}", e.what());
    m_stats.dataCorruptions++;
    return false;
  }
}

/**
 * @brief Retry operation with exponential backoff.
 */
bool TrophyManager::RetryOperation(std::function<bool()> operation,
                                   int maxRetries) {
  try {
    for (int attempt = 0; attempt < maxRetries; ++attempt) {
      if (operation()) {
        return true;
      }

      if (attempt < maxRetries - 1) {
        uint64_t delay = m_retryDelayMs * (1 << attempt); // Exponential backoff
        spdlog::debug("Operation failed, retrying in {}ms (attempt {}/{})",
                      delay, attempt + 1, maxRetries);
        std::this_thread::sleep_for(std::chrono::milliseconds(delay));
      }
    }

    spdlog::error("Operation failed after {} attempts", maxRetries);
    return false;
  } catch (const std::exception &e) {
    spdlog::error("RetryOperation failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Validates XML node structure.
 */
bool TrophyManager::ValidateXMLNode(const pugi::xml_node &node,
                                    const std::string &expectedName) {
  try {
    if (!node) {
      spdlog::error("XML node is null");
      return false;
    }

    if (node.name() != expectedName) {
      spdlog::error("Expected XML node '{}', got '{}'", expectedName,
                    node.name());
      return false;
    }

    return true;
  } catch (const std::exception &e) {
    spdlog::error("ValidateXMLNode failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Validates XML attribute existence and optionally its value.
 */
bool TrophyManager::ValidateXMLAttribute(const pugi::xml_node &node,
                                         const std::string &attrName,
                                         bool required) {
  try {
    auto attr = node.attribute(attrName.c_str());
    if (!attr) {
      if (required) {
        spdlog::error("Required XML attribute '{}' not found in node '{}'",
                      attrName, node.name());
        return false;
      }
      return true; // Optional attribute not present is OK
    }

    // Basic validation - attribute exists and has non-empty value
    std::string value = attr.as_string();
    if (required && value.empty()) {
      spdlog::error("Required XML attribute '{}' has empty value in node '{}'",
                    attrName, node.name());
      return false;
    }

    return true;
  } catch (const std::exception &e) {
    spdlog::error("ValidateXMLAttribute failed for '{}': {}", attrName,
                  e.what());
    return false;
  }
}

/**
 * @brief Sends HTTP request to server (placeholder implementation).
 */
bool TrophyManager::SendHTTPRequest(const std::string &url,
                                    const std::string &data,
                                    std::string &response) {
  try {
    // Placeholder implementation - in a real implementation, use libcurl or
    // similar
    spdlog::debug("Sending HTTP request to: {}", url);
    spdlog::debug("Request data: {}", data);

    // Simulate network delay
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // Simulate successful response
    response =
        R"({"status": "success", "message": "Trophy sync completed", "timestamp": ")" +
        GenerateTimestamp() + R"("})";

    spdlog::debug("HTTP response: {}", response);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("SendHTTPRequest failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Parses server response JSON.
 */
bool TrophyManager::ParseServerResponse(const std::string &response,
                                        std::string &status,
                                        std::string &message) {
  try {
    // Simple JSON parsing - in a real implementation, use nlohmann::json or
    // similar
    size_t statusPos = response.find("\"status\":");
    size_t messagePos = response.find("\"message\":");

    if (statusPos == std::string::npos || messagePos == std::string::npos) {
      spdlog::error("Invalid server response format");
      return false;
    }

    // Extract status
    size_t statusStart = response.find("\"", statusPos + 9) + 1;
    size_t statusEnd = response.find("\"", statusStart);
    if (statusStart != std::string::npos && statusEnd != std::string::npos) {
      status = response.substr(statusStart, statusEnd - statusStart);
    }

    // Extract message
    size_t messageStart = response.find("\"", messagePos + 10) + 1;
    size_t messageEnd = response.find("\"", messageStart);
    if (messageStart != std::string::npos && messageEnd != std::string::npos) {
      message = response.substr(messageStart, messageEnd - messageStart);
    }

    spdlog::debug("Parsed response - status: {}, message: {}", status, message);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ParseServerResponse failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Creates backup of trophy data.
 */
bool TrophyManager::CreateBackup(const std::string &setId,
                                 const std::string &backupPath) {
  try {
    auto setIt = m_trophySets.find(setId);
    if (setIt == m_trophySets.end()) {
      spdlog::error("Trophy set not found for backup: {}", setId);
      return false;
    }

    const TrophySet &set = setIt->second;

    // Create backup directory
    std::filesystem::create_directories(
        std::filesystem::path(backupPath).parent_path());

    // Write backup data
    std::ofstream backup(backupPath, std::ios::binary);
    if (!backup) {
      spdlog::error("Failed to create backup file: {}", backupPath);
      return false;
    }

    // Write header
    backup.write("TROPHYBACKUP", 12);
    uint32_t version = 1;
    backup.write(reinterpret_cast<const char *>(&version), sizeof(version));

    // Write set data
    uint32_t setIdLen = static_cast<uint32_t>(set.id.length());
    backup.write(reinterpret_cast<const char *>(&setIdLen), sizeof(setIdLen));
    backup.write(set.id.c_str(), setIdLen);

    uint32_t trophyCount = static_cast<uint32_t>(set.trophies.size());
    backup.write(reinterpret_cast<const char *>(&trophyCount),
                 sizeof(trophyCount));

    for (const auto &trophy : set.trophies) {
      backup.write(reinterpret_cast<const char *>(&trophy.id),
                   sizeof(trophy.id));
      backup.write(reinterpret_cast<const char *>(&trophy.unlocked),
                   sizeof(trophy.unlocked));
      backup.write(reinterpret_cast<const char *>(&trophy.unlockTime),
                   sizeof(trophy.unlockTime));
    }

    backup.close();
    spdlog::info("Created trophy backup: {}", backupPath);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateBackup failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Generates timestamp string.
 */
std::string TrophyManager::GenerateTimestamp() {
  try {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S");
    return ss.str();
  } catch (const std::exception &e) {
    spdlog::error("GenerateTimestamp failed: {}", e.what());
    return "unknown";
  }
}

} // namespace ps4