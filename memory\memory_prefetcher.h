#include <cstdint>
#include <deque>
#include <mutex>
#include <unordered_map>
#include <utility>
#include <vector>

namespace ps4 {

class MemoryPrefetcher {
public:
  MemoryPrefetcher();
  ~MemoryPrefetcher() = default;

  // Disable copy and move to avoid accidental misuse
  MemoryPrefetcher(const MemoryPrefetcher &) = delete;
  MemoryPrefetcher &operator=(const MemoryPrefetcher &) = delete;
  MemoryPrefetcher(MemoryPrefetcher &&) = delete;
  MemoryPrefetcher &operator=(MemoryPrefetcher &&) = delete;

  // Statistics structure for performance visualization
  struct Stats {
    float hitRate = 0.0f;
    uint64_t totalAccesses = 0;
    uint64_t prefetchRequests = 0;
    uint64_t prefetchHits = 0;
    uint64_t sequentialPatterns = 0;
    uint64_t stridePatterns = 0;
  };

  // Initialize the prefetcher
  bool Initialize();

  // Register a memory read access
  void RegisterMemoryAccess(uint64_t address, size_t size, bool isWrite,
                            uint64_t processId);

  // Get prefetch address predictions based on current access patterns
  std::vector<uint64_t> GetPrefetchAddresses(uint64_t address, size_t size);

  // Execute prefetch hints (typically called from MMU)
  void ExecutePrefetchHint(uint64_t address, size_t size);

  // Get statistics
  Stats GetStats() const;

  // Get recent detected patterns for visualization
  std::vector<std::pair<uint64_t, int64_t>> GetRecentPatterns() const;

  // Configure prefetcher settings
  void SetAggressiveness(float aggressiveness);
  void SetEnabled(bool enabled);
  void Reset();

  // Prefetch statistics
  uint64_t GetTotalPrefetchRequests() const;
  uint64_t GetTotalPrefetches() const;
  float GetAveragePrefetchesPerRequest() const;

private:
  struct MemoryAccess {
    uint64_t address;
    size_t size;
    bool isWrite;
    uint64_t processId;
    uint64_t timestamp;
  };

  struct PatternEntry {
    uint64_t baseAddress;
    int64_t stride;
    uint32_t confidence;
    uint64_t lastTimestamp;
  };

  // Detect memory access patterns from recent accesses
  void DetectPatterns();

  // Analyze stride patterns
  void AnalyzeStridePatterns(uint64_t processId);

  // Prefetch data based on detected patterns
  void PrefetchData(uint64_t address, uint64_t processId);

  // Update statistics
  void RecordPrefetchHit(uint64_t address);
  void RecordPrefetchMiss(uint64_t address);
  // Record data for visualization
  void UpdateVisualizationData();

  // Cleanup stale prefetch data
  void CleanupStalePrefetchData(uint64_t currentTime);

private:
  // Configuration
  bool m_enabled{true};
  float m_aggressiveness{1.0f};

  // Recent memory accesses for pattern detection
  std::deque<MemoryAccess> m_recentAccesses;

  // Detected stride patterns (baseAddress -> stride)
  std::unordered_map<uint64_t, PatternEntry> m_patterns;

  // Last accessed address by process
  std::unordered_map<uint64_t, uint64_t> m_lastAccessByProcess;

  // Prefetched addresses
  std::unordered_map<uint64_t, uint64_t> m_prefetchedAddresses;

  // Statistics
  uint64_t m_prefetchHits{0};
  uint64_t m_prefetchMisses{0};
  uint64_t m_totalAccesses{0};
  uint64_t m_totalPrefetchRequests{0};
  uint64_t m_totalPrefetches{0};
  uint64_t m_sequentialPatterns{0};
  uint64_t m_stridePatterns{0};

  // Thread safety
  mutable std::mutex m_prefetcherMutex;

  // Visualization data
  std::vector<std::pair<uint64_t, int64_t>> m_recentPatterns;

  // Constants
  static constexpr size_t MAX_RECENT_ACCESSES = 1000;
  static constexpr size_t MAX_PATTERNS = 100;
  static constexpr uint32_t PATTERN_CONFIDENCE_THRESHOLD = 3;
};

} // namespace ps4