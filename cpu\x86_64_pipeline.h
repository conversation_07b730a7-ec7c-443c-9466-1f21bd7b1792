#pragma once

#include <chrono>
#include <cstdint>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>

#include "instruction_decoder.h"
#include "register.h"

namespace x86_64 {
class X86_64CPU;
class X86_64JITCompiler;

// IMPROVEMENT: Enhanced branch predictor with better performance
class BranchPredictor {
public:
  BranchPredictor();
  bool PredictTaken(uint64_t pc);
  void Update(uint64_t pc, bool taken);
  void RecordPrediction(bool correct);
  void ResetStats();

  // Performance metrics
  uint64_t GetHits() const { return m_hits; }
  uint64_t GetMisses() const { return m_misses; }
  double GetAccuracy() const {
    uint64_t total = m_hits + m_misses;
    return total > 0 ? static_cast<double>(m_hits) / total : 0.0;
  }

private:
  // IMPROVEMENT: Configurable predictor parameters
  static constexpr size_t HISTORY_BITS = 12;
  static constexpr size_t TABLE_SIZE = 1 << HISTORY_BITS;
  static constexpr size_t GHR_MASK = (1 << HISTORY_BITS) - 1;
  static constexpr uint8_t STRONGLY_NOT_TAKEN = 0;
  static constexpr uint8_t WEAKLY_NOT_TAKEN = 1;
  static constexpr uint8_t WEAKLY_TAKEN = 2;
  static constexpr uint8_t STRONGLY_TAKEN = 3;

  std::vector<uint8_t> m_pht; // Pattern History Table (2-bit counters)
  uint64_t m_ghr;             // Global History Register
  uint64_t m_hits;
  uint64_t m_misses;

  // IMPROVEMENT: Add local history for better prediction
  std::unordered_map<uint64_t, uint16_t> m_local_history;
};

class Pipeline {
public:
  struct FetchStage {
    uint64_t pc = 0;
    DecodedInstruction instr;
    bool valid = false;
    bool predicted_taken = false;
    uint64_t predicted_target = 0;
    uint64_t fetch_cycle = 0; // For latency tracking
  };

  struct DecodeStage {
    uint64_t pc = 0;
    DecodedInstruction instr;
    bool valid = false;
    bool predicted_taken = false;
    uint64_t predicted_target = 0;
    uint64_t decode_cycle = 0;
    uint64_t fetch_cycle = 0; // CRITICAL FIX: Track fetch cycle for latency
  };

  struct ExecuteStage {
    uint64_t pc = 0;
    DecodedInstruction instr;
    bool valid = false;
    bool predicted_taken = false;
    uint64_t predicted_target = 0;
    uint64_t execute_cycle = 0;
    uint64_t fetch_cycle = 0; // CRITICAL FIX: Track fetch cycle for latency
  };

  struct MemoryStage {
    uint64_t pc = 0;
    DecodedInstruction instr;
    bool valid = false;
    uint64_t memory_cycle = 0;
    uint64_t fetch_cycle = 0; // CRITICAL FIX: Track fetch cycle for latency
  };

  struct WriteBackStage {
    uint64_t pc = 0;
    DecodedInstruction instr;
    bool valid = false;
    uint64_t writeback_cycle = 0;
    uint64_t fetch_cycle = 0; // CRITICAL FIX: Track fetch cycle for latency
  };

  struct Stats {
    uint64_t cycles = 0;
    uint64_t instructionsExecuted = 0;
    uint64_t stalls = 0;
    uint64_t data_hazard_stalls = 0;
    uint64_t structural_hazard_stalls = 0;
    uint64_t memory_stalls = 0;
    uint64_t branch_hits = 0;
    uint64_t branch_mispredictions = 0;
    uint64_t avg_instruction_latency = 0;
    uint64_t simd_instructions = 0;
    uint64_t fetch_cycle_start = 0; // For proper latency calculation

    // IMPROVEMENT: Enhanced JIT and performance metrics
    uint64_t jit_executions = 0;
    uint64_t jit_fallbacks = 0;
    uint64_t jit_compile_failures = 0;
    uint64_t jit_cache_hits = 0;
    uint64_t jit_cache_misses = 0;
    uint64_t memory_protection_faults = 0;
    uint64_t tlb_hits = 0;
    uint64_t tlb_misses = 0;
    uint64_t cache_l1_hits = 0;
    uint64_t cache_l1_misses = 0;
    uint64_t cache_l2_hits = 0;
    uint64_t cache_l2_misses = 0;
    uint64_t prefetch_hits = 0;
    uint64_t prefetch_misses = 0;

    // Multi-threading metrics
    uint64_t thread_switches = 0;
    uint64_t lock_contentions = 0;
    uint64_t atomic_operations = 0;

    // Advanced performance counters
    double ipc = 0.0; // Instructions per cycle
    double branch_prediction_accuracy = 0.0;
    double jit_efficiency = 0.0;
    double cache_hit_ratio = 0.0;
  };

  struct ProfilingEntry {
    uint64_t cycle = 0;
    uint64_t instructions = 0;
    uint64_t stalls = 0;
    std::chrono::steady_clock::time_point timestamp;
  };

  explicit Pipeline(X86_64CPU &cpu_ref);
  void Step();
  void Flush();
  void ResetStats();
  Stats GetStats() const { return stats; }
  const std::vector<ProfilingEntry> &GetProfilingData() const {
    return profilingData;
  }

  std::unordered_map<std::string, uint64_t> GetDiagnostics() const;
  bool SwitchToFiber(uint64_t fiberId);

private:
  void FetchMultiple();
  void Decode();
  void Execute();
  void Memory();
  void WriteBack();
  bool HasDataHazard(const DecodedInstruction &instr) const;
  bool HasStructuralHazard() const;
  bool Conflicts(const DecodedInstruction &a,
                 const DecodedInstruction &b) const;
  void InterpretInstruction(ExecuteStage &stage);
  void UpdateProfiling();
  bool IsBranchInstruction(const DecodedInstruction &instr) const;
  bool IsSIMDInstruction(const DecodedInstruction &instr) const;
  uint64_t PredictBranchTarget(const DecodedInstruction &instr, uint64_t pc,
                               uint64_t nextRip) const;

  // IMPROVEMENT: Enhanced JIT integration methods
  bool ShouldUseJIT(const ExecuteStage &stage) const;
  void UpdateJITMetrics(uint64_t pc, bool success);
  bool IsJITCandidate(const DecodedInstruction &instr) const;

  // IMPROVEMENT: Memory protection and multi-threading support
  bool CheckMemoryProtection(uint64_t address, size_t size, bool write) const;
  void HandleMemoryProtectionFault(uint64_t address, uint64_t error_code);
  void UpdateCacheMetrics(uint64_t address, bool hit, int level);
  void UpdateTLBMetrics(uint64_t address, bool hit);

  // IMPROVEMENT: Multi-threading support
  void HandleThreadSwitch(uint32_t old_thread, uint32_t new_thread);
  void UpdateLockContentionMetrics();
  void TrackAtomicOperation();
  // IMPROVEMENT: Advanced performance analysis
  void CalculatePerformanceMetrics();
  void UpdateBranchPredictionAccuracy();
  void UpdateJITEfficiency();
  void UpdateCacheHitRatio();

  // CRITICAL ADDITION: Pipeline safety and deadlock prevention methods
  bool ValidatePipelineState() const;
  void FlushFromStage(int stage_num);

  X86_64CPU &cpu;
  InstructionDecoder decoder;
  std::unique_ptr<X86_64JITCompiler> jit;
  std::vector<FetchStage> fetchStage;
  std::vector<DecodeStage> decodeStage;
  std::vector<ExecuteStage> executeStage;
  std::vector<MemoryStage> memoryStage;
  std::vector<WriteBackStage> writeBackStage;
  mutable std::timed_mutex mutex;
  mutable Stats stats;
  std::vector<ProfilingEntry> profilingData;
  BranchPredictor branchPredictor;
  uint64_t execution_units = 2; // Number of execution units
};
} // namespace x86_64