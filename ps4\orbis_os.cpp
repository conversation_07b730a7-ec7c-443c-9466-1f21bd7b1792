#include "orbis_os.h"
#include "../cpu/x86_64_cpu.h"
#include "../memory/memory_diagnostics.h"
#include "ps4_emulator.h"
#include <algorithm>
#include <chrono>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <thread>

namespace ps4 {

/**
 * @brief Constructs an OrbisOS instance.
 * @param emulator Reference to the PS4 emulator.
 */
OrbisOS::OrbisOS(PS4Emulator &emulator) : m_emulator(emulator) {
  auto start = std::chrono::steady_clock::now();
  spdlog::info("OrbisOS constructed");
  m_stats = Stats();
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Destructor, cleaning up resources.
 */
OrbisOS::~OrbisOS() {
  auto start = std::chrono::steady_clock::now();
  Shutdown();
  spdlog::info("OrbisOS destroyed");
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Initializes the OrbisOS.
 * @return True on success, false otherwise.
 */
bool OrbisOS::Initialize() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_osMutex);
  try {
    m_processes.clear();
    m_threads.clear();
    m_hostToEmulatedTidMap.clear();
    m_mutexes.clear();
    m_semaphores.clear();
    m_nextProcessId = 1;
    m_nextThreadId = 1;
    m_nextMutexId = 1;
    m_nextSemId = 1;
    m_stats = Stats();
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("OrbisOS initialized");
    return true;
  } catch (const std::exception &e) {
    spdlog::error("OrbisOS initialization failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Shuts down the OrbisOS, releasing resources.
 */
void OrbisOS::Shutdown() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_osMutex);
  try {
    for (auto &[tid, thread] : m_threads) {
      thread.running = false;
      {
        std::unique_lock<std::mutex> tlock(thread.mutex);
        thread.cv.notify_all();
      }
      if (thread.hostThread.joinable()) {
        thread.hostThread.join();
      }
    }
    m_processes.clear();
    m_threads.clear();
    m_hostToEmulatedTidMap.clear();
    m_mutexes.clear();
    m_semaphores.clear();
    m_stats.processCount = 0;
    m_stats.threadCount = 0;
    m_stats.mutexCount = 0;
    m_stats.semaphoreCount = 0;
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("OrbisOS shutdown");
  } catch (const std::exception &e) {
    spdlog::error("OrbisOS shutdown failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Creates a new process.
 * @param path Path to the executable.
 * @return Process ID, or 0 on failure.
 */
uint64_t OrbisOS::SceCreateProcess(const std::string &path) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_osMutex);
  try {
    uint64_t pid = m_nextProcessId++;
    lock.unlock(); // Release mutex before MMU call to avoid deadlock
    uint64_t baseAddr = m_emulator.GetMMU().AllocateVirtual(
        pid, 1ULL * 1024 * 1024 * 1024, 4096, PROT_READ | PROT_WRITE);
    lock.lock();
    if (baseAddr == 0) {
      spdlog::error("Failed to allocate memory for process: {}", path);
      throw OSException("Process memory allocation failed");
    }
    m_processes.emplace(
        pid, Process(pid, path, baseAddr, 4ULL * 1024 * 1024 * 1024));
    m_stats.processCount++;
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().RecordAllocation(
        baseAddr, 4ULL * 1024 * 1024 * 1024, pid);
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Created process {}: path={}", pid, path);
    return pid;
  } catch (const std::exception &e) {
    spdlog::error("Create process {} failed: {}", path, e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}

/**
 * @brief Allocates virtual memory for a process.
 * @param size Size to allocate.
 * @param alignment Alignment requirement.
 * @param shared True if shared memory.
 * @return Virtual address, or 0 on failure.
 */
uint64_t OrbisOS::AllocateVirtualMemory(uint64_t size, uint64_t alignment,
                                        bool shared) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_osMutex);
  try {
    uint64_t pid = SceKernelGetProcessId();
    lock.unlock(); // Release mutex before MMU call
    uint64_t addr = m_emulator.GetMMU().AllocateVirtual(
        pid, size, alignment, PROT_READ | PROT_WRITE, shared,
        MemoryType::Default);
    lock.lock();
    if (addr == 0) {
      spdlog::error("Failed to allocate virtual memory: size={:#x}, pid={}",
                    size, pid);
      throw OSException("Virtual memory allocation failed");
    }
    auto it = m_processes.find(pid);
    if (it != m_processes.end()) {
      it->second.memoryMap->emplace(addr, size);
      it->second.cacheHits++;
    }
    m_stats.cacheHits++;
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().RecordAllocation(addr, size, pid);
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Allocated virtual memory: addr=0x{:x}, size={:#x}, pid={}",
                 addr, size, pid);
    return addr;
  } catch (const std::exception &e) {
    spdlog::error("Allocate virtual memory failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}

/**
 * @brief Sets memory protection for a region.
 * @param address Virtual address.
 * @param size Size of region.
 * @param protection Protection flags.
 * @return True on success, false otherwise.
 */
bool OrbisOS::ProtectMemory(uint64_t address, uint64_t size, int protection) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_osMutex);
  try {
    uint64_t pid = SceKernelGetProcessId();
    lock.unlock(); // Release mutex before MMU call
    bool result =
        m_emulator.GetMMU().ProtectMemory(address, size, protection, pid);
    lock.lock();
    if (!result) {
      spdlog::warn("Failed to protect memory: addr=0x{:x}, size={:#x}, pid={}",
                   address, size, pid);
      m_stats.cacheMisses++;
      return false;
    }
    auto it = m_processes.find(pid);
    if (it != m_processes.end()) {
      it->second.cacheHits++;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info(
        "Protected memory: addr=0x{:x}, size={:#x}, protection=0x{:x}, pid={}",
        address, size, protection, pid);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Protect memory failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Frees virtual memory.
 * @param address Virtual address.
 */
void OrbisOS::FreeVirtualMemory(uint64_t address) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_osMutex);
  try {
    uint64_t pid = SceKernelGetProcessId();
    auto it = m_processes.find(pid);
    if (it != m_processes.end()) {
      auto memIt = it->second.memoryMap->find(address);
      if (memIt != it->second.memoryMap->end()) {
        it->second.cacheHits++;
        it->second.memoryMap->erase(memIt);
      }
    }
    lock.unlock(); // Release mutex before MMU call
    m_emulator.GetMMU().FreeVirtual(address, pid);
    lock.lock();
    m_stats.cacheHits++;
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().RecordDeallocation(address, pid);
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Freed virtual memory: addr=0x{:x}, pid={}", address, pid);
  } catch (const std::exception &e) {
    spdlog::error("Free virtual memory failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Exits the current process.
 * @param code Exit code.
 */
void OrbisOS::ExitProcess(int code) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_osMutex);
  try {
    uint64_t pid = SceKernelGetProcessId();
    auto it = m_processes.find(pid);
    if (it != m_processes.end()) {
      for (auto &[tid, thread] : m_threads) {
        if (thread.processId == pid) {
          thread.running = false;
          {
            std::unique_lock<std::mutex> tlock(thread.mutex);
            thread.cv.notify_all();
          }
          if (thread.hostThread.joinable()) {
            thread.hostThread.join();
          }
        }
      }
      uint64_t baseAddress = it->second.baseAddress;
      it->second.cacheHits++;
      m_processes.erase(it);
      lock.unlock(); // Release mutex before MMU call
      m_emulator.GetMMU().FreeVirtual(baseAddress, pid);
      lock.lock();
      ps4::MemoryDiagnostics::GetInstance().RecordDeallocation(baseAddress,
                                                               pid);
      m_stats.processCount--;
    }
    m_stats.cacheHits++;
    lock.unlock();
    m_emulator.Stop();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Process {} exited with code {}", pid, code);
  } catch (const std::exception &e) {
    spdlog::error("Exit process failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Creates a new thread.
 * @param processId Process ID.
 * @param entry Entry point address.
 * @param arg Argument for entry point.
 * @param priority Thread priority.
 * @return Thread ID, or 0 on failure.
 */
uint64_t OrbisOS::CreateThread(uint64_t processId, uint64_t entry, void *arg,
                               int priority) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_osMutex);
  try {
    auto pit = m_processes.find(processId);
    if (pit == m_processes.end()) {
      spdlog::error("Invalid process ID for thread creation: {}", processId);
      throw OSException("Invalid process ID");
    }
    uint64_t tid = m_nextThreadId++;
    auto res =
        m_threads.emplace(std::piecewise_construct, std::forward_as_tuple(tid),
                          std::forward_as_tuple());
    Thread &threadRef = res.first->second;
    threadRef.id = tid;
    threadRef.processId = processId;
    threadRef.entry = entry;
    threadRef.arg = arg;
    threadRef.priority = priority;
    threadRef.assignedCoreId = tid % m_emulator.GetCPUCount(); // Affinity
    threadRef.running = true;
    threadRef.cpuContext = std::make_unique<x86_64::CPUContext>();
    threadRef.cpuContext->rip = entry;
    threadRef.cpuContext
        ->registers[static_cast<size_t>(x86_64::Register::RDI)] =
        reinterpret_cast<uint64_t>(arg);
    threadRef.hostThread =
        std::thread(&OrbisOS::ThreadEntry, this, tid, entry, arg);
    {
      std::unique_lock<std::shared_mutex> tlock(m_tidMapMutex);
      m_hostToEmulatedTidMap[threadRef.hostThread.get_id()] = tid;
    }
    pit->second.cacheHits++;
    m_stats.threadCount.fetch_add(1, std::memory_order_relaxed);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Created thread {}: process={}, entry=0x{:x}, priority={}",
                 tid, processId, entry, priority);
    return tid;
  } catch (const std::exception &e) {
    spdlog::error("Create thread failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return 0;
  }
}

/**
 * @brief Exits the specified thread.
 * @param tid Thread ID.
 */
void OrbisOS::ExitThread(uint64_t tid) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_osMutex);
  try {
    auto it = m_threads.find(tid);
    if (it != m_threads.end()) {
      it->second.running = false;
      {
        std::unique_lock<std::mutex> tlock(it->second.mutex);
        it->second.cv.notify_all();
      }
      if (it->second.hostThread.joinable()) {
        it->second.hostThread.join();
      }
      {
        std::unique_lock<std::shared_mutex> tlock(m_tidMapMutex);
        m_hostToEmulatedTidMap.erase(it->second.hostThread.get_id());
      }
      it->second.cacheHits++;
      m_threads.erase(it);
      m_stats.threadCount.fetch_sub(1, std::memory_order_relaxed);
    }
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::info("Thread {} exited", tid);
  } catch (const std::exception &e) {
    spdlog::error("Exit thread {} failed: {}", tid, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Schedules threads for execution.
 */
void OrbisOS::ScheduleThreads() {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    for (auto &[tid, thread] : m_threads) {
      if (thread.running && !thread.waiting) {
        std::unique_lock<std::mutex> tlock(thread.mutex);
        thread.cacheHits++;
        thread.cv.notify_one();
      }
    }
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    spdlog::trace("Scheduled threads");
  } catch (const std::exception &e) {
    spdlog::error("Schedule threads failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
}

/**
 * @brief Gets the current thread ID.
 * @return Thread ID, or 0 if not in a thread.
 */
uint64_t OrbisOS::GetCurrentThreadId() {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_tidMapMutex);
  try {
    auto tid = std::this_thread::get_id();
    auto it = m_hostToEmulatedTidMap.find(tid);
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    return it != m_hostToEmulatedTidMap.end() ? it->second : 0;
  } catch (const std::exception &e) {
    spdlog::error("Get current thread ID failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return 0;
  }
}

/**
 * @brief Creates a new mutex.
 * @param name Mutex name.
 * @return Mutex ID, or 0 on failure.
 */
uint64_t OrbisOS::SceCreateMutex(const char *name) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_osMutex);
  try {
    uint64_t mutexId = m_nextMutexId++;
    m_mutexes.try_emplace(mutexId, std::string(name ? name : "unnamed_mutex"));
    m_stats.mutexCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Created mutex {}: name={}", mutexId, name ? name : "unnamed");
    return mutexId;
  } catch (const std::exception &e) {
    spdlog::error("Create mutex failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}

/**
 * @brief Locks a mutex with a timeout.
 * @param mutexId Mutex ID.
 * @param timeoutUs Timeout in microseconds.
 * @return True on success, false if timed out or invalid.
 */
bool OrbisOS::LockMutex(uint64_t mutexId, uint64_t timeoutUs) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    auto it = m_mutexes.find(mutexId);
    if (it == m_mutexes.end()) {
      spdlog::warn("Invalid mutex ID: {}", mutexId);
      m_stats.cacheMisses++;
      return false;
    }
    Mutex &mutex = it->second;
    uint64_t tid = GetCurrentThreadId();
    if (!tid) {
      spdlog::error("Mutex lock attempted from non-emulated thread");
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }
    lock.unlock();
    if (timeoutUs == 0) {
      if (mutex.lock.try_lock()) {
        lock.lock();
        mutex.ownerThread = tid;
        mutex.recursionCount = 1;
        mutex.lockCount++;
        m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
        auto end = std::chrono::steady_clock::now();
        auto latency =
            std::chrono::duration_cast<std::chrono::microseconds>(end - start)
                .count();
        m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
        spdlog::trace("Mutex {} locked by thread {}", mutexId, tid);
        return true;
      }
      m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
      return false;
    }
    auto timeout = std::chrono::microseconds(timeoutUs);
    if (mutex.lock.try_lock_for(timeout)) {
      lock.lock();
      mutex.ownerThread = tid;
      mutex.recursionCount = 1;
      mutex.lockCount++;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace("Mutex {} locked by thread {}", mutexId, tid);
      return true;
    }
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("Mutex {} lock timeout for thread {}", mutexId, tid);
    return false;
  } catch (const std::exception &e) {
    spdlog::error("Lock mutex {} failed: {}", mutexId, e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Attempts to lock a mutex without waiting.
 * @param mutexId Mutex ID.
 * @return True on success, false if invalid or locked.
 */
bool OrbisOS::TryLockMutex(uint64_t mutexId) { return LockMutex(mutexId, 0); }

/**
 * @brief Unlocks a mutex.
 * @param mutexId Mutex ID.
 */
void OrbisOS::UnlockMutex(uint64_t mutexId) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    auto it = m_mutexes.find(mutexId);
    if (it == m_mutexes.end()) {
      spdlog::warn("Invalid mutex ID: {}", mutexId);
      m_stats.cacheMisses++;
      return;
    }
    Mutex &mutex = it->second;
    uint64_t tid = GetCurrentThreadId();
    if (mutex.ownerThread != tid) {
      spdlog::error("Mutex {} unlock attempted by non-owner thread {}", mutexId,
                    tid);
      m_stats.cacheMisses++;
      return;
    }
    if (mutex.recursionCount == 0) {
      spdlog::error(
          "Mutex {} unlock attempted with zero recursion count by thread {}",
          mutexId, tid);
      m_stats.cacheMisses++;
      return;
    }
    if (--mutex.recursionCount == 0) {
      mutex.ownerThread = 0;
      mutex.unlockCount++;
      mutex.lock.unlock();
      spdlog::trace("Mutex {} unlocked by thread {}", mutexId, tid);
    }
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  } catch (const std::exception &e) {
    spdlog::error("Unlock mutex {} failed: {}", mutexId, e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Creates a new semaphore.
 * @param name Semaphore name.
 * @param initialCount Initial count.
 * @return Semaphore ID, or 0 on failure.
 */
uint64_t OrbisOS::SceCreateSemaphore(const char *name, uint32_t initialCount) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_osMutex);
  try {
    uint64_t semId = m_nextSemId++;
    m_semaphores.try_emplace(
        semId, std::string(name ? name : "unnamed_semaphore"), initialCount);
    m_stats.semaphoreCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Created semaphore {}: name={}, initial={}", semId,
                 name ? name : "unnamed", initialCount);
    return semId;
  } catch (const std::exception &e) {
    spdlog::error("Create semaphore failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}

/**
 * @brief Waits on a semaphore with a timeout.
 * @param semId Semaphore ID.
 * @param timeoutUs Timeout in microseconds.
 * @return True on success, false if timed out or invalid.
 */
bool OrbisOS::WaitSemaphore(uint64_t semId, uint64_t timeoutUs) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    auto it = m_semaphores.find(semId);
    if (it == m_semaphores.end()) {
      spdlog::warn("Invalid semaphore ID: {}", semId);
      m_stats.cacheMisses++;
      return false;
    }
    Semaphore &sem = it->second;
    uint64_t tid = GetCurrentThreadId();
    if (!tid) {
      spdlog::error("Semaphore wait attempted from non-emulated thread");
      m_stats.cacheMisses++;
      return false;
    }
    std::unique_lock<std::mutex> slock(sem.mutex);
    if (sem.count > 0) {
      --sem.count;
      sem.waitCount++;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace("Semaphore {} decremented by thread {}, count={}", semId,
                    tid, sem.count);
      return true;
    }
    sem.waitingThreads.push(tid);
    auto threadIt = m_threads.find(tid);
    if (threadIt != m_threads.end()) {
      threadIt->second.waiting = true;
      threadIt->second.cacheHits++;
    }
    lock.unlock();
    bool success;
    if (timeoutUs == 0) {
      sem.cv.wait(slock,
                  [&] { return sem.count > 0 || !threadIt->second.running; });
      success = threadIt->second.running;
    } else {
      auto timeout = std::chrono::microseconds(timeoutUs);
      success = sem.cv.wait_for(slock, timeout, [&] {
        return sem.count > 0 || !threadIt->second.running;
      });
    }
    lock.lock();
    if (success && threadIt->second.running) {
      --sem.count;
      sem.waitingThreads.pop();
      threadIt->second.waiting = false;
      sem.waitCount++;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace("Semaphore {} decremented by thread {}, count={}", semId,
                    tid, sem.count);
      return true;
    }
    if (!sem.waitingThreads.empty() && sem.waitingThreads.front() == tid) {
      sem.waitingThreads.pop();
    }
    threadIt->second.waiting = false;
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("Semaphore {} wait timeout for thread {}", semId, tid);
    return false;
  } catch (const std::exception &e) {
    spdlog::error("Wait semaphore {} failed: {}", semId, e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Signals a semaphore.
 * @param semId Semaphore ID.
 */
void OrbisOS::SignalSemaphore(uint64_t semId) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    auto it = m_semaphores.find(semId);
    if (it == m_semaphores.end()) {
      spdlog::warn("Invalid semaphore ID: {}", semId);
      m_stats.cacheMisses++;
      return;
    }
    Semaphore &sem = it->second;
    std::unique_lock<std::mutex> slock(sem.mutex);
    ++sem.count;
    sem.signalCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("Semaphore {} signaled, count={}", semId, sem.count);
    sem.cv.notify_one();
  } catch (const std::exception &e) {
    spdlog::error("Signal semaphore {} failed: {}", semId, e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Gets the current process ID.
 * @return Process ID.
 */
uint64_t OrbisOS::SceKernelGetProcessId() {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    uint64_t tid = GetCurrentThreadId();
    auto it = m_threads.find(tid);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return it != m_threads.end() ? it->second.processId : 1;
  } catch (const std::exception &e) {
    spdlog::error("Get process ID failed: {}", e.what());
    m_stats.cacheMisses++;
    return 1;
  }
}

/**
 * @brief Sleeps the current thread.
 * @param microseconds Sleep duration.
 * @return 0 on success.
 */
int OrbisOS::SceKernelUsleep(uint32_t microseconds) {
  auto start = std::chrono::steady_clock::now();
  try {
    std::this_thread::sleep_for(std::chrono::microseconds(microseconds));
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return 0;
  } catch (const std::exception &e) {
    spdlog::error("Usleep failed: {}", e.what());
    m_stats.cacheMisses++;
    return -1;
  }
}

/**
 * @brief Gets the CPU frequency.
 * @return CPU frequency in Hz.
 */
int OrbisOS::SceKernelGetCpuFrequency() {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    int freq = m_emulator.GetTSC().GetCPUFrequency();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return freq;
  } catch (const std::exception &e) {
    spdlog::error("Get CPU frequency failed: {}", e.what());
    m_stats.cacheMisses++;
    return -1;
  }
}

/**
 * @brief Creates a new fiber.
 * @param name Fiber name.
 * @param entry Entry point address.
 * @param arg Argument for entry point.
 * @param stackSize Stack size in bytes.
 * @return Fiber ID, or 0 on failure.
 */
uint64_t OrbisOS::SceKernelCreateFiber(const char *name, uint64_t entry,
                                       void *arg, uint64_t stackSize) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    FiberFunction function = [entry](uint64_t fiberArg) {
      reinterpret_cast<void (*)(uint64_t)>(entry)(fiberArg);
    };
    uint64_t fiberId = m_emulator.GetFiberManager().CreateFiber(
        name ? name : "unnamed_fiber", function,
        reinterpret_cast<uint64_t>(arg), 0, stackSize);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Created fiber {}: name={}", fiberId, name ? name : "unnamed");
    return fiberId;
  } catch (const std::exception &e) {
    spdlog::error("Create fiber failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}

/**
 * @brief Deletes a fiber.
 * @param fiberId Fiber ID.
 * @return 0 on success, -1 on failure.
 */
int OrbisOS::SceKernelDeleteFiber(uint64_t fiberId) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    bool success = m_emulator.GetFiberManager().DeleteFiber(fiberId);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return success ? 0 : -1;
  } catch (const std::exception &e) {
    spdlog::error("Delete fiber {} failed: {}", fiberId, e.what());
    m_stats.cacheMisses++;
    return -1;
  }
}

/**
 * @brief Switches to a fiber.
 * @param fiberId Fiber ID.
 * @param argCount Number of arguments.
 * @param args Arguments.
 * @return 0 on success, -1 on failure.
 */
int OrbisOS::SceKernelSwitchToFiber(uint64_t fiberId, uint64_t argCount,
                                    void *args) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    bool success = m_emulator.GetFiberManager().SwitchToFiber(fiberId);
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return success ? 0 : -1;
  } catch (const std::exception &e) {
    spdlog::error("Switch to fiber {} failed: {}", fiberId, e.what());
    m_stats.cacheMisses++;
    return -1;
  }
}

/**
 * @brief Initializes the trophy system.
 * @return 0 on success, -1 on failure.
 */
int OrbisOS::SceNpTrophyInit() {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    bool success = m_emulator.GetTrophyManager().Initialize("default_user");
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Trophy system initialized");
    return success ? 0 : -1;
  } catch (const std::exception &e) {
    spdlog::error("Trophy init failed: {}", e.what());
    m_stats.cacheMisses++;
    return -1;
  }
}

/**
 * @brief Creates a trophy context.
 * @param context Output context ID.
 * @param titleId Title ID.
 * @param titleSecret Title secret.
 * @param userData User data.
 * @return 0 on success, -1 on failure.
 */
int OrbisOS::SceNpTrophyCreateContext(uint32_t *context, const char *titleId,
                                      const char *titleSecret,
                                      void * /*userData*/) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    if (!context || !titleId) {
      spdlog::error("Invalid trophy context parameters");
      m_stats.cacheMisses++;
      return -1;
    }
    *context = 0; // trophy context creation not supported
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Created trophy context: titleId={}", titleId);
    return 0;
  } catch (const std::exception &e) {
    spdlog::error("Create trophy context failed: {}", e.what());
    m_stats.cacheMisses++;
    return -1;
  }
}

/**
 * @brief Unlocks a trophy.
 * @param context Context ID.
 * @param handle Trophy handle.
 * @param trophyId Trophy ID.
 * @param platinumId Output platinum trophy ID.
 * @return 0 on success, -1 on failure.
 */
int OrbisOS::SceNpTrophyUnlockTrophy(uint32_t context, uint32_t handle,
                                     uint32_t trophyId, uint32_t *platinumId) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    bool success =
        m_emulator.GetTrophyManager().UnlockTrophy("NPWR12345_00", trophyId);
    if (platinumId) {
      *platinumId =
          success && trophyId == 0 ? 1 : 0; // Mock platinum for trophy 0
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Unlocked trophy {}: context={}, handle={}", trophyId, context,
                 handle);
    return success ? 0 : -1;
  } catch (const std::exception &e) {
    spdlog::error("Unlock trophy {} failed: {}", trophyId, e.what());
    m_stats.cacheMisses++;
    return -1;
  }
}

/**
 * @brief Decompresses data using zlib.
 * @param dst Output buffer.
 * @param dstSize Output size.
 * @param src Input buffer.
 * @param srcSize Input size.
 * @return 0 on success, -1 on failure.
 */
int OrbisOS::SceZlibDecompress(void *dst, size_t *dstSize, const void *src,
                               size_t srcSize) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    if (!dst || !dstSize || !src) {
      spdlog::error("Invalid zlib decompress parameters");
      m_stats.cacheMisses++;
      return -1;
    }
    std::vector<uint8_t> input(reinterpret_cast<const uint8_t *>(src),
                               reinterpret_cast<const uint8_t *>(src) +
                                   srcSize);
    std::vector<uint8_t> output;
    if (!m_emulator.GetZlibWrapper().Decompress(input, output, *dstSize)) {
      spdlog::error("Zlib decompress failed: srcSize={}", srcSize);
      m_stats.cacheMisses++;
      return -1;
    }
    if (output.size() > *dstSize) {
      spdlog::error("Zlib decompress output too large: {} > {}", output.size(),
                    *dstSize);
      m_stats.cacheMisses++;
      return -1;
    }
    std::memcpy(dst, output.data(), output.size());
    *dstSize = output.size();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("Zlib decompressed: srcSize={}, dstSize={}", srcSize,
                  *dstSize);
    return 0;
  } catch (const std::exception &e) {
    spdlog::error("Zlib decompress failed: {}", e.what());
    m_stats.cacheMisses++;
    return -1;
  }
}

/**
 * @brief Compresses data using zlib.
 * @param dst Output buffer.
 * @param dstSize Output size.
 * @param src Input buffer.
 * @param srcSize Input size.
 * @param level Compression level.
 * @return 0 on success, -1 on failure.
 */
int OrbisOS::SceZlibCompress(void *dst, size_t *dstSize, const void *src,
                             size_t srcSize, int level) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    if (!dst || !dstSize || !src) {
      spdlog::error("Invalid zlib compress parameters");
      m_stats.cacheMisses++;
      return -1;
    }
    std::vector<uint8_t> input(reinterpret_cast<const uint8_t *>(src),
                               reinterpret_cast<const uint8_t *>(src) +
                                   srcSize);
    std::vector<uint8_t> output;
    if (!m_emulator.GetZlibWrapper().Compress(
            input, output, static_cast<CompressionLevel>(level))) {
      spdlog::error("Zlib compress failed: srcSize={}", srcSize);
      m_stats.cacheMisses++;
      return -1;
    }
    if (output.size() > *dstSize) {
      spdlog::error("Zlib compress output too large: {} > {}", output.size(),
                    *dstSize);
      m_stats.cacheMisses++;
      return -1;
    }
    std::memcpy(dst, output.data(), output.size());
    *dstSize = output.size();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("Zlib compressed: srcSize={}, dstSize={}", srcSize, *dstSize);
    return 0;
  } catch (const std::exception &e) {
    spdlog::error("Zlib compress failed: {}", e.what());
    m_stats.cacheMisses++;
    return -1;
  }
}

/**
 * @brief Thread entry point for execution.
 * @param tid Thread ID.
 * @param entry Entry point address.
 * @param arg Argument for entry point.
 */
void OrbisOS::ThreadEntry(uint64_t tid, uint64_t entry, void *arg) {
  auto start = std::chrono::steady_clock::now();
  auto it = m_threads.find(tid);
  if (it == m_threads.end()) {
    spdlog::error("Thread {} not found in entry", tid);
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return;
  }
  Thread &thread = it->second;
  try {
    m_emulator.GetCPU(thread.assignedCoreId).SetContext(*thread.cpuContext);
    m_emulator.GetCPU(thread.assignedCoreId).Execute();
    thread.cacheHits++;
    m_stats.cacheHits.fetch_add(1, std::memory_order_relaxed);
  } catch (const std::exception &e) {
    spdlog::error("Exception in thread {}: {}", tid, e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
  }
  thread.executionTimeUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(
          std::chrono::steady_clock::now() - start)
          .count();
  ExitThread(tid);
}

/**
 * @brief Saves the OrbisOS state to a stream.
 * @param out Output stream.
 */
void OrbisOS::SaveState(std::ostream &out) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_osMutex);
  try {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    uint64_t processCount = m_processes.size();
    out.write(reinterpret_cast<const char *>(&processCount),
              sizeof(processCount));
    for (const auto &[pid, proc] : m_processes) {
      uint32_t pathLen = static_cast<uint32_t>(proc.path.size());
      out.write(reinterpret_cast<const char *>(&pid), sizeof(pid));
      out.write(reinterpret_cast<const char *>(&pathLen), sizeof(pathLen));
      out.write(proc.path.data(), pathLen);
      out.write(reinterpret_cast<const char *>(&proc.baseAddress),
                sizeof(proc.baseAddress));
      out.write(reinterpret_cast<const char *>(&proc.size), sizeof(proc.size));
      out.write(reinterpret_cast<const char *>(&proc.executionTimeUs),
                sizeof(proc.executionTimeUs));
      out.write(reinterpret_cast<const char *>(&proc.cacheHits),
                sizeof(proc.cacheHits));
      out.write(reinterpret_cast<const char *>(&proc.cacheMisses),
                sizeof(proc.cacheMisses));
      uint64_t fdCount = proc.fdTable->size();
      out.write(reinterpret_cast<const char *>(&fdCount), sizeof(fdCount));
      for (const auto &fd : *proc.fdTable) {
        out.write(reinterpret_cast<const char *>(&fd), sizeof(fd));
      }
      uint64_t mapCount = proc.memoryMap->size();
      out.write(reinterpret_cast<const char *>(&mapCount), sizeof(mapCount));
      for (const auto &[addr, size] : *proc.memoryMap) {
        out.write(reinterpret_cast<const char *>(&addr), sizeof(addr));
        out.write(reinterpret_cast<const char *>(&size), sizeof(size));
      }
    }
    uint64_t threadCount = m_threads.size();
    out.write(reinterpret_cast<const char *>(&threadCount),
              sizeof(threadCount));
    for (const auto &[tid, thread] : m_threads) {
      out.write(reinterpret_cast<const char *>(&tid), sizeof(tid));
      out.write(reinterpret_cast<const char *>(&thread.processId),
                sizeof(thread.processId));
      out.write(reinterpret_cast<const char *>(&thread.entry),
                sizeof(thread.entry));
      out.write(reinterpret_cast<const char *>(&thread.priority),
                sizeof(thread.priority));
      out.write(reinterpret_cast<const char *>(&thread.assignedCoreId),
                sizeof(thread.assignedCoreId));
      out.write(reinterpret_cast<const char *>(&thread.executionTimeUs),
                sizeof(thread.executionTimeUs));
      out.write(reinterpret_cast<const char *>(&thread.cacheHits),
                sizeof(thread.cacheHits));
      out.write(reinterpret_cast<const char *>(&thread.cacheMisses),
                sizeof(thread.cacheMisses));
      // Serialize CPU context
      // thread.cpuContext->Save(out);
    }
    uint64_t mutexCount = m_mutexes.size();
    out.write(reinterpret_cast<const char *>(&mutexCount), sizeof(mutexCount));
    for (const auto &[mid, mutex] : m_mutexes) {
      uint32_t nameLen = static_cast<uint32_t>(mutex.name.size());
      out.write(reinterpret_cast<const char *>(&mid), sizeof(mid));
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
      out.write(mutex.name.data(), nameLen);
      out.write(reinterpret_cast<const char *>(&mutex.ownerThread),
                sizeof(mutex.ownerThread));
      out.write(reinterpret_cast<const char *>(&mutex.recursionCount),
                sizeof(mutex.recursionCount));
      out.write(reinterpret_cast<const char *>(&mutex.lockCount),
                sizeof(mutex.lockCount));
      out.write(reinterpret_cast<const char *>(&mutex.unlockCount),
                sizeof(mutex.unlockCount));
      uint64_t waitingCount = mutex.waitingThreads.size();
      out.write(reinterpret_cast<const char *>(&waitingCount),
                sizeof(waitingCount));
      auto tempQueue = mutex.waitingThreads;
      while (!tempQueue.empty()) {
        uint64_t tid = tempQueue.front();
        out.write(reinterpret_cast<const char *>(&tid), sizeof(tid));
        tempQueue.pop();
      }
    }
    uint64_t semCount = m_semaphores.size();
    out.write(reinterpret_cast<const char *>(&semCount), sizeof(semCount));
    for (const auto &[sid, sem] : m_semaphores) {
      uint32_t nameLen = static_cast<uint32_t>(sem.name.size());
      out.write(reinterpret_cast<const char *>(&sid), sizeof(sid));
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
      out.write(sem.name.data(), nameLen);
      out.write(reinterpret_cast<const char *>(&sem.count), sizeof(sem.count));
      out.write(reinterpret_cast<const char *>(&sem.waitCount),
                sizeof(sem.waitCount));
      out.write(reinterpret_cast<const char *>(&sem.signalCount),
                sizeof(sem.signalCount));
      uint64_t waitingCount = sem.waitingThreads.size();
      out.write(reinterpret_cast<const char *>(&waitingCount),
                sizeof(waitingCount));
      auto tempQueue = sem.waitingThreads;
      while (!tempQueue.empty()) {
        uint64_t tid = tempQueue.front();
        out.write(reinterpret_cast<const char *>(&tid), sizeof(tid));
        tempQueue.pop();
      }
    }
    out.write(reinterpret_cast<const char *>(&m_nextProcessId),
              sizeof(m_nextProcessId));
    out.write(reinterpret_cast<const char *>(&m_nextThreadId),
              sizeof(m_nextThreadId));
    out.write(reinterpret_cast<const char *>(&m_nextMutexId),
              sizeof(m_nextMutexId));
    out.write(reinterpret_cast<const char *>(&m_nextSemId),
              sizeof(m_nextSemId));
    out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));
    if (!out.good()) {
      throw std::runtime_error("Failed to write OrbisOS state");
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("OrbisOS state saved: {} processes, {} threads", processCount,
                 threadCount);
  } catch (const std::exception &e) {
    spdlog::error("OrbisOS SaveState failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Loads the OrbisOS state from a stream.
 * @param in Input stream.
 */
void OrbisOS::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_osMutex);
  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      spdlog::error("Unsupported OrbisOS state version: {}", version);
      throw std::runtime_error("Invalid OrbisOS state version");
    }
    Shutdown();
    uint64_t processCount;
    in.read(reinterpret_cast<char *>(&processCount), sizeof(processCount));
    for (uint64_t i = 0; i < processCount && in.good(); ++i) {
      uint64_t pid;
      uint32_t pathLen;
      uint64_t baseAddr, size, executionTimeUs, cacheHits, cacheMisses;
      in.read(reinterpret_cast<char *>(&pid), sizeof(pid));
      in.read(reinterpret_cast<char *>(&pathLen), sizeof(pathLen));
      std::string path(pathLen, '\0');
      in.read(path.data(), pathLen);
      in.read(reinterpret_cast<char *>(&baseAddr), sizeof(baseAddr));
      in.read(reinterpret_cast<char *>(&size), sizeof(size));
      in.read(reinterpret_cast<char *>(&executionTimeUs),
              sizeof(executionTimeUs));
      in.read(reinterpret_cast<char *>(&cacheHits), sizeof(cacheHits));
      in.read(reinterpret_cast<char *>(&cacheMisses), sizeof(cacheMisses));
      Process proc(pid, path, baseAddr, size);
      proc.executionTimeUs = executionTimeUs;
      proc.cacheHits = cacheHits;
      proc.cacheMisses = cacheMisses;
      uint64_t fdCount;
      in.read(reinterpret_cast<char *>(&fdCount), sizeof(fdCount));
      proc.fdTable->resize(fdCount);
      for (auto &fd : *proc.fdTable) {
        in.read(reinterpret_cast<char *>(&fd), sizeof(fd));
      }
      uint64_t mapCount;
      in.read(reinterpret_cast<char *>(&mapCount), sizeof(mapCount));
      for (uint64_t j = 0; j < mapCount && in.good(); ++j) {
        uint64_t addr, size;
        in.read(reinterpret_cast<char *>(&addr), sizeof(addr));
        in.read(reinterpret_cast<char *>(&size), sizeof(size));
        proc.memoryMap->emplace(addr, size);
      }
      m_processes.emplace(pid, std::move(proc));
    }
    uint64_t threadCount;
    in.read(reinterpret_cast<char *>(&threadCount), sizeof(threadCount));
    for (uint64_t i = 0; i < threadCount && in.good(); ++i) {
      uint64_t tid, pid, entry, priority, coreId, executionTimeUs, cacheHits,
          cacheMisses;
      in.read(reinterpret_cast<char *>(&tid), sizeof(tid));
      in.read(reinterpret_cast<char *>(&pid), sizeof(pid));
      in.read(reinterpret_cast<char *>(&entry), sizeof(entry));
      in.read(reinterpret_cast<char *>(&priority), sizeof(priority));
      in.read(reinterpret_cast<char *>(&coreId), sizeof(coreId));
      in.read(reinterpret_cast<char *>(&executionTimeUs),
              sizeof(executionTimeUs));
      in.read(reinterpret_cast<char *>(&cacheHits), sizeof(cacheHits));
      in.read(reinterpret_cast<char *>(&cacheMisses), sizeof(cacheMisses));
      Thread &thread = m_threads[tid];
      thread.id = tid;
      thread.processId = pid;
      thread.entry = entry;
      thread.priority = priority;
      thread.assignedCoreId = coreId;
      thread.executionTimeUs = executionTimeUs;
      thread.cacheHits = cacheHits;
      thread.cacheMisses = cacheMisses;
      thread.cpuContext = std::make_unique<x86_64::CPUContext>();
      // thread.cpuContext->Load(in);
      thread.running = true;
    }
    uint64_t mutexCount;
    in.read(reinterpret_cast<char *>(&mutexCount), sizeof(mutexCount));
    for (uint64_t i = 0; i < mutexCount && in.good(); ++i) {
      uint64_t mid, ownerThread, lockCount, unlockCount;
      uint32_t nameLen, recursionCount;
      in.read(reinterpret_cast<char *>(&mid), sizeof(mid));
      in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
      std::string name(nameLen, '\0');
      in.read(name.data(), nameLen);
      in.read(reinterpret_cast<char *>(&ownerThread), sizeof(ownerThread));
      in.read(reinterpret_cast<char *>(&recursionCount),
              sizeof(recursionCount));
      in.read(reinterpret_cast<char *>(&lockCount), sizeof(lockCount));
      in.read(reinterpret_cast<char *>(&unlockCount), sizeof(unlockCount));
      // Construct Mutex in-place using try_emplace to forward name to
      // constructor
      auto &newMutex = m_mutexes.try_emplace(mid, name).first->second;
      newMutex.ownerThread = ownerThread;
      newMutex.recursionCount = recursionCount;
      newMutex.lockCount = lockCount;
      newMutex.unlockCount = unlockCount;
      uint64_t waitingCount;
      in.read(reinterpret_cast<char *>(&waitingCount), sizeof(waitingCount));
      for (uint64_t j = 0; j < waitingCount && in.good(); ++j) {
        uint64_t tid;
        in.read(reinterpret_cast<char *>(&tid), sizeof(tid));
        newMutex.waitingThreads.push(tid);
      }
    }
    uint64_t semCount;
    in.read(reinterpret_cast<char *>(&semCount), sizeof(semCount));
    for (uint64_t i = 0; i < semCount && in.good(); ++i) {
      uint64_t sid, waitCount, signalCount;
      uint32_t nameLen, count;
      in.read(reinterpret_cast<char *>(&sid), sizeof(sid));
      in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
      std::string name(nameLen, '\0');
      in.read(name.data(), nameLen);
      in.read(reinterpret_cast<char *>(&count), sizeof(count));
      in.read(reinterpret_cast<char *>(&waitCount), sizeof(waitCount));
      in.read(reinterpret_cast<char *>(&signalCount), sizeof(signalCount));
      // Construct Semaphore in-place using try_emplace to forward name and
      // count
      auto &newSem = m_semaphores.try_emplace(sid, name, count).first->second;
      newSem.waitCount = waitCount;
      newSem.signalCount = signalCount;
      uint64_t waitingCount;
      in.read(reinterpret_cast<char *>(&waitingCount), sizeof(waitingCount));
      for (uint64_t j = 0; j < waitingCount && in.good(); ++j) {
        uint64_t tid;
        in.read(reinterpret_cast<char *>(&tid), sizeof(tid));
        newSem.waitingThreads.push(tid);
      }
    }
    in.read(reinterpret_cast<char *>(&m_nextProcessId),
            sizeof(m_nextProcessId));
    in.read(reinterpret_cast<char *>(&m_nextThreadId), sizeof(m_nextThreadId));
    in.read(reinterpret_cast<char *>(&m_nextMutexId), sizeof(m_nextMutexId));
    in.read(reinterpret_cast<char *>(&m_nextSemId), sizeof(m_nextSemId));
    in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));
    if (!in.good()) {
      throw std::runtime_error("Failed to read OrbisOS state");
    }
    m_stats.processCount = m_processes.size();
    m_stats.threadCount = m_threads.size();
    m_stats.mutexCount = m_mutexes.size();
    m_stats.semaphoreCount = m_semaphores.size();
    m_stats.cacheHits++;
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("OrbisOS state loaded: {} processes, {} threads", processCount,
                 threadCount);
  } catch (const std::exception &e) {
    spdlog::error("OrbisOS LoadState failed: {}", e.what());
    m_stats.cacheMisses++;
    Shutdown();
  }
}

/**
 * @brief Retrieves operating system statistics.
 * @return Current statistics.
 */
OrbisOS::Stats OrbisOS::GetStats() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    // RACE CONDITION FIX: Create a copy with atomic loads
    Stats result;
    result.processCount.store(m_stats.processCount.load());
    result.threadCount.store(m_stats.threadCount.load());
    result.mutexCount.store(m_stats.mutexCount.load());
    result.semaphoreCount.store(m_stats.semaphoreCount.load());
    result.totalLatencyUs.store(m_stats.totalLatencyUs.load());
    result.cacheHits.store(m_stats.cacheHits.load());
    result.cacheMisses.store(m_stats.cacheMisses.load());

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs.fetch_add(latency, std::memory_order_relaxed);
    return result;
  } catch (const std::exception &e) {
    spdlog::error("Get OrbisOS stats failed: {}", e.what());
    m_stats.cacheMisses.fetch_add(1, std::memory_order_relaxed);
    return m_stats;
  }
}

// Enhanced OS management methods implementation

/**
 * @brief Sets thread priority with proper validation.
 * @param tid Thread ID.
 * @param priority New priority (lower values = higher priority).
 * @return True on success, false otherwise.
 */
bool OrbisOS::SetThreadPriority(uint64_t tid, int priority) {
  std::unique_lock<std::shared_mutex> lock(m_osMutex);
  try {
    auto it = m_threads.find(tid);
    if (it == m_threads.end()) {
      spdlog::warn("SetThreadPriority: Invalid thread ID: {}", tid);
      return false;
    }

    // Validate priority range (PS4 uses 0-767, lower is higher priority)
    if (priority < 0 || priority > 767) {
      spdlog::warn("SetThreadPriority: Invalid priority {} for thread {}",
                   priority, tid);
      return false;
    }

    int oldPriority = it->second.priority;
    it->second.priority = priority;

    spdlog::debug("Thread {} priority changed: {} -> {}", tid, oldPriority,
                  priority);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("SetThreadPriority failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Gets thread priority.
 * @param tid Thread ID.
 * @return Thread priority, or -1 if invalid.
 */
int OrbisOS::GetThreadPriority(uint64_t tid) {
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    auto it = m_threads.find(tid);
    return it != m_threads.end() ? it->second.priority : -1;
  } catch (const std::exception &e) {
    spdlog::error("GetThreadPriority failed: {}", e.what());
    return -1;
  }
}

/**
 * @brief Sets thread CPU affinity.
 * @param tid Thread ID.
 * @param affinityMask CPU affinity mask.
 * @return True on success, false otherwise.
 */
bool OrbisOS::SetThreadAffinity(uint64_t tid, uint64_t affinityMask) {
  std::unique_lock<std::shared_mutex> lock(m_osMutex);
  try {
    auto it = m_threads.find(tid);
    if (it == m_threads.end()) {
      spdlog::warn("SetThreadAffinity: Invalid thread ID: {}", tid);
      return false;
    }

    // PS4 has 8 CPU cores (0-7)
    if (affinityMask == 0 || affinityMask > 0xFF) {
      spdlog::warn(
          "SetThreadAffinity: Invalid affinity mask 0x{:x} for thread {}",
          affinityMask, tid);
      return false;
    }

    m_threadAffinity[tid] = affinityMask;
    spdlog::debug("Thread {} affinity set to 0x{:x}", tid, affinityMask);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("SetThreadAffinity failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Gets thread CPU affinity.
 * @param tid Thread ID.
 * @return CPU affinity mask, or 0 if invalid.
 */
uint64_t OrbisOS::GetThreadAffinity(uint64_t tid) {
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    auto it = m_threadAffinity.find(tid);
    return it != m_threadAffinity.end() ? it->second
                                        : 0xFF; // Default: all cores
  } catch (const std::exception &e) {
    spdlog::error("GetThreadAffinity failed: {}", e.what());
    return 0;
  }
}

/**
 * @brief Yields the current thread to allow other threads to run.
 */
void OrbisOS::YieldThread() {
  try {
    std::this_thread::yield();
    spdlog::trace("Thread yielded execution");
  } catch (const std::exception &e) {
    spdlog::error("YieldThread failed: {}", e.what());
  }
}

/**
 * @brief Suspends a thread.
 * @param tid Thread ID.
 * @return True on success, false otherwise.
 */
bool OrbisOS::SuspendThread(uint64_t tid) {
  std::unique_lock<std::shared_mutex> lock(m_osMutex);
  try {
    auto it = m_threads.find(tid);
    if (it == m_threads.end()) {
      spdlog::warn("SuspendThread: Invalid thread ID: {}", tid);
      return false;
    }

    m_suspendedThreads[tid] = true;
    it->second.running = false;
    spdlog::debug("Thread {} suspended", tid);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("SuspendThread failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Resumes a suspended thread.
 * @param tid Thread ID.
 * @return True on success, false otherwise.
 */
bool OrbisOS::ResumeThread(uint64_t tid) {
  std::unique_lock<std::shared_mutex> lock(m_osMutex);
  try {
    auto it = m_threads.find(tid);
    if (it == m_threads.end()) {
      spdlog::warn("ResumeThread: Invalid thread ID: {}", tid);
      return false;
    }

    auto suspendIt = m_suspendedThreads.find(tid);
    if (suspendIt == m_suspendedThreads.end() || !suspendIt->second) {
      spdlog::warn("ResumeThread: Thread {} is not suspended", tid);
      return false;
    }

    m_suspendedThreads[tid] = false;
    it->second.running = true;
    it->second.cv.notify_one();
    spdlog::debug("Thread {} resumed", tid);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ResumeThread failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Handles priority inheritance for mutex contention.
 * @param mutexId Mutex ID.
 * @param waiterTid Waiting thread ID.
 */
void OrbisOS::HandlePriorityInheritance(uint64_t mutexId, uint64_t waiterTid) {
  try {
    auto mutexIt = m_mutexes.find(mutexId);
    auto waiterIt = m_threads.find(waiterTid);

    if (mutexIt == m_mutexes.end() || waiterIt == m_threads.end()) {
      return;
    }

    Mutex &mutex = mutexIt->second;
    if (!mutex.priorityInheritance || mutex.ownerThread == 0) {
      return;
    }

    auto ownerIt = m_threads.find(mutex.ownerThread);
    if (ownerIt == m_threads.end()) {
      return;
    }

    // If waiter has higher priority (lower number), inherit it
    if (waiterIt->second.priority < ownerIt->second.priority) {
      mutex.inheritedPriority = waiterIt->second.priority;
      ownerIt->second.priority = waiterIt->second.priority;

      spdlog::trace("Priority inheritance: mutex {} owner {} inherited "
                    "priority {} from waiter {}",
                    mutexId, mutex.ownerThread, waiterIt->second.priority,
                    waiterTid);
    }
  } catch (const std::exception &e) {
    spdlog::error("HandlePriorityInheritance failed: {}", e.what());
  }
}

/**
 * @brief Restores original priority after mutex unlock.
 * @param mutexId Mutex ID.
 */
void OrbisOS::RestoreOriginalPriority(uint64_t mutexId) {
  try {
    auto mutexIt = m_mutexes.find(mutexId);
    if (mutexIt == m_mutexes.end()) {
      return;
    }

    Mutex &mutex = mutexIt->second;
    if (!mutex.priorityInheritance || mutex.ownerThread == 0) {
      return;
    }

    auto ownerIt = m_threads.find(mutex.ownerThread);
    if (ownerIt != m_threads.end() && mutex.inheritedPriority != 0) {
      ownerIt->second.priority = mutex.originalPriority;
      mutex.inheritedPriority = 0;

      spdlog::trace(
          "Priority restored: mutex {} owner {} priority restored to {}",
          mutexId, mutex.ownerThread, mutex.originalPriority);
    }
  } catch (const std::exception &e) {
    spdlog::error("RestoreOriginalPriority failed: {}", e.what());
  }
}

/**
 * @brief Gets list of threads waiting on a mutex.
 * @param mutexId Mutex ID.
 * @return Vector of waiting thread IDs.
 */
std::vector<uint64_t> OrbisOS::GetWaitingThreads(uint64_t mutexId) {
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    auto it = m_mutexes.find(mutexId);
    if (it == m_mutexes.end()) {
      return {};
    }

    std::vector<uint64_t> waiters;
    std::queue<uint64_t> tempQueue = it->second.waitingThreads;
    while (!tempQueue.empty()) {
      waiters.push_back(tempQueue.front());
      tempQueue.pop();
    }
    return waiters;
  } catch (const std::exception &e) {
    spdlog::error("GetWaitingThreads failed: {}", e.what());
    return {};
  }
}

/**
 * @brief Validates resource limits for a process.
 * @param processId Process ID.
 * @return True if within limits, false otherwise.
 */
bool OrbisOS::ValidateResourceLimits(uint64_t processId) {
  std::shared_lock<std::shared_mutex> lock(m_osMutex);
  try {
    auto processIt = m_processes.find(processId);
    if (processIt == m_processes.end()) {
      return false;
    }

    // Check thread count limit (PS4 supports up to 64 threads per process)
    auto threadCountIt = m_processThreadCount.find(processId);
    if (threadCountIt != m_processThreadCount.end() &&
        threadCountIt->second > 64) {
      spdlog::warn("Process {} exceeds thread limit: {}", processId,
                   threadCountIt->second);
      return false;
    }

    // Check memory usage limit (example: 5GB for applications)
    auto memoryIt = m_processMemoryUsage.find(processId);
    if (memoryIt != m_processMemoryUsage.end() &&
        memoryIt->second > (5ULL * 1024 * 1024 * 1024)) {
      spdlog::warn("Process {} exceeds memory limit: {} bytes", processId,
                   memoryIt->second);
      return false;
    }

    return true;
  } catch (const std::exception &e) {
    spdlog::error("ValidateResourceLimits failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Updates resource usage tracking for a process.
 * @param processId Process ID.
 */
void OrbisOS::UpdateResourceUsage(uint64_t processId) {
  std::unique_lock<std::shared_mutex> lock(m_osMutex);
  try {
    // Count threads for this process
    uint64_t threadCount = 0;
    for (const auto &[tid, thread] : m_threads) {
      if (thread.processId == processId) {
        threadCount++;
      }
    }
    m_processThreadCount[processId] = threadCount;

    // Update memory usage (simplified - would need integration with MMU)
    auto processIt = m_processes.find(processId);
    if (processIt != m_processes.end()) {
      m_processMemoryUsage[processId] = processIt->second.size;
    }

    spdlog::trace("Process {} resource usage: {} threads, {} bytes memory",
                  processId, threadCount, m_processMemoryUsage[processId]);
  } catch (const std::exception &e) {
    spdlog::error("UpdateResourceUsage failed: {}", e.what());
  }
}

} // namespace ps4