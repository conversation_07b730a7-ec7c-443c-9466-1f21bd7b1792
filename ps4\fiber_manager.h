// Copyright 2025 <Copyright Owner>

#pragma once

#include <array>
#include <atomic>
#include <cstdint>
#include <functional>
#include <istream>
#include <memory>
#include <ostream>
#include <shared_mutex>
#include <string>
#include <unordered_map>
#include <vector>

#ifdef _WIN32
#if defined(_M_X64) && !defined(_AMD64_)
#define _AMD64_
#endif
#if defined(_M_IX86) && !defined(_X86_)
#define _X86_
#endif
#include <windows.h>
#else
#include <ucontext.h>
#endif

namespace ps4 {

/**
 * @brief States for a fiber's lifecycle.
 */
enum class FiberState {
  READY,    ///< Fiber is ready to run
  RUNNING,  ///< <PERSON>ber is currently executing
  WAITING,  ///< Fiber is waiting for another fiber or event
  FINISHED, ///< <PERSON>ber has completed execution
  SUSPENDED ///< Fiber is paused
};

/**
 * @brief Scheduling policies for fiber execution.
 */
enum class SchedulingPolicy {
  PRIORITY,    ///< Schedule based on priority (0=highest, 255=lowest)
  ROUND_ROBIN, ///< Schedule in a circular order
  ADAPTIVE     ///< Schedule based on execution time and priority
};

/**
 * @brief Function type for fiber entry points.
 */
using FiberFunction = std::function<void(uint64_t)>;

/**
 * @brief Enhanced PS4-specific fiber performance characteristics.
 */
struct PS4FiberPerformance {
  uint64_t cpuCycles = 0;            ///< CPU cycles consumed
  uint64_t memoryAccesses = 0;       ///< Memory access count
  uint64_t cacheL1Hits = 0;          ///< L1 cache hits
  uint64_t cacheL1Misses = 0;        ///< L1 cache misses
  uint64_t cacheL2Hits = 0;          ///< L2 cache hits
  uint64_t cacheL2Misses = 0;        ///< L2 cache misses
  uint64_t branchPredictions = 0;    ///< Branch predictions
  uint64_t branchMispredictions = 0; ///< Branch mispredictions
  uint64_t tlbHits = 0;              ///< TLB hits
  uint64_t tlbMisses = 0;            ///< TLB misses
  float ipc = 0.0f;                  ///< Instructions per cycle
  float cacheHitRatio = 0.0f;        ///< Overall cache hit ratio
  uint64_t lastUpdateTime = 0;       ///< Last performance update timestamp
};

/**
 * @brief Enhanced fiber affinity and scheduling hints.
 */
struct PS4FiberAffinity {
  uint8_t preferredCore = 0xFF;       ///< Preferred CPU core (0xFF = any)
  uint8_t affinityMask = 0xFF;        ///< CPU affinity mask (8 cores)
  bool numaAware = false;             ///< NUMA-aware scheduling
  uint32_t lastCore = 0xFF;           ///< Last executed core
  uint64_t coreAffinityTime[8] = {0}; ///< Time spent on each core
  bool stickyScheduling = false;      ///< Prefer same core for cache locality
};

/**
 * @brief Enhanced structure representing a fiber for lightweight threading.
 */
struct Fiber {
  uint64_t id = 0;                      ///< Unique fiber identifier
  std::string name;                     ///< Fiber name
  FiberState state = FiberState::READY; ///< Current state
  uint64_t arg = 0;                     ///< Argument passed to function
  FiberFunction function;               ///< Entry point function
  uint64_t waitingOn = 0;               ///< ID of fiber being waited on
  uint8_t priority = 0;                 ///< Priority (0=highest, 255=lowest)
  uint64_t stackSize = 0;               ///< Stack size in bytes
  std::vector<uint8_t> stack;           ///< Stack memory
  uint64_t executionTimeUs = 0;         ///< Total execution time (microseconds)
  uint32_t switchCount = 0;             ///< Number of context switches
  uint64_t cacheHits = 0;               ///< Cache hits during execution
  uint64_t cacheMisses = 0;             ///< Cache misses during execution

  // Enhanced PS4-specific fields
  PS4FiberPerformance performance; ///< Performance characteristics
  PS4FiberAffinity affinity;       ///< CPU affinity and scheduling hints
  uint64_t quantumUs = 10000; ///< Time quantum in microseconds (10ms default)
  uint64_t lastScheduledTime = 0;    ///< Last scheduled timestamp
  uint64_t totalWaitTime = 0;        ///< Total time spent waiting
  uint64_t averageExecutionTime = 0; ///< Average execution time per quantum
  float loadFactor = 0.0f;           ///< Load factor for adaptive scheduling
  bool isSystemFiber = false;        ///< System fiber flag
  uint32_t yieldCount = 0;           ///< Number of voluntary yields
  uint32_t preemptionCount = 0;      ///< Number of preemptions

#ifdef _WIN32
  LPVOID handle = nullptr; ///< Windows fiber handle
#else
  ucontext_t context; ///< POSIX ucontext
#endif
};

/**
 * @brief Core load information for load balancing.
 */
struct CoreLoadInfo {
  uint32_t activeFibers = 0;       ///< Number of active fibers on this core
  uint64_t totalExecutionTime = 0; ///< Total execution time for this core
  float loadFactor = 0.0f;         ///< Current load factor (0.0 to 1.0)
};

/**
 * @brief Manages PS4 fibers for cooperative multitasking.
 * @details Provides fiber creation, scheduling, and state management with
 *          support for priority, round-robin, and adaptive scheduling policies.
 */
class FiberManager {
public:
  /**
   * @brief Constructs a FiberManager instance.
   */
  FiberManager();

  /**
   * @brief Destructor, cleaning up resources.
   */
  ~FiberManager();

  /**
   * @brief Initializes the fiber manager.
   * @return True on success, false on failure.
   */
  bool Initialize();

  /**
   * @brief Shuts down the fiber manager, releasing resources.
   */
  void Shutdown();

  /**
   * @brief Creates a new fiber.
   * @param name Fiber name.
   * @param function Entry point function.
   * @param arg Argument passed to function.
   * @param priority Priority (0=highest, 255=lowest).
   * @param stackSize Stack size in bytes.
   * @return Fiber ID, or 0 on failure.
   */
  uint64_t CreateFiber(const std::string &name, FiberFunction function,
                       uint64_t arg, uint8_t priority, uint64_t stackSize);

  /**
   * @brief Deletes a fiber.
   * @param fiberId Fiber ID.
   * @return True on success, false if invalid or current fiber.
   */
  bool DeleteFiber(uint64_t fiberId);

  /**
   * @brief Switches to a specified fiber.
   * @param fiberId Fiber ID.
   * @return True on success, false if invalid.
   */
  bool SwitchToFiber(uint64_t fiberId);

  /**
   * @brief Suspends the current fiber.
   * @return True on success, false if not in a fiber.
   */
  bool SuspendFiber();

  /**
   * @brief Resumes a suspended fiber.
   * @param fiberId Fiber ID.
   * @return True on success, false if invalid.
   */
  bool ResumeFiber(uint64_t fiberId);

  /**
   * @brief Yields the current fiber to allow others to run.
   */
  void YieldFiber();

  /**
   * @brief Waits for a fiber to complete.
   * @param fiberId Fiber ID to wait for.
   * @param timeoutUs Timeout in microseconds (0 for infinite).
   * @return True if waited successfully, false if invalid or timed out.
   */
  bool WaitForFiber(uint64_t fiberId, uint64_t timeoutUs = 0);

  /**
   * @brief Gets the current fiber ID.
   * @return Current fiber ID, or 0 if in main context.
   */
  uint64_t GetCurrentFiberId() const;

  /**
   * @brief Retrieves fiber information.
   * @param fiberId Fiber ID.
   * @param name Output for fiber name.
   * @param state Output for fiber state.
   * @param priority Output for fiber priority.
   * @param executionTimeUs Output for execution time.
   * @param switchCount Output for switch count.
   * @return True if found, false otherwise.
   */
  bool GetFiberInfo(uint64_t fiberId, std::string &name, FiberState &state,
                    uint8_t &priority, uint64_t &executionTimeUs,
                    uint32_t &switchCount) const;

  /**
   * @brief Sets a fiber's priority.
   * @param fiberId Fiber ID.
   * @param priority New priority (0=highest, 255=lowest).
   * @return True on success, false if invalid.
   */
  bool SetFiberPriority(uint64_t fiberId, uint8_t priority);

  /**
   * @brief Gets the number of active fibers.
   * @return Number of fibers.
   */
  uint32_t GetFiberCount() const;

  /**
   * @brief Sets the scheduling policy.
   * @param policy Scheduling policy (PRIORITY, ROUND_ROBIN, ADAPTIVE).
   */
  void SetSchedulingPolicy(SchedulingPolicy policy);

  /**
   * @brief Saves the fiber manager state to a stream.
   * @param out Output stream.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the fiber manager state from a stream.
   * @param in Input stream.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Retrieves a fiber by ID (non-const).
   * @param fiberId Fiber ID.
   * @return Pointer to the fiber, or nullptr if not found.
   */
  Fiber *GetFiberById(uint64_t fiberId);

  /**
   * @brief Retrieves a fiber by ID (const).
   * @param fiberId Fiber ID.
   * @return Const pointer to the fiber, or nullptr if not found.
   */
  const Fiber *GetFiberById(uint64_t fiberId) const;

  /**
   * @brief Enhanced PS4-specific scheduling methods.
   */
  bool SetFiberAffinity(uint64_t fiberId, uint8_t affinityMask);
  bool SetFiberQuantum(uint64_t fiberId, uint64_t quantumUs);
  bool EnableStickyScheduling(uint64_t fiberId, bool enabled);
  void UpdatePerformanceMetrics(uint64_t fiberId);
  void OptimizeScheduling();
  float CalculateFiberLoadFactor(const Fiber *fiber) const;
  uint8_t SelectOptimalCore(const Fiber *fiber) const;
  bool ShouldPreempt(const Fiber *currentFiber,
                     const Fiber *candidateFiber) const;
  void BalanceLoad();

  /**
   * @brief Enhanced scheduling statistics for the fiber manager with atomic
   * members to prevent race conditions.
   */
  struct Stats {
    std::atomic<uint64_t> totalSwitches{0}; ///< Total context switches
    std::atomic<uint64_t> totalExecutionTimeUs{
        0}; ///< Total execution time (microseconds)
    std::atomic<uint64_t> fiberCreations{0};  ///< Number of fibers created
    std::atomic<uint64_t> fiberDeletions{0};  ///< Number of fibers deleted
    std::atomic<uint64_t> suspensions{0};     ///< Number of suspensions
    std::atomic<uint64_t> resumptions{0};     ///< Number of resumptions
    std::atomic<uint64_t> yields{0};          ///< Number of yields
    std::atomic<uint64_t> waits{0};           ///< Number of wait operations
    std::atomic<uint64_t> priorityChanges{0}; ///< Number of priority changes
    std::atomic<uint64_t> policyChanges{0};   ///< Number of policy changes
    std::atomic<uint64_t> preemptions{0};     ///< Number of preemptions
    std::atomic<uint64_t> loadBalanceOperations{0}; ///< Load balance operations
    std::atomic<uint64_t> affinityChanges{0};       ///< CPU affinity changes
    std::atomic<uint64_t> quantumAdjustments{0}; ///< Time quantum adjustments
    std::atomic<uint64_t> performanceUpdates{0}; ///< Performance metric updates
    std::atomic<float> averageLoadFactor{0.0f};  ///< Average system load factor
    std::atomic<float> cacheLocalityRatio{0.0f}; ///< Cache locality ratio
    std::atomic<uint64_t> contextSwitchOverheadUs{
        0}; ///< Context switch overhead
    std::atomic<uint64_t> totalLatencyUs{
        0}; ///< Total scheduling latency (microseconds)
    std::atomic<uint64_t> cacheHits{0};   ///< Cache hits during scheduling
    std::atomic<uint64_t> cacheMisses{0}; ///< Cache misses during scheduling

    // Copy constructor for atomic members
    Stats() = default;
    Stats(const Stats &other)
        : totalSwitches(other.totalSwitches.load()),
          totalExecutionTimeUs(other.totalExecutionTimeUs.load()),
          fiberCreations(other.fiberCreations.load()),
          fiberDeletions(other.fiberDeletions.load()),
          suspensions(other.suspensions.load()),
          resumptions(other.resumptions.load()), yields(other.yields.load()),
          waits(other.waits.load()),
          priorityChanges(other.priorityChanges.load()),
          policyChanges(other.policyChanges.load()),
          preemptions(other.preemptions.load()),
          loadBalanceOperations(other.loadBalanceOperations.load()),
          affinityChanges(other.affinityChanges.load()),
          quantumAdjustments(other.quantumAdjustments.load()),
          performanceUpdates(other.performanceUpdates.load()),
          averageLoadFactor(other.averageLoadFactor.load()),
          cacheLocalityRatio(other.cacheLocalityRatio.load()),
          contextSwitchOverheadUs(other.contextSwitchOverheadUs.load()),
          totalLatencyUs(other.totalLatencyUs.load()),
          cacheHits(other.cacheHits.load()),
          cacheMisses(other.cacheMisses.load()) {}

    // Assignment operator for atomic members
    Stats &operator=(const Stats &other) {
      if (this != &other) {
        totalSwitches.store(other.totalSwitches.load());
        totalExecutionTimeUs.store(other.totalExecutionTimeUs.load());
        fiberCreations.store(other.fiberCreations.load());
        fiberDeletions.store(other.fiberDeletions.load());
        suspensions.store(other.suspensions.load());
        resumptions.store(other.resumptions.load());
        yields.store(other.yields.load());
        waits.store(other.waits.load());
        priorityChanges.store(other.priorityChanges.load());
        policyChanges.store(other.policyChanges.load());
        preemptions.store(other.preemptions.load());
        loadBalanceOperations.store(other.loadBalanceOperations.load());
        affinityChanges.store(other.affinityChanges.load());
        quantumAdjustments.store(other.quantumAdjustments.load());
        performanceUpdates.store(other.performanceUpdates.load());
        averageLoadFactor.store(other.averageLoadFactor.load());
        cacheLocalityRatio.store(other.cacheLocalityRatio.load());
        contextSwitchOverheadUs.store(other.contextSwitchOverheadUs.load());
        totalLatencyUs.store(other.totalLatencyUs.load());
        cacheHits.store(other.cacheHits.load());
        cacheMisses.store(other.cacheMisses.load());
      }
      return *this;
    }
  };

  /**
   * @brief Retrieves scheduling statistics.
   * @return Current statistics.
   */
  Stats GetStats() const;

  /**
   * @brief Schedules the next fiber to run based on the current policy.
   */
  void ScheduleNextFiber();

private:
  /**
   * @brief Initializes a fiber's context and stack.
   * @param fiber Pointer to the fiber.
   */
  void InitializeFiberContext(Fiber *fiber);

  /**
   * @brief Trampoline function for fiber execution.
   * @param arg Fiber ID passed as an argument.
   */
  static void FiberTrampoline(uint64_t arg);

  /**
   * @brief Internal scheduling logic for selecting the next fiber.
   */
  void ScheduleNextFiberInternal();

  /**
   * @brief Validates a fiber state transition.
   * @param fiber Pointer to the fiber.
   * @param targetState Desired state.
   * @return True if valid, false otherwise.
   */
  bool ValidateFiberTransition(Fiber *fiber, FiberState targetState);

  /**
   * @brief Updates a fiber's statistics (execution time, cache stats).
   * @param fiber Pointer to the fiber.
   */
  void UpdateFiberStats(Fiber *fiber);

  std::unordered_map<uint64_t, std::unique_ptr<Fiber>>
      m_fibers;                           ///< Active fibers
  uint64_t m_nextFiberId = 1;             ///< Next fiber ID
  uint64_t m_currentFiberId = 0;          ///< Current fiber ID
  mutable std::shared_mutex m_fiberMutex; ///< Mutex for thread safety
  SchedulingPolicy m_policy = SchedulingPolicy::PRIORITY; ///< Current policy
  mutable Stats
      m_stats; ///< Scheduling statistics (allow updates in const methods)
  uint64_t m_lastScheduledId = 0; ///< Last scheduled fiber ID

  // Enhanced PS4-specific scheduling data
  struct CoreLoadInfo {
    uint64_t totalExecutionTime = 0;
    uint32_t activeFibers = 0;
    float loadFactor = 0.0f;
    uint64_t lastBalanceTime = 0;
  };
  std::array<CoreLoadInfo, 8> m_coreLoads; ///< Per-core load information

  uint64_t m_lastOptimizationTime = 0; ///< Last scheduling optimization time
  uint64_t m_optimizationIntervalUs = 100000; ///< Optimization interval (100ms)
  float m_loadBalanceThreshold = 0.8f;        ///< Load balance threshold
  bool m_adaptiveQuantum = true;              ///< Adaptive quantum adjustment
  uint64_t m_baseQuantumUs = 10000;           ///< Base quantum (10ms)
  uint64_t m_minQuantumUs = 1000;             ///< Minimum quantum (1ms)
  uint64_t m_maxQuantumUs = 50000;            ///< Maximum quantum (50ms)

#ifdef _WIN32
  LPVOID m_mainFiber = nullptr; ///< Main fiber handle
#else
  ucontext_t m_mainContext; ///< Main context
#endif
};

} // namespace ps4