// Copyright 2025 <Copyright Owner>

#pragma once

#include <cstdint>
#include <cstring>
#include <iostream>
#include <stdexcept>

namespace elf {
struct ELFException : std::runtime_error {
  explicit ELFException(const std::string &msg) : std::runtime_error(msg) {}
};

/**
 * @brief ELF identification indices for e_ident array.
 */
enum {
  EI_MAG0 = 0,       ///< File identification byte 0 index
  EI_MAG1 = 1,       ///< File identification byte 1 index
  EI_MAG2 = 2,       ///< File identification byte 2 index
  EI_MAG3 = 3,       ///< File identification byte 3 index
  EI_CLASS = 4,      ///< File class byte index
  EI_DATA = 5,       ///< Data encoding byte index
  EI_VERSION = 6,    ///< File version byte index
  EI_OSABI = 7,      ///< OS ABI identification
  EI_ABIVERSION = 8, ///< ABI version
  EI_NIDENT = 16     ///< Size of e_ident[]
};

/**
 * @brief ELF magic number for file identification.
 */
static constexpr unsigned char ElfMagic[] = {0x7f, 'E', 'L', 'F'};
static constexpr size_t ElfMagicLen = sizeof(ElfMagic);

/**
 * @brief ELF file classes.
 */
enum {
  ELFCLASS32 = 1, ///< 32-bit objects
  ELFCLASS64 = 2  ///< 64-bit objects
};

/**
 * @brief ELF data encoding.
 */
enum {
  ELFDATA2LSB = 1, ///< Little-endian
  ELFDATA2MSB = 2  ///< Big-endian
};

/**
 * @brief ELF object file types.
 */
constexpr int ET_EXEC = 2; ///< Executable file
constexpr int ET_DYN = 3;  ///< Shared object file

/**
 * @brief ELF machine types.
 */
enum {
  EM_X86_64 = 62 ///< AMD x86-64 architecture
};

/**
 * @brief ELF program header types.
 */
constexpr int PT_LOAD = 1;    ///< Loadable segment
constexpr int PT_DYNAMIC = 2; ///< Dynamic linking information
constexpr int PT_PHDR = 6;    ///< Program header table

/**
 * @brief ELF program header flags.
 */
constexpr int PF_X = 1; ///< Execute permission
constexpr int PF_W = 2; ///< Write permission
constexpr int PF_R = 4; ///< Read permission

/**
 * @brief ELF section header types.
 */
enum {
  SHT_NULL = 0,                 ///< Inactive section
  SHT_PROGBITS = 1,             ///< Program data
  SHT_SYMTAB = 2,               ///< Symbol table
  SHT_STRTAB = 3,               ///< String table
  SHT_RELA = 4,                 ///< Relocation entries with addends
  SHT_REL = 9,                  ///< Relocation entries without addends
  SHT_DYNSYM = 11,              ///< Dynamic linker symbol table
  SHT_PS4_METADATA = 0x60000000 ///< PS4-specific metadata (custom)
};

/**
 * @brief ELF section header flags.
 */
enum {
  SHF_WRITE = 1,    ///< Writable section
  SHF_ALLOC = 2,    ///< Occupies memory during execution
  SHF_EXECINSTR = 4 ///< Executable instructions
};

/**
 * @brief ELF64 header structure.
 */
/**
 * @brief ELF64 header structure.
 */
struct Elf64_Ehdr_struct {
  unsigned char e_ident[16]; // fixed size 16
  uint16_t e_type;
  uint16_t e_machine;
  uint32_t e_version;
  uint64_t e_entry;
  uint64_t e_phoff;
  uint64_t e_shoff;
  uint32_t e_flags;
  uint16_t e_ehsize;
  uint16_t e_phentsize;
  uint16_t e_phnum;
  uint16_t e_shentsize;
  uint16_t e_shnum;
  uint16_t e_shstrndx;

  /**
   * @brief Validates the ELF64 header.
   * @return True if the header is a valid 64-bit ELF for x86_64, false otherwise.
   */
  bool isValidElf64() const {
    // Check magic number
    if (std::memcmp(e_ident, ElfMagic, ElfMagicLen) != 0) {
      return false;
    }
    // Check 64-bit class
    if (e_ident[EI_CLASS] != ELFCLASS64) {
      return false;
    }
    // Check data encoding (little-endian for PS4/x86_64)
    if (e_ident[EI_DATA] != ELFDATA2LSB) {
      return false;
    }
    // Check version
    if (e_ident[EI_VERSION] != 1 || e_version != 1) {
      return false;
    }
    // Check machine type (x86_64)
    if (e_machine != EM_X86_64) {
      return false;
    }
    // Check header size
    if (e_ehsize < sizeof(Elf64_Ehdr_struct)) {
      return false;
    }
    return true;
  }
};
typedef struct Elf64_Ehdr_struct Elf64_Ehdr;

/**
 * @brief ELF64 program header structure.
 */
struct Elf64_Phdr {
  uint32_t p_type;   ///< Segment type
  uint32_t p_flags;  ///< Segment flags
  uint64_t p_offset; ///< Segment file offset
  uint64_t p_vaddr;  ///< Segment virtual address
  uint64_t p_paddr;  ///< Segment physical address
  uint64_t p_filesz; ///< Segment size in file
  uint64_t p_memsz;  ///< Segment size in memory
  uint64_t p_align;  ///< Segment alignment

  /**
   * @brief Validates the program header.
   * @return True if valid, false otherwise.
   */
  bool isValid() const {
    return p_align == 0 || (p_align & (p_align - 1)) == 0; // Power of 2
  }

  /**
   * @brief Saves the program header to a stream.
   * @param out Output stream.
   */
  void Save(std::ostream &out) const {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    out.write(reinterpret_cast<const char *>(this), sizeof(*this));
  }

  /**
   * @brief Loads the program header from a stream.
   * @param in Input stream.
   */
  void Load(std::istream &in) {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      throw ELFException("Unsupported program header version: " +
                         std::to_string(version));
    }
    in.read(reinterpret_cast<char *>(this), sizeof(*this));
    if (!in.good()) {
      throw ELFException("Failed to read program header");
    }
  }
};

/**
 * @brief ELF64 section header structure.
 */
struct Elf64_Shdr {
  uint32_t sh_name;      ///< Section name (string table index)
  uint32_t sh_type;      ///< Section type
  uint64_t sh_flags;     ///< Section flags
  uint64_t sh_addr;      ///< Section virtual address
  uint64_t sh_offset;    ///< Section file offset
  uint64_t sh_size;      ///< Section size
  uint32_t sh_link;      ///< Link to another section
  uint32_t sh_info;      ///< Additional section information
  uint64_t sh_addralign; ///< Section alignment
  uint64_t sh_entsize;   ///< Entry size if section holds a table

  /**
   * @brief Validates the section header.
   * @return True if valid, false otherwise.
   */
  bool isValid() const {
    return sh_addralign == 0 ||
           (sh_addralign & (sh_addralign - 1)) == 0; // Power of 2
  }

  /**
   * @brief Saves the section header to a stream.
   * @param out Output stream.
   */
  void Save(std::ostream &out) const {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    out.write(reinterpret_cast<const char *>(this), sizeof(*this));
  }

  /**
   * @brief Loads the section header from a stream.
   * @param in Input stream.
   */
  void Load(std::istream &in) {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      throw ELFException("Unsupported section header version: " +
                         std::to_string(version));
    }
    in.read(reinterpret_cast<char *>(this), sizeof(*this));
    if (!in.good()) {
      throw ELFException("Failed to read section header");
    }
  }
};

/**
 * @brief ELF64 symbol structure.
 */
struct Elf64_Sym {
  uint32_t st_name;       ///< Symbol name (string table index)
  unsigned char st_info;  ///< Symbol type and binding
  unsigned char st_other; ///< Symbol visibility
  uint16_t st_shndx;      ///< Section index
  uint64_t st_value;      ///< Symbol value
  uint64_t st_size;       ///< Symbol size

  /**
   * @brief Saves the symbol to a stream.
   * @param out Output stream.
   */
  void Save(std::ostream &out) const {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    out.write(reinterpret_cast<const char *>(this), sizeof(*this));
  }

  /**
   * @brief Loads the symbol from a stream.
   * @param in Input stream.
   */
  void Load(std::istream &in) {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      throw ELFException("Unsupported symbol version: " +
                         std::to_string(version));
    }
    in.read(reinterpret_cast<char *>(this), sizeof(*this));
    if (!in.good()) {
      throw ELFException("Failed to read symbol");
    }
  }
};

/**
 * @brief ELF64 dynamic section structure.
 */
struct Elf64_Dyn {
  int64_t d_tag; ///< Dynamic entry type
  union {
    uint64_t d_val; ///< Integer value
    uint64_t d_ptr; ///< Address value
  } d_un;

  /**
   * @brief Saves the dynamic entry to a stream.
   * @param out Output stream.
   */
  void Save(std::ostream &out) const {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    out.write(reinterpret_cast<const char *>(this), sizeof(*this));
  }

  /**
   * @brief Loads the dynamic entry from a stream.
   * @param in Input stream.
   */
  void Load(std::istream &in) {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      throw ELFException("Unsupported dynamic entry version: " +
                         std::to_string(version));
    }
    in.read(reinterpret_cast<char *>(this), sizeof(*this));
    if (!in.good()) {
      throw ELFException("Failed to read dynamic entry");
    }
  }
};

/**
 * @brief Dynamic section tags.
 */
constexpr int64_t DT_NULL = 0;     ///< End of dynamic section
constexpr int64_t DT_NEEDED = 1;   ///< Name of needed library
constexpr int64_t DT_PLTRELSZ = 2; ///< Size of PLT relocations
constexpr int64_t DT_PLTGOT = 3;   ///< PLT GOT address
constexpr int64_t DT_STRTAB = 5;   ///< String table address
constexpr int64_t DT_SYMTAB = 6;   ///< Symbol table address
constexpr int64_t DT_RELA = 7;     ///< Relocation table address
constexpr int64_t DT_RELASZ = 8;   ///< Relocation table size
constexpr int64_t DT_RELAENT = 9;  ///< Relocation entry size
constexpr int64_t DT_STRSZ = 10;   ///< String table size
constexpr int64_t DT_SYMENT = 11;  ///< Symbol entry size
constexpr int64_t DT_PLTREL = 20;  ///< PLT relocation type
constexpr int64_t DT_JMPREL = 23;  ///< PLT relocation table address

#define ELF64_ST_BIND(val) ((val) >> 4)
#define ELF64_ST_TYPE(val) ((val) & 0xf)
#define ELF64_ST_INFO(bind, type) (((bind) << 4) | ((type) & 0xf))

/**
 * @brief Symbol binding types.
 */
constexpr int STB_LOCAL = 0;  ///< Local symbol
constexpr int STB_GLOBAL = 1; ///< Global symbol
constexpr int STB_WEAK = 2;   ///< Weak symbol

/**
 * @brief Symbol types.
 */
enum {
  STT_NOTYPE = 0, ///< Unspecified type
  STT_OBJECT = 1, ///< Data object
  STT_FUNC = 2,   ///< Function
  STT_SECTION = 3 ///< Section
};

/**
 * @brief ELF64 relocation entry (without addend).
 */
struct Elf64_Rel {
  uint64_t r_offset; ///< Relocation offset
  uint64_t r_info;   ///< Relocation type and symbol index

  /**
   * @brief Saves the relocation entry to a stream.
   * @param out Output stream.
   */
  void Save(std::ostream &out) const {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    out.write(reinterpret_cast<const char *>(this), sizeof(*this));
  }

  /**
   * @brief Loads the relocation entry from a stream.
   * @param in Input stream.
   */
  void Load(std::istream &in) {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      throw ELFException("Unsupported relocation version: " +
                         std::to_string(version));
    }
    in.read(reinterpret_cast<char *>(this), sizeof(*this));
    if (!in.good()) {
      throw ELFException("Failed to read relocation entry");
    }
  }
};

/**
 * @brief ELF64 relocation entry (with addend).
 */
struct Elf64_Rela {
  uint64_t r_offset; ///< Relocation offset
  uint64_t r_info;   ///< Relocation type and symbol index
  int64_t r_addend;  ///< Relocation addend

  /**
   * @brief Saves the relocation entry to a stream.
   * @param out Output stream.
   */
  void Save(std::ostream &out) const {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    out.write(reinterpret_cast<const char *>(this), sizeof(*this));
  }

  /**
   * @brief Loads the relocation entry from a stream.
   * @param in Input stream.
   */
  void Load(std::istream &in) {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      throw ELFException("Unsupported relocation version: " +
                         std::to_string(version));
    }
    in.read(reinterpret_cast<char *>(this), sizeof(*this));
    if (!in.good()) {
      throw ELFException("Failed to read relocation entry");
    }
  }
};

#define ELF64_R_SYM(info) ((info) >> 32)
#define ELF64_R_TYPE(info) ((info) & 0xffffffffL)
#define ELF64_R_INFO(sym, type) (((uint64_t)(sym) << 32) + (type))

/**
 * @brief Standard x86_64 relocation types.
 */
constexpr int R_X86_64_NONE = 0;      ///< No relocation
constexpr int R_X86_64_64 = 1;        ///< Direct 64-bit
constexpr int R_X86_64_GLOB_DAT = 6;  ///< Global data
constexpr int R_X86_64_JUMP_SLOT = 7; ///< Jump slot
constexpr int R_X86_64_RELATIVE = 8;  ///< Relative relocation
constexpr int R_X86_64_PC32 = 2;      ///< PC-relative 32-bit
constexpr int R_X86_64_COPY = 5;      ///< Copy symbol

/**
 * @brief PS4-specific relocation types.
 */
constexpr int R_X86_64_PS4_64 = 0x60000000;       ///< PS4 direct 64-bit
constexpr int R_X86_64_PS4_GLOB_DAT = 0x60000001; ///< PS4 global data
constexpr int R_X86_64_PS4_RELATIVE = 0x60000002; ///< PS4 relative relocation
} // namespace elf