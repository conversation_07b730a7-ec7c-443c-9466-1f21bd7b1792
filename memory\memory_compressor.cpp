#include "memory_compressor.h"
#include <algorithm>
#include <array>
#include <chrono>
#include <cstring>
#include <functional>
#include <iostream>
#include <spdlog/spdlog.h>
#include <thread>

// Third-party compression libraries
#include "zlib.h"

// If LZ4 is available
#ifdef HAVE_LZ4
#include "lz4.h"
#endif

// If ZSTD is available
#ifdef HAVE_ZSTD
#include "zstd.h"
#endif

namespace ps4 {

MemoryCompressor::MemoryCompressor(CompressionAlgorithm algorithm,
                                   CompressionPolicy policy)
    : m_algorithm(algorithm), m_policy(policy) {
  ResetStats();
}

MemoryCompressor::~MemoryCompressor() noexcept {
  // Clean up compressed pages
  m_compressedPages.clear();
}

bool MemoryCompressor::Initialize() {
  // Clear existing compressed pages
  ClearAllCompressedPages();

  // Reset stats
  ResetStats();
  spdlog::info("Memory compressor initialized with algorithm {} and policy {}",
               static_cast<int>(m_algorithm), static_cast<int>(m_policy));
  return true;
}

bool MemoryCompressor::CompressPage(const uint8_t *pageData, size_t pageSize,
                                    uint64_t &outCompressedId) {
  if (!pageData || pageSize == 0) {
#ifdef DEBUG_COMPRESSOR
    spdlog::error("Invalid input for compression: null data or zero size");
#endif
    return false;
  }

  // Quick check for data entropy to avoid compressing uncompressible data
  if (!IsCompressible(pageData, pageSize)) {
#ifdef DEBUG_COMPRESSOR
    spdlog::info("Skipping compression due to high entropy data");
#endif
    return false;
  }

  std::vector<uint8_t> compressed;
  if (!CompressData(pageData, pageSize, compressed)) {
#ifdef DEBUG_COMPRESSOR
    spdlog::error("Compression failed for page data");
#endif
    return false;
  }

  // Calculate compression ratio
  double ratio =
      static_cast<double>(compressed.size()) / static_cast<double>(pageSize);

  // Only store the compressed page if it actually saves space
  if (ratio >= 0.95) { // If compression doesn't save at least 5%, don't bother
#ifdef DEBUG_COMPRESSOR
    spdlog::info("Compression ratio {} not beneficial, skipping", ratio);
#endif
    return false;
  }

  // Generate a new ID for the compressed page
  uint64_t compressedId = GenerateCompressedId();

  // Store the size before moving
  size_t compressedSize = compressed.size();

  // Store the compressed page
  {
    std::lock_guard<std::mutex> lock(m_compressedPagesMutex);
    m_compressedPages[compressedId] = std::move(compressed);
  }

  // Update stats
  {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    m_stats.pagesCompressed++;
    m_stats.totalBytesOriginal += pageSize;
    m_stats.totalBytesCompressed += compressedSize;
    m_stats.totalBytesSaved += (pageSize - compressedSize);
    m_stats.compressionRatio =
        static_cast<double>(m_stats.totalBytesCompressed) /
        static_cast<double>(m_stats.totalBytesOriginal);
    m_stats.compressionEfficiency =
        static_cast<double>(m_stats.totalBytesSaved) /
        static_cast<double>(m_stats.totalBytesOriginal);
  }

  outCompressedId = compressedId;
  return true;
}

bool MemoryCompressor::DecompressPage(uint64_t compressedId,
                                      uint8_t *outPageData, size_t pageSize) {
  if (!outPageData || pageSize == 0) {
#ifdef DEBUG_COMPRESSOR
    spdlog::error("Invalid output buffer for decompression: null or zero size");
#endif
    return false;
  }

  std::vector<uint8_t> compressed;

  // Retrieve the compressed page
  {
    std::lock_guard<std::mutex> lock(m_compressedPagesMutex);
    auto it = m_compressedPages.find(compressedId);
    if (it == m_compressedPages.end()) {
#ifdef DEBUG_COMPRESSOR
      spdlog::error("Compressed page ID {} not found", compressedId);
#endif
      return false;
    }
    compressed = it->second;
  }

  // Decompress the page
  if (!DecompressData(compressed.data(), compressed.size(), outPageData,
                      pageSize)) {
#ifdef DEBUG_COMPRESSOR
    spdlog::error("Decompression failed for page ID {}", compressedId);
#endif
    return false;
  }

  // Update stats
  {
    std::lock_guard<std::mutex> lock(m_statsMutex);
    m_stats.pagesDecompressed++;
  }

  return true;
}

bool MemoryCompressor::FreeCompressedPage(uint64_t compressedId) {
  std::lock_guard<std::mutex> lock(m_compressedPagesMutex);
  return m_compressedPages.erase(compressedId) > 0;
}

void MemoryCompressor::SetCompressionAlgorithm(CompressionAlgorithm algorithm) {
  m_algorithm = algorithm;
}

void MemoryCompressor::SetCompressionPolicy(CompressionPolicy policy) {
  m_policy = policy;
}

bool MemoryCompressor::ShouldCompressPage(
    uint64_t accessCount,
    const std::chrono::steady_clock::time_point &lastAccessTime) const {
  uint64_t accessThreshold, idleTimeMs;
  GetPolicyThresholds(accessThreshold, idleTimeMs);

  // Check access count
  if (accessCount > accessThreshold) {
    return false;
  }

  // Check idle time
  auto now = std::chrono::steady_clock::now();
  auto idleTime = std::chrono::duration_cast<std::chrono::milliseconds>(
                      now - lastAccessTime)
                      .count();

  return idleTime >= idleTimeMs;
}

CompressionStats MemoryCompressor::GetStats() const {
  std::lock_guard<std::mutex> lock(m_statsMutex);
  return m_stats;
}

void MemoryCompressor::ResetStats() {
  std::lock_guard<std::mutex> lock(m_statsMutex);
  m_stats = {};
  m_stats.compressionRatio = 1.0; // Default to 1:1 ratio
}

void MemoryCompressor::SetCustomThresholds(uint64_t accessThreshold,
                                           uint64_t idleTimeMs) {
  m_customAccessThreshold = accessThreshold;
  m_customIdleTimeMs = idleTimeMs;
}

void MemoryCompressor::GetPolicyThresholds(uint64_t &outAccessThreshold,
                                           uint64_t &outIdleTimeMs) const {
  switch (m_policy) {
  case POLICY_AGGRESSIVE:
    outAccessThreshold = 100;
    outIdleTimeMs = 1000; // 1 second
    break;

  case POLICY_BALANCED:
    outAccessThreshold = 50;
    outIdleTimeMs = 3000; // 3 seconds
    break;

  case POLICY_CONSERVATIVE:
    outAccessThreshold = 20;
    outIdleTimeMs = 10000; // 10 seconds
    break;

  case POLICY_CUSTOM:
    outAccessThreshold = m_customAccessThreshold;
    outIdleTimeMs = m_customIdleTimeMs;
    break;

  default:
    outAccessThreshold = 50;
    outIdleTimeMs = 3000;
  }
}

uint64_t MemoryCompressor::GenerateCompressedId() {
  return m_nextCompressedId.fetch_add(1, std::memory_order_relaxed);
}

bool MemoryCompressor::CompressData(const uint8_t *data, size_t size,
                                    std::vector<uint8_t> &outCompressed) {
  switch (m_algorithm) {
  case ALGO_LZ4:
#ifdef HAVE_LZ4
  {
    // Calculate max compressed size
    int maxCompressedSize = LZ4_compressBound(static_cast<int>(size));
    outCompressed.resize(maxCompressedSize);

    // Compress data
    int compressedSize =
        LZ4_compress_default(reinterpret_cast<const char *>(data),
                             reinterpret_cast<char *>(outCompressed.data()),
                             static_cast<int>(size), maxCompressedSize);

    if (compressedSize <= 0) {
      return false;
    }

    // Resize to actual compressed size
    outCompressed.resize(compressedSize);
    return true;
  }
#else
    // Fall back to ZLIB if LZ4 is not available
    m_algorithm = ALGO_ZLIB;
    [[fallthrough]];
#endif

  case ALGO_ZSTD:
#ifdef HAVE_ZSTD
  {
    // Calculate max compressed size
    size_t maxCompressedSize = ZSTD_compressBound(size);
    outCompressed.resize(maxCompressedSize);

    // Compress data
    size_t compressedSize = ZSTD_compress(
        outCompressed.data(), maxCompressedSize, data, size,
        3 // Compression level (1-19, higher = better compression but slower)
    );

    if (ZSTD_isError(compressedSize)) {
      return false;
    }

    // Resize to actual compressed size
    outCompressed.resize(compressedSize);
    return true;
  }
#else
    // Fall back to ZLIB if ZSTD is not available
    m_algorithm = ALGO_ZLIB;
    [[fallthrough]];
#endif

  case ALGO_ZLIB: {
    // Calculate max compressed size (worst case)
    uLong maxCompressedSize = compressBound(static_cast<uLong>(size));
    outCompressed.resize(maxCompressedSize);

    // Compress data
    uLong compressedSize = static_cast<uLong>(outCompressed.size());
    int result = compress(outCompressed.data(), &compressedSize, data,
                          static_cast<uLong>(size));

    if (result != Z_OK) {
      return false;
    }

    // Resize to actual compressed size
    outCompressed.resize(compressedSize);
    return true;
  }

  case ALGO_NONE: {
    // No compression, just copy the data
    outCompressed.resize(size);
    std::memcpy(outCompressed.data(), data, size);
    return true;
  }

  default:
    return false;
  }
}

bool MemoryCompressor::DecompressData(const uint8_t *compressedData,
                                      size_t compressedSize, uint8_t *outData,
                                      size_t outSize) {
  switch (m_algorithm) {
  case ALGO_LZ4:
#ifdef HAVE_LZ4
  {
    // Decompress data
    int decompressedSize = LZ4_decompress_safe(
        reinterpret_cast<const char *>(compressedData),
        reinterpret_cast<char *>(outData), static_cast<int>(compressedSize),
        static_cast<int>(outSize));

    return decompressedSize > 0 &&
           static_cast<size_t>(decompressedSize) == outSize;
  }
#else
    // Fall back to ZLIB if LZ4 is not available
    m_algorithm = ALGO_ZLIB;
    [[fallthrough]];
#endif

  case ALGO_ZSTD:
#ifdef HAVE_ZSTD
  {
    // Decompress data
    size_t decompressedSize =
        ZSTD_decompress(outData, outSize, compressedData, compressedSize);

    return !ZSTD_isError(decompressedSize) && decompressedSize == outSize;
  }
#else
    // Fall back to ZLIB if ZSTD is not available
    m_algorithm = ALGO_ZLIB;
    [[fallthrough]];
#endif

  case ALGO_ZLIB: {
    // Decompress data
    uLong destLen = static_cast<uLong>(outSize);
    int result = uncompress(outData, &destLen, compressedData,
                            static_cast<uLong>(compressedSize));

    return result == Z_OK && destLen == outSize;
  }

  case ALGO_NONE: {
    // No compression, just copy the data
    if (compressedSize != outSize) {
      return false;
    }
    std::memcpy(outData, compressedData, outSize);
    return true;
  }

  default:
    return false;
  }
}

void MemoryCompressor::ClearAllCompressedPages() {
  std::lock_guard<std::mutex> lock(m_compressedPagesMutex);
  m_compressedPages.clear();
  spdlog::debug("All compressed pages cleared");
}

size_t MemoryCompressor::GetCompressedPageCount() const {
  std::lock_guard<std::mutex> lock(m_compressedPagesMutex);
  return m_compressedPages.size();
}

double MemoryCompressor::GetCompressionRatio() const {
  std::lock_guard<std::mutex> lock(m_statsMutex);
  return m_stats.compressionRatio;
}

double MemoryCompressor::GetCompressionEfficiency() const {
  std::lock_guard<std::mutex> lock(m_statsMutex);
  return m_stats.compressionEfficiency;
}

void MemoryCompressor::LogStats() const {
  auto stats = GetStats();
  spdlog::info(
      "Compression Stats - PagesCompressed: {}, PagesDecompressed: {}, "
      "TotalOrig: {}, TotalComp: {}, Saved: {}, Ratio: {:.2f}, Efficiency: "
      "{:.2f}",
      stats.pagesCompressed, stats.pagesDecompressed, stats.totalBytesOriginal,
      stats.totalBytesCompressed, stats.totalBytesSaved, stats.compressionRatio,
      stats.compressionEfficiency);
}

void MemoryCompressor::RegisterPageAccess(uint64_t pageAddress,
                                          const uint8_t *pageData,
                                          size_t pageSize) {
  using Clock = std::chrono::steady_clock;
  auto now = Clock::now();

  // Update access info
  {
    std::lock_guard<std::mutex> lock(m_pageAccessMutex);
    auto &info = m_pageAccessInfo[pageAddress];
    info.accessCount++;
    info.lastAccessTime = now;
  }

  // Check if page is already compressed
  uint64_t compressedId = 0;
  bool isCompressed = false;
  {
    std::lock_guard<std::mutex> lock(m_pageToCompressedMutex);
    auto it = m_pageToCompressedId.find(pageAddress);
    if (it != m_pageToCompressedId.end()) {
      compressedId = it->second;
      isCompressed = true;
    }
  }

  // Decide to compress or decompress
  {
    std::lock_guard<std::mutex> lock(m_pageAccessMutex);
    auto &info = m_pageAccessInfo[pageAddress];
    if (!isCompressed) {
      if (ShouldCompressPage(info.accessCount, info.lastAccessTime)) {
        uint64_t newId;
        if (CompressPage(pageData, pageSize, newId)) {
          std::lock_guard<std::mutex> lock2(m_pageToCompressedMutex);
          m_pageToCompressedId[pageAddress] = newId;
          spdlog::debug("Compressed page 0x{:x} -> id {}", pageAddress, newId);
        }
      }
    } else {
      // Page is compressed - if now hot, decompress
      if (!ShouldCompressPage(info.accessCount, info.lastAccessTime)) {
        std::vector<uint8_t> buffer(pageSize);
        if (DecompressPage(compressedId, buffer.data(), pageSize)) {
          std::lock_guard<std::mutex> lock2(m_pageToCompressedMutex);
          m_pageToCompressedId.erase(pageAddress);
          FreeCompressedPage(compressedId);
          spdlog::debug("Decompressed page 0x{:x} from id {}", pageAddress,
                        compressedId);
        }
      }
    }
  }
}

bool MemoryCompressor::IsCompressible(const uint8_t *data, size_t size) {
  if (size < 16)
    return true; // Too small to judge

  // Simple entropy check: count unique bytes in a sample
  std::array<bool, 256> byteSeen = {false};
  size_t sampleSize = std::min(size, size_t(256));
  size_t uniqueCount = 0;

  for (size_t i = 0; i < sampleSize; ++i) {
    if (!byteSeen[data[i]]) {
      byteSeen[data[i]] = true;
      uniqueCount++;
      if (uniqueCount > sampleSize / 2) {
        // High entropy detected, likely not compressible
        return false;
      }
    }
  }
  return true;
}

} // namespace ps4