#include "gnm_state.h"
#include <chrono>
#include <iomanip>
#include <spdlog/spdlog.h>
#include <sstream>
#include <stdexcept>
#include <string>

namespace ps4 {

GNMRegisterState::GNMRegisterState() : m_stats{} {
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  // CRITICAL FIX: Ensure all statistics are properly initialized to zero
  m_stats = {};
  for (auto &stageRegs : m_shaderRegisters) {
    stageRegs.fill(0);
  }
  m_contextRegisters.fill(0);
  m_configRegisters.fill(0);
  m_userRegisters.fill(0);
  spdlog::info("GNMRegisterState constructed with zero-initialized stats");
}

GNMRegisterState::~GNMRegisterState() {
  Shutdown();
  spdlog::info("GNMRegisterState destroyed");
}

bool GNMRegisterState::Initialize() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  try {
    InitRegisterNames();
    m_stats = GNMRegisterStateStats();
    m_registerCache.clear();
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    m_stats.cacheHits++;
    spdlog::info("GNMRegisterState initialized, latency={}us", latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GNMRegisterState initialization failed: {}", e.what());
    throw GNMException("Initialization failed: " + std::string(e.what()));
  }
}

void GNMRegisterState::Shutdown() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  try {
    m_shaderRegNames.clear();
    m_contextRegNames.clear();
    m_configRegNames.clear();
    m_userRegNames.clear();
    m_registerCache.clear();
    m_stats = GNMRegisterStateStats();
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    m_stats.cacheHits++;
    spdlog::info("GNMRegisterState shutdown, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GNMRegisterState shutdown failed: {}", e.what());
  }
}

void GNMRegisterState::SetRegisterChangeCallback(
    const RegisterChangeCallback &callback) {
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  m_registerChangeCallback = callback;
  spdlog::debug("GNMRegisterState: Register change callback set");
}

uint32_t GNMRegisterState::GetShaderRegister(uint32_t stage,
                                             uint32_t offset) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    uint32_t value;
    uint64_t cacheKey = (static_cast<uint64_t>(stage) << 32) | offset;
    if (GetCachedRegister(GNMRegisterType::SHADER_REG, stage, offset, value)) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("GetShaderRegister: stage={}, offset=0x{:x}, value=0x{:x}, "
                    "cached, latency={}us",
                    stage, offset, value, latency);
      return value;
    }
    if (!ValidateRegister(GNMRegisterType::SHADER_REG, stage, offset, 0)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw GNMException(
          "Invalid shader register access: stage=" + std::to_string(stage) +
          ", offset=0x" + std::to_string(offset));
    }
    value = m_shaderRegisters[stage][offset];
    RegisterCacheEntry entry{GNMRegisterType::SHADER_REG, stage, offset, value};
    lock.unlock();
    std::unique_lock<std::shared_mutex> writeLock(m_regMutex);
    m_registerCache[cacheKey] = entry;
    m_stats.accessCount++;
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetShaderRegister: stage={}, offset=0x{:x}, value=0x{:x}, "
                  "latency={}us",
                  stage, offset, value, latency);
    return value;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetShaderRegister failed: {}", e.what());
    throw GNMException("GetShaderRegister failed: " + std::string(e.what()));
  }
}

void GNMRegisterState::SetShaderRegister(uint32_t stage, uint32_t offset,
                                         uint32_t value) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  try {
    if (!ValidateRegister(GNMRegisterType::SHADER_REG, stage, offset, value)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw GNMException(
          "Invalid shader register write: stage=" + std::to_string(stage) +
          ", offset=0x" + std::to_string(offset) + ", value=0x" +
          std::to_string(value));
    }
    m_shaderRegisters[stage][offset] = value;
    uint64_t cacheKey = (static_cast<uint64_t>(stage) << 32) | offset;
    RegisterCacheEntry entry{GNMRegisterType::SHADER_REG, stage, offset, value};
    m_registerCache[cacheKey] = entry;
    m_stats.accessCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    auto it = m_shaderRegNames.find(offset);
    if (it != m_shaderRegNames.end()) {
      spdlog::trace(
          "SetShaderRegister: stage={}, {} (0x{:x})=0x{:x}, latency={}us",
          stage, it->second, offset, value, latency);
    } else {
      spdlog::trace("SetShaderRegister: stage={}, offset=0x{:x}, value=0x{:x}, "
                    "latency={}us",
                    stage, offset, value, latency);
    }
    // CRITICAL FIX: Execute callback without holding mutex to prevent deadlock
    RegisterChangeCallback callback = m_registerChangeCallback;
    lock.unlock();

    // Notify PS4GPU of shader register change for rendering updates
    if (callback) {
      callback(GNMRegisterType::SHADER_REG, stage, offset, value);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SetShaderRegister failed: {}", e.what());
    throw GNMException("SetShaderRegister failed: " + std::string(e.what()));
  }
}

uint32_t GNMRegisterState::GetContextRegister(uint32_t offset) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    uint32_t value;
    if (GetCachedRegister(GNMRegisterType::CONTEXT_REG, 0, offset, value)) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("GetContextRegister: offset=0x{:x}, value=0x{:x}, cached, "
                    "latency={}us",
                    offset, value, latency);
      return value;
    }
    if (!ValidateRegister(GNMRegisterType::CONTEXT_REG, 0, offset, 0)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw GNMException("Invalid context register access: offset=0x" +
                         std::to_string(offset));
    }
    value = m_contextRegisters[offset];
    RegisterCacheEntry entry{GNMRegisterType::CONTEXT_REG, 0, offset, value};
    lock.unlock();
    std::unique_lock<std::shared_mutex> writeLock(m_regMutex);
    m_registerCache[offset] = entry;
    m_stats.accessCount++;
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "GetContextRegister: offset=0x{:x}, value=0x{:x}, latency={}us", offset,
        value, latency);
    return value;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetContextRegister failed: {}", e.what());
    throw GNMException("GetContextRegister failed: " + std::string(e.what()));
  }
}

void GNMRegisterState::SetContextRegister(uint32_t offset, uint32_t value) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  try {
    if (!ValidateRegister(GNMRegisterType::CONTEXT_REG, 0, offset, value)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw GNMException("Invalid context register write: offset=0x" +
                         std::to_string(offset) + ", value=0x" +
                         std::to_string(value));
    }
    m_contextRegisters[offset] = value;
    RegisterCacheEntry entry{GNMRegisterType::CONTEXT_REG, 0, offset, value};
    m_registerCache[offset] = entry;
    m_stats.accessCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    auto it = m_contextRegNames.find(offset);
    if (it != m_contextRegNames.end()) {
      spdlog::trace("SetContextRegister: {} (0x{:x})=0x{:x}, latency={}us",
                    it->second, offset, value, latency);
    } else {
      spdlog::trace(
          "SetContextRegister: offset=0x{:x}, value=0x{:x}, latency={}us",
          offset, value, latency);
    }
    // CRITICAL FIX: Execute callback without holding mutex to prevent deadlock
    RegisterChangeCallback callback = m_registerChangeCallback;
    lock.unlock();

    // Notify PS4GPU of context register change for rendering updates
    if (callback) {
      callback(GNMRegisterType::CONTEXT_REG, 0, offset, value);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SetContextRegister failed: {}", e.what());
    throw GNMException("SetContextRegister failed: " + std::string(e.what()));
  }
}

uint32_t GNMRegisterState::GetConfigRegister(uint32_t offset) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    uint32_t value;
    if (GetCachedRegister(GNMRegisterType::CONFIG_REG, 0, offset, value)) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("GetConfigRegister: offset=0x{:x}, value=0x{:x}, cached, "
                    "latency={}us",
                    offset, value, latency);
      return value;
    }
    if (!ValidateRegister(GNMRegisterType::CONFIG_REG, 0, offset, 0)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw GNMException("Invalid config register access: offset=0x" +
                         std::to_string(offset));
    }
    value = m_configRegisters[offset];
    RegisterCacheEntry entry{GNMRegisterType::CONFIG_REG, 0, offset, value};
    lock.unlock();
    std::unique_lock<std::shared_mutex> writeLock(m_regMutex);
    m_registerCache[offset] = entry;
    m_stats.accessCount++;
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "GetConfigRegister: offset=0x{:x}, value=0x{:x}, latency={}us", offset,
        value, latency);
    return value;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetConfigRegister failed: {}", e.what());
    throw GNMException("GetConfigRegister failed: " + std::string(e.what()));
  }
}

void GNMRegisterState::SetConfigRegister(uint32_t offset, uint32_t value) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  try {
    if (!ValidateRegister(GNMRegisterType::CONFIG_REG, 0, offset, value)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw GNMException("Invalid config register write: offset=0x" +
                         std::to_string(offset) + ", value=0x" +
                         std::to_string(value));
    }
    m_configRegisters[offset] = value;
    RegisterCacheEntry entry{GNMRegisterType::CONFIG_REG, 0, offset, value};
    m_registerCache[offset] = entry;
    m_stats.accessCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    auto it = m_configRegNames.find(offset);
    if (it != m_configRegNames.end()) {
      spdlog::trace("SetConfigRegister: {} (0x{:x})=0x{:x}, latency={}us",
                    it->second, offset, value, latency);
    } else {
      spdlog::trace(
          "SetConfigRegister: offset=0x{:x}, value=0x{:x}, latency={}us",
          offset, value, latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SetConfigRegister failed: {}", e.what());
    throw GNMException("SetConfigRegister failed: " + std::string(e.what()));
  }
}

uint32_t GNMRegisterState::GetUserRegister(uint32_t offset) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    uint32_t value;
    if (GetCachedRegister(GNMRegisterType::USER_REG, 0, offset, value)) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace(
          "GetUserRegister: offset=0x{:x}, value=0x{:x}, cached, latency={}us",
          offset, value, latency);
      return value;
    }
    if (!ValidateRegister(GNMRegisterType::USER_REG, 0, offset, 0)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw GNMException("Invalid user register access: offset=0x" +
                         std::to_string(offset));
    }
    value = m_userRegisters[offset];
    RegisterCacheEntry entry{GNMRegisterType::USER_REG, 0, offset, value};
    lock.unlock();
    std::unique_lock<std::shared_mutex> writeLock(m_regMutex);
    m_registerCache[offset] = entry;
    m_stats.accessCount++;
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetUserRegister: offset=0x{:x}, value=0x{:x}, latency={}us",
                  offset, value, latency);
    return value;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetUserRegister failed: {}", e.what());
    throw GNMException("GetUserRegister failed: " + std::string(e.what()));
  }
}

void GNMRegisterState::SetUserRegister(uint32_t offset, uint32_t value) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  try {
    if (!ValidateRegister(GNMRegisterType::USER_REG, 0, offset, value)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw GNMException("Invalid user register write: offset=0x" +
                         std::to_string(offset) + ", value=0x" +
                         std::to_string(value));
    }
    m_userRegisters[offset] = value;
    RegisterCacheEntry entry{GNMRegisterType::USER_REG, 0, offset, value};
    m_registerCache[offset] = entry;
    m_stats.accessCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    auto it = m_userRegNames.find(offset);
    if (it != m_userRegNames.end()) {
      spdlog::trace("SetUserRegister: {} (0x{:x})=0x{:x}, latency={}us",
                    it->second, offset, value, latency);
    } else {
      spdlog::trace(
          "SetUserRegister: offset=0x{:x}, value=0x{:x}, latency={}us", offset,
          value, latency);
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SetUserRegister failed: {}", e.what());
    throw GNMException("SetUserRegister failed: " + std::string(e.what()));
  }
}

uint32_t GNMRegisterState::GetShaderBase(uint32_t stage) const {
  auto start = std::chrono::steady_clock::now();
  try {
    if (stage >= SHADER_STAGES) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw GNMException("Invalid shader stage: " + std::to_string(stage));
    }
    constexpr uint32_t SHADER_BASE_OFFSET = 0x0;

    // CRITICAL FIX: Avoid recursive locking by accessing register directly
    std::shared_lock<std::shared_mutex> lock(m_regMutex);
    uint32_t value = m_shaderRegisters[stage][SHADER_BASE_OFFSET];
    m_stats.accessCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetShaderBase: stage={}, value=0x{:x}, latency={}us", stage,
                  value, latency);
    return value;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetShaderBase failed: {}", e.what());
    throw GNMException("GetShaderBase failed: " + std::string(e.what()));
  }
}

uint32_t GNMRegisterState::GetRenderTarget(uint32_t index) const {
  auto start = std::chrono::steady_clock::now();
  try {
    if (index >= 8) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw GNMException("Invalid render target index: " +
                         std::to_string(index));
    }
    constexpr uint32_t RENDER_TARGET_BASE_OFFSET = 0x100;

    // CRITICAL FIX: Avoid recursive locking by accessing register directly
    std::shared_lock<std::shared_mutex> lock(m_regMutex);
    uint32_t offset = RENDER_TARGET_BASE_OFFSET + index;
    if (offset >= MAX_CONTEXT_REGS) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw GNMException("Render target offset out of bounds: " + std::to_string(offset));
    }
    uint32_t value = m_contextRegisters[offset];
    m_stats.accessCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetRenderTarget: index={}, value=0x{:x}, latency={}us",
                  index, value, latency);
    return value;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetRenderTarget failed: {}", e.what());
    throw GNMException("GetRenderTarget failed: " + std::string(e.what()));
  }
}

void GNMRegisterState::GetViewport(float &x, float &y, float &width,
                                   float &height, float &minDepth,
                                   float &maxDepth) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    constexpr uint32_t VIEWPORT_X_OFFSET = 0x80;
    constexpr uint32_t VIEWPORT_Y_OFFSET = 0x81;
    constexpr uint32_t VIEWPORT_WIDTH_OFFSET = 0x82;
    constexpr uint32_t VIEWPORT_HEIGHT_OFFSET = 0x83;
    constexpr uint32_t VIEWPORT_MIN_DEPTH_OFFSET = 0x84;
    constexpr uint32_t VIEWPORT_MAX_DEPTH_OFFSET = 0x85;

    x = *reinterpret_cast<const float *>(
        &m_contextRegisters[VIEWPORT_X_OFFSET]);
    y = *reinterpret_cast<const float *>(
        &m_contextRegisters[VIEWPORT_Y_OFFSET]);
    width = *reinterpret_cast<const float *>(
        &m_contextRegisters[VIEWPORT_WIDTH_OFFSET]);
    height = *reinterpret_cast<const float *>(
        &m_contextRegisters[VIEWPORT_HEIGHT_OFFSET]);
    minDepth = *reinterpret_cast<const float *>(
        &m_contextRegisters[VIEWPORT_MIN_DEPTH_OFFSET]);
    maxDepth = *reinterpret_cast<const float *>(
        &m_contextRegisters[VIEWPORT_MAX_DEPTH_OFFSET]);

    m_stats.accessCount += 6;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetViewport: x={}, y={}, width={}, height={}, minDepth={}, "
                  "maxDepth={}, latency={}us",
                  x, y, width, height, minDepth, maxDepth, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetViewport failed: {}", e.what());
    throw GNMException("GetViewport failed: " + std::string(e.what()));
  }
}

bool GNMRegisterState::GetCachedRegister(GNMRegisterType type, uint32_t stage,
                                         uint32_t offset,
                                         uint32_t &value) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    uint64_t cacheKey = (type == GNMRegisterType::SHADER_REG)
                            ? (static_cast<uint64_t>(stage) << 32) | offset
                            : offset;
    auto it = m_registerCache.find(cacheKey);
    if (it != m_registerCache.end() && it->second.type == type &&
        it->second.stage == stage && it->second.offset == offset) {
      value = it->second.value;
      it->second.cacheHits++;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("GetCachedRegister: type={}, stage={}, offset=0x{:x}, "
                    "value=0x{:x}, hit, latency={}us",
                    static_cast<int>(type), stage, offset, value, latency);
      return true;
    }
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetCachedRegister: type={}, stage={}, offset=0x{:x}, miss, "
                  "latency={}us",
                  static_cast<int>(type), stage, offset, latency);
    return false;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetCachedRegister failed: {}", e.what());
    return false;
  }
}

void GNMRegisterState::ClearRegisterCache() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  try {
    m_registerCache.clear();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ClearRegisterCache: Cleared cache, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ClearRegisterCache failed: {}", e.what());
  }
}

std::string GNMRegisterState::DumpState(bool nonZeroOnly) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    std::stringstream ss;
    ss << "GNM Register State:\n";

    lock.unlock(); // Release mutex during string building
    for (uint32_t stage = 0; stage < SHADER_STAGES; stage++) {
      std::shared_lock<std::shared_mutex> readLock(m_regMutex);
      ss << "Shader Stage " << stage << " Registers:\n";
      for (uint32_t i = 0; i < MAX_SHADER_REGS; i++) {
        uint32_t value;
        if (GetCachedRegister(GNMRegisterType::SHADER_REG, stage, i, value) ||
            (value = m_shaderRegisters[stage][i], true)) {
          if (!nonZeroOnly || value != 0) {
            auto it = m_shaderRegNames.find(i);
            if (it != m_shaderRegNames.end()) {
              ss << "  " << it->second;
            } else {
              ss << "  Reg[0x" << std::hex << std::setw(4) << std::setfill('0')
                 << i << "]";
            }
            ss << " = 0x" << std::hex << std::setw(8) << std::setfill('0')
               << value << "\n";
          }
        }
      }
      readLock.unlock();
    }

    std::shared_lock<std::shared_mutex> readLock(m_regMutex);
    ss << "Context Registers:\n";
    for (uint32_t i = 0; i < MAX_CONTEXT_REGS; i++) {
      uint32_t value;
      if (GetCachedRegister(GNMRegisterType::CONTEXT_REG, 0, i, value) ||
          (value = m_contextRegisters[i], true)) {
        if (!nonZeroOnly || value != 0) {
          auto it = m_contextRegNames.find(i);
          if (it != m_contextRegNames.end()) {
            ss << "  " << it->second;
          } else {
            ss << "  Reg[0x" << std::hex << std::setw(4) << std::setfill('0')
               << i << "]";
          }
          ss << " = 0x" << std::hex << std::setw(8) << std::setfill('0')
             << value << "\n";
        }
      }
    }

    ss << "Config Registers:\n";
    for (uint32_t i = 0; i < MAX_CONFIG_REGS; i++) {
      uint32_t value;
      if (GetCachedRegister(GNMRegisterType::CONFIG_REG, 0, i, value) ||
          (value = m_configRegisters[i], true)) {
        if (!nonZeroOnly || value != 0) {
          auto it = m_configRegNames.find(i);
          if (it != m_configRegNames.end()) {
            ss << "  " << it->second;
          } else {
            ss << "  Reg[0x" << std::hex << std::setw(4) << std::setfill('0')
               << i << "]";
          }
          ss << " = 0x" << std::hex << std::setw(8) << std::setfill('0')
             << value << "\n";
        }
      }
    }

    ss << "User Registers:\n";
    for (uint32_t i = 0; i < MAX_USER_REGS; i++) {
      uint32_t value;
      if (GetCachedRegister(GNMRegisterType::USER_REG, 0, i, value) ||
          (value = m_userRegisters[i], true)) {
        if (!nonZeroOnly || value != 0) {
          auto it = m_userRegNames.find(i);
          if (it != m_userRegNames.end()) {
            ss << "  " << it->second;
          } else {
            ss << "  Reg[0x" << std::hex << std::setw(4) << std::setfill('0')
               << i << "]";
          }
          ss << " = 0x" << std::hex << std::setw(8) << std::setfill('0')
             << value << "\n";
        }
      }
    }
    readLock.unlock();

    std::unique_lock<std::shared_mutex> writeLock(m_regMutex);
    m_stats.accessCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("DumpState: nonZeroOnly={}, latency={}us", nonZeroOnly,
                  latency);
    return ss.str();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("DumpState failed: {}", e.what());
    throw GNMException("DumpState failed: " + std::string(e.what()));
  }
}

void GNMRegisterState::InitRegisterNames() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  try {
    m_shaderRegNames[0x0] = "SHADER_BASE_ADDR";
    m_shaderRegNames[0x1] = "SHADER_SIZE";
    m_shaderRegNames[0x2] = "SHADER_ENTRY_POINT";

    m_contextRegNames[0x80] = "VIEWPORT_X";
    m_contextRegNames[0x81] = "VIEWPORT_Y";
    m_contextRegNames[0x82] = "VIEWPORT_WIDTH";
    m_contextRegNames[0x83] = "VIEWPORT_HEIGHT";
    m_contextRegNames[0x84] = "VIEWPORT_MIN_DEPTH";
    m_contextRegNames[0x85] = "VIEWPORT_MAX_DEPTH";
    m_contextRegNames[0x100] = "RENDER_TARGET_0";
    m_contextRegNames[0x101] = "RENDER_TARGET_1";
    m_contextRegNames[0x102] = "RENDER_TARGET_2";
    m_contextRegNames[0x103] = "RENDER_TARGET_3";
    m_contextRegNames[0x104] = "RENDER_TARGET_4";
    m_contextRegNames[0x105] = "RENDER_TARGET_5";
    m_contextRegNames[0x106] = "RENDER_TARGET_6";
    m_contextRegNames[0x107] = "RENDER_TARGET_7";

    m_configRegNames[0x0] = "CONFIG_REGISTER_0";
    m_userRegNames[0x0] = "USER_REGISTER_0";

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("InitRegisterNames: shader={}, context={}, config={}, "
                  "user={}, latency={}us",
                  m_shaderRegNames.size(), m_contextRegNames.size(),
                  m_configRegNames.size(), m_userRegNames.size(), latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("InitRegisterNames failed: {}", e.what());
    throw GNMException("InitRegisterNames failed: " + std::string(e.what()));
  }
}

GNMRegisterStateStats GNMRegisterState::GetStats() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetStats: latency={}us", latency);
    return m_stats;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetStats failed: {}", e.what());
    return m_stats;
  }
}

void GNMRegisterState::SaveState(std::ostream &out) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_regMutex);
  try {
    uint32_t version = 1; // Serialization version
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));

    for (const auto &stageRegs : m_shaderRegisters) {
      out.write(reinterpret_cast<const char *>(stageRegs.data()),
                stageRegs.size() * sizeof(uint32_t));
    }
    out.write(reinterpret_cast<const char *>(m_contextRegisters.data()),
              m_contextRegisters.size() * sizeof(uint32_t));
    out.write(reinterpret_cast<const char *>(m_configRegisters.data()),
              m_configRegisters.size() * sizeof(uint32_t));
    out.write(reinterpret_cast<const char *>(m_userRegisters.data()),
              m_userRegisters.size() * sizeof(uint32_t));

    uint64_t shaderRegNameCount = m_shaderRegNames.size();
    out.write(reinterpret_cast<const char *>(&shaderRegNameCount),
              sizeof(shaderRegNameCount));
    for (const auto &[offset, name] : m_shaderRegNames) {
      out.write(reinterpret_cast<const char *>(&offset), sizeof(offset));
      uint32_t nameLen = static_cast<uint32_t>(name.size());
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
      out.write(name.data(), nameLen);
    }

    uint64_t contextRegNameCount = m_contextRegNames.size();
    out.write(reinterpret_cast<const char *>(&contextRegNameCount),
              sizeof(contextRegNameCount));
    for (const auto &[offset, name] : m_contextRegNames) {
      out.write(reinterpret_cast<const char *>(&offset), sizeof(offset));
      uint32_t nameLen = static_cast<uint32_t>(name.size());
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
      out.write(name.data(), nameLen);
    }

    uint64_t configRegNameCount = m_configRegNames.size();
    out.write(reinterpret_cast<const char *>(&configRegNameCount),
              sizeof(configRegNameCount));
    for (const auto &[offset, name] : m_configRegNames) {
      out.write(reinterpret_cast<const char *>(&offset), sizeof(offset));
      uint32_t nameLen = static_cast<uint32_t>(name.size());
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
      out.write(name.data(), nameLen);
    }

    uint64_t userRegNameCount = m_userRegNames.size();
    out.write(reinterpret_cast<const char *>(&userRegNameCount),
              sizeof(userRegNameCount));
    for (const auto &[offset, name] : m_userRegNames) {
      out.write(reinterpret_cast<const char *>(&offset), sizeof(offset));
      uint32_t nameLen = static_cast<uint32_t>(name.size());
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
      out.write(name.data(), nameLen);
    }

    uint64_t cacheCount = m_registerCache.size();
    out.write(reinterpret_cast<const char *>(&cacheCount), sizeof(cacheCount));
    for (const auto &[key, entry] : m_registerCache) {
      out.write(reinterpret_cast<const char *>(&key), sizeof(key));
      out.write(reinterpret_cast<const char *>(&entry.type),
                sizeof(entry.type));
      out.write(reinterpret_cast<const char *>(&entry.stage),
                sizeof(entry.stage));
      out.write(reinterpret_cast<const char *>(&entry.offset),
                sizeof(entry.offset));
      out.write(reinterpret_cast<const char *>(&entry.value),
                sizeof(entry.value));
      out.write(reinterpret_cast<const char *>(&entry.cacheHits),
                sizeof(entry.cacheHits));
      out.write(reinterpret_cast<const char *>(&entry.cacheMisses),
                sizeof(entry.cacheMisses));
    }

    out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));

    if (!out.good()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw GNMException("Failed to write register state");
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info(
        "SaveState: Saved register state, shader_names={}, context_names={}, "
        "config_names={}, user_names={}, cache_entries={}, latency={}us",
        shaderRegNameCount, contextRegNameCount, configRegNameCount,
        userRegNameCount, cacheCount, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SaveState failed: {}", e.what());
    throw GNMException("SaveState failed: " + std::string(e.what()));
  }
}

void GNMRegisterState::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_regMutex);
  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (!in.good()) {
      throw GNMException("Failed to read version from stream");
    }
    if (version != 1) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw GNMException("Unsupported register state version: " +
                         std::to_string(version));
    }

    // Read register arrays with validation
    for (auto &stageRegs : m_shaderRegisters) {
      in.read(reinterpret_cast<char *>(stageRegs.data()),
              stageRegs.size() * sizeof(uint32_t));
      if (!in.good()) {
        throw GNMException("Failed to read shader registers from stream");
      }
    }
    in.read(reinterpret_cast<char *>(m_contextRegisters.data()),
            m_contextRegisters.size() * sizeof(uint32_t));
    if (!in.good()) {
      throw GNMException("Failed to read context registers from stream");
    }
    in.read(reinterpret_cast<char *>(m_configRegisters.data()),
            m_configRegisters.size() * sizeof(uint32_t));
    if (!in.good()) {
      throw GNMException("Failed to read config registers from stream");
    }
    in.read(reinterpret_cast<char *>(m_userRegisters.data()),
            m_userRegisters.size() * sizeof(uint32_t));
    if (!in.good()) {
      throw GNMException("Failed to read user registers from stream");
    }

    // Read shader register names with validation
    m_shaderRegNames.clear();
    uint64_t shaderRegNameCount;
    in.read(reinterpret_cast<char *>(&shaderRegNameCount),
            sizeof(shaderRegNameCount));
    if (!in.good() || shaderRegNameCount > 10000) { // Sanity check
      throw GNMException("Invalid shader register name count");
    }

    for (uint64_t i = 0; i < shaderRegNameCount; ++i) {
      uint32_t offset;
      uint32_t nameLen;
      in.read(reinterpret_cast<char *>(&offset), sizeof(offset));
      in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
      if (!in.good() || nameLen > 256) { // Sanity check for name length
        spdlog::warn("LoadState: Skipping invalid shader register name entry {}", i);
        continue;
      }

      std::string name(nameLen, '\0');
      in.read(name.data(), nameLen);
      if (!in.good()) {
        spdlog::warn("LoadState: Failed to read shader register name {}, stopping", i);
        break;
      }
      m_shaderRegNames[offset] = std::move(name);
    }

    m_contextRegNames.clear();
    uint64_t contextRegNameCount;
    in.read(reinterpret_cast<char *>(&contextRegNameCount),
            sizeof(contextRegNameCount));
    for (uint64_t i = 0; i < contextRegNameCount && in.good(); ++i) {
      uint32_t offset;
      uint32_t nameLen;
      in.read(reinterpret_cast<char *>(&offset), sizeof(offset));
      in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
      std::string name(nameLen, '\0');
      in.read(name.data(), nameLen);
      m_contextRegNames[offset] = name;
    }

    m_configRegNames.clear();
    uint64_t configRegNameCount;
    in.read(reinterpret_cast<char *>(&configRegNameCount),
            sizeof(configRegNameCount));
    for (uint64_t i = 0; i < configRegNameCount && in.good(); ++i) {
      uint32_t offset;
      uint32_t nameLen;
      in.read(reinterpret_cast<char *>(&offset), sizeof(offset));
      in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
      std::string name(nameLen, '\0');
      in.read(name.data(), nameLen);
      m_configRegNames[offset] = name;
    }

    m_userRegNames.clear();
    uint64_t userRegNameCount;
    in.read(reinterpret_cast<char *>(&userRegNameCount),
            sizeof(userRegNameCount));
    for (uint64_t i = 0; i < userRegNameCount && in.good(); ++i) {
      uint32_t offset;
      uint32_t nameLen;
      in.read(reinterpret_cast<char *>(&offset), sizeof(offset));
      in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
      std::string name(nameLen, '\0');
      in.read(name.data(), nameLen);
      m_userRegNames[offset] = name;
    }

    m_registerCache.clear();
    uint64_t cacheCount;
    in.read(reinterpret_cast<char *>(&cacheCount), sizeof(cacheCount));
    for (uint64_t i = 0; i < cacheCount && in.good(); ++i) {
      uint64_t key;
      RegisterCacheEntry entry;
      in.read(reinterpret_cast<char *>(&key), sizeof(key));
      in.read(reinterpret_cast<char *>(&entry.type), sizeof(entry.type));
      in.read(reinterpret_cast<char *>(&entry.stage), sizeof(entry.stage));
      in.read(reinterpret_cast<char *>(&entry.offset), sizeof(entry.offset));
      in.read(reinterpret_cast<char *>(&entry.value), sizeof(entry.value));
      in.read(reinterpret_cast<char *>(&entry.cacheHits),
              sizeof(entry.cacheHits));
      in.read(reinterpret_cast<char *>(&entry.cacheMisses),
              sizeof(entry.cacheMisses));
      m_registerCache[key] = entry;
    }

    in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));

    if (!in.good()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw GNMException("Failed to read register state");
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info(
        "LoadState: Loaded register state, shader_names={}, context_names={}, "
        "config_names={}, user_names={}, cache_entries={}, latency={}us",
        shaderRegNameCount, contextRegNameCount, configRegNameCount,
        userRegNameCount, cacheCount, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("LoadState failed: {}", e.what());
    throw GNMException("LoadState failed: " + std::string(e.what()));
  }
}

} // namespace ps4