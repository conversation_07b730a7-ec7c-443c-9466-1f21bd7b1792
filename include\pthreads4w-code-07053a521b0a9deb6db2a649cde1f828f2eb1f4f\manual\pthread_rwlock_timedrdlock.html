<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_RWLOCK_TIMEDRDLOCK(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_rwlock_timedrdlock - lock a read-write lock for reading 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt; <BR></B>#include &lt;time.h&gt; 
</P>
<P><B>int pthread_rwlock_timedrdlock(pthread_rwlock_t *restrict</B>
<I>rwlock</I><B>, const struct timespec *restrict</B> <I>abs_timeout</I><B>);
</B>
</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>The <B>pthread_rwlock_timedrdlock</B> function shall apply a read
lock to the read-write lock referenced by <I>rwlock</I> as in the
<A HREF="pthread_rwlock_rdlock.html"><B>pthread_rwlock_rdlock</B>(3)</A>
function. However, if the lock cannot be acquired without waiting for
other threads to unlock the lock, this wait shall be terminated when
the specified timeout expires. The timeout shall expire when the
absolute time specified by <I>abs_timeout</I> passes, as measured by
the clock on which timeouts are based (that is, when the value of
that clock equals or exceeds <I>abs_timeout</I>), or if the absolute
time specified by <I>abs_timeout</I> has already been passed at the
time of the call. 
</P>
<P>The <B>timespec</B> data type is defined in the <I>&lt;time.h&gt;</I>
header. Under no circumstances shall the function fail with a timeout
if the lock can be acquired immediately. The validity of the
<I>abs_timeout</I> parameter need not be checked if the lock can be
immediately acquired. 
</P>
<P>The calling thread may deadlock if at the time the call is made it
holds a write lock on <I>rwlock</I>. The results are undefined if
this function is called with an uninitialized read-write lock. 
</P>
<P><B>PThreads4W</B> defines <B>_POSIX_READER_WRITER_LOCKS</B> in
pthread.h as  200112L to indicate that the reader/writer routines are
implemented and may be used.</P>
<H2><A HREF="#toc3" NAME="sect3">Return Value</A></H2>
<P>The <B>pthread_rwlock_timedrdlock</B> function shall return zero
if the lock for reading on the read-write lock object referenced by
<I>rwlock</I> is acquired. Otherwise, an error number shall be
returned to indicate the error. 
</P>
<H2><A HREF="#toc4" NAME="sect4">Errors</A></H2>
<P>The <B>pthread_rwlock_timedrdlock</B> function shall fail if: 
</P>
<DL>
	<DT><B>ETIMEDOUT</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	The lock could not be acquired before the specified timeout expired.
		</DD></DL>
<P>
The <B>pthread_rwlock_timedrdlock</B> function may fail if: 
</P>
<DL>
	<DT><B>EAGAIN</B> 
	</DT><DD>
	The read lock could not be acquired because the maximum number of
	read locks for lock would be exceeded. 
	</DD><DT>
	<B>EINVAL</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	The value specified by <I>rwlock</I> does not refer to an
	initialized read-write lock object, or the <I>abs_timeout</I>
	nanosecond value is less than zero or greater than or equal to 1000
	million. 
	</DD></DL>
<P>
This function shall not return an error code of [EINTR]. 
</P>
<P><I>The following sections are informative.</I> 
</P>
<H2><A HREF="#toc5" NAME="sect5">Examples</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc6" NAME="sect6">Application Usage</A></H2>
<P>Applications using this function may be subject to priority
inversion, as discussed in the Base Definitions volume of
IEEE&nbsp;Std&nbsp;1003.1-2001, Section 3.285, Priority Inversion. 
</P>
<H2><A HREF="#toc7" NAME="sect7">Rationale</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc8" NAME="sect8">Future Directions</A></H2>
<P>None. 
</P>
<H2><A HREF="#toc9" NAME="sect9">See Also</A></H2>
<P><A HREF="pthread_rwlock_init.html"><B>pthread_rwlock_destroy</B>(3)</A>
<B>,</B> <A HREF="pthread_rwlock_rdlock.html"><B>pthread_rwlock_rdlock</B>(3)</A>
<B>,</B> <A HREF="pthread_rwlock_timedwrlock.html"><B>pthread_rwlock_timedwrlock</B>(3)</A>
<B>,</B> <A HREF="pthread_rwlock_rdlock.html"><B>pthread_rwlock_tryrdlock</B>(3)</A>
<B>,</B> <A HREF="pthread_rwlock_wrlock.html"><B>pthread_rwlock_trywrlock</B>(3)</A>
<B>,</B> <A HREF="pthread_rwlock_unlock.html"><B>pthread_rwlock_unlock</B>(3)</A>
<B>,</B> <A HREF="pthread_rwlock_wrlock.html"><B>pthread_rwlock_wrlock</B>(3)</A>
<B>,</B> the Base Definitions volume of IEEE&nbsp;Std&nbsp;1003.1-2001,
<I>&lt;pthread.h&gt;</I>, <I>&lt;time.h&gt;</I> 
</P>
<H2><A HREF="#toc10" NAME="sect10">Copyright</A></H2>
<P>Portions of this text are reprinted and reproduced in electronic
form from IEEE Std 1003.1, 2003 Edition, Standard for Information
Technology -- Portable Operating System Interface (POSIX), The Open
Group Base Specifications Issue 6, Copyright (C) 2001-2003 by the
Institute of Electrical and Electronics Engineers, Inc and The Open
Group. In the event of any discrepancy between this version and the
original IEEE and The Open Group Standard, the original IEEE and The
Open Group Standard is the referee document. The original Standard
can be obtained online at <A HREF="http://www.opengroup.org/unix/online.html">http://www.opengroup.org/unix/online.html</A>
. 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Examples</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect6" NAME="toc6">Application
	Usage</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect7" NAME="toc7">Rationale</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect8" NAME="toc8">Future
	Directions</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect9" NAME="toc9">See
	Also</A> 
	</P>
	<LI><P><A HREF="#sect10" NAME="toc10">Copyright</A> 
	</P>
</UL>
</BODY>
</HTML>
