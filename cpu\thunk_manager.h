// Copyright 2025 <Copyright Owner>

#pragma once

#include <cstdint>
#include <functional>
#include <shared_mutex>
#include <string>
#include <tuple>
#include <unordered_map>
#include <utility>
#include <vector>

namespace ps4 {
enum class ThunkError { Success, InvalidId, Inactive, InvalidArgs, Exception };

class ThunkManager {
public:
  using ThunkFunction = std::function<uint64_t(uint64_t *, uint32_t)>;

  ThunkManager();
  ~ThunkManager();

  bool Initialize();
  void Shutdown();

  template <typename Ret, typename... Args>
  uint64_t RegisterThunk(const std::string &name,
                         std::function<Ret(Args...)> func) {
    auto thunkFunc = [this, func](uint64_t *args, uint32_t count) -> uint64_t {
      if (count != sizeof...(Args))
        return -1;
      std::tuple<Args...> tuple;
      unpackArgs(args, tuple, std::index_sequence_for<Args...>{});
      return static_cast<uint64_t>(std::apply(func, tuple));
    };
    return RegisterThunkImpl(name, thunkFunc, sizeof...(Args));
  }

  bool UnregisterThunk(uint64_t thunkId);

  std::pair<uint64_t, ThunkError> CallThunk(uint64_t thunkId, uint64_t *args,
                                            uint32_t argCount);

  uint64_t GetThunkId(const std::string &name) const;
  std::string GetThunkName(uint64_t thunkId) const;
  std::vector<std::string> ListThunks() const;

  void SaveState(std::ostream &out) const;
  void LoadState(std::istream &in);

private:
  struct ThunkEntry {
    std::string name;
    ThunkFunction function;
    bool active;
    uint32_t refCount;
    uint32_t expectedArgs;
  };

  uint64_t RegisterThunkImpl(const std::string &name, ThunkFunction function,
                             uint32_t expectedArgs);
  uint64_t GenerateThunkId();

  template <typename Tuple, size_t... Is>
  void unpackArgs(uint64_t *args, Tuple &tuple, std::index_sequence<Is...>) {
    ((std::get<Is>(tuple) =
          static_cast<std::tuple_element_t<Is, Tuple>>(args[Is])),
     ...);
  }

  std::vector<ThunkEntry> m_thunks;
  std::unordered_map<std::string, uint64_t> m_thunkNameMap;
  mutable std::shared_mutex m_thunkMutex;
  uint64_t m_nextThunkId;

  std::unordered_map<uint64_t, std::pair<uint64_t, uint64_t>>
      m_stats; // Stores call count and total cycles
};
} // namespace ps4