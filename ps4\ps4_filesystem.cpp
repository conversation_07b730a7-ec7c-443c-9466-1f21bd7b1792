// Copyright 2025 <Copyright Owner>

#include "ps4_filesystem.h"
#include "../memory/memory_diagnostics.h"
#include "ps4_emulator.h"
#include <algorithm>
#include <chrono>
#include <fcntl.h>
#include <filesystem>
#include <fmt/core.h>
#include <fstream>
#include <iomanip>
#include <random>
#include <spdlog/spdlog.h>
#include <sstream>
#include <stdexcept>
#include <sys/stat.h>

// Forward declarations and type definitions

#ifndef S_ISDIR
#define S_ISDIR(m) (((m) & S_IFMT) == S_IFDIR)
#endif

#ifdef _WIN32
#include <io.h>
#define stat _stat
#ifndef O_ACCMODE
#define O_ACCMODE (_O_RDONLY | _O_WRONLY | _O_RDWR)
#endif
#ifndef S_IWUSR
#define S_IWUSR _S_IWRITE
#endif
#undef stat
#else
#include <unistd.h>
#endif
#include <ctime>
#ifdef CreateDirectoryW
#undef CreateDirectoryW
#endif
#ifdef CreateDirectoryA
#undef CreateDirectoryA
#endif
#ifdef CreateDirectory
#undef CreateDirectory
#endif
#ifdef EncryptFile
#undef EncryptFile
#endif
#ifdef DecryptFile
#undef DecryptFile
#endif
#ifdef RemoveDirectoryA
#undef RemoveDirectoryA
#endif
#ifdef RemoveDirectoryW
#undef RemoveDirectoryW
#endif
#ifdef RemoveDirectory
#undef RemoveDirectory
#endif

namespace ps4 {

/**
 * @brief Constructs the filesystem with an emulator reference.
 * @param emu Reference to the PS4 emulator.
 */
PS4Filesystem::PS4Filesystem(PS4Emulator &emu)
    : m_emulator(emu), m_rootPath(""), m_nextFd(3) {
  auto start = std::chrono::steady_clock::now();
  m_stats = FilesystemStats();
  spdlog::info("PS4Filesystem constructed");
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Default constructor (for testing).
 */
PS4Filesystem::PS4Filesystem()
    : m_emulator(PS4Emulator::GetInstance()), m_rootPath(""), m_nextFd(3) {
  auto start = std::chrono::steady_clock::now();
  m_stats = FilesystemStats();
  spdlog::info("PS4Filesystem constructed (default)");
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Destructor, cleaning up resources.
 */
PS4Filesystem::~PS4Filesystem() {
  auto start = std::chrono::steady_clock::now();
  Shutdown();
  spdlog::info("PS4Filesystem destroyed");
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
}

/**
 * @brief Initializes the filesystem.
 * @return True on success, false otherwise.
 */
bool PS4Filesystem::Initialize() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    m_files.clear();
    m_fileHandles.clear();
    m_directories.clear();
    m_deviceFiles.clear();
    m_mountPoints.clear();
    m_nextFd = 3;
    m_stats = FilesystemStats();
    m_directories.emplace_back("/"); // Root directory
    m_rootPath = m_settings.defaultMountPoint.empty()
                     ? "./ps4_root"
                     : m_settings.defaultMountPoint;
    std::filesystem::create_directories(m_rootPath);

    // Initialize PS4-specific filesystem features
    if (m_settings.enableDeviceFiles) {
      InitializeDeviceFiles();
    }

    if (m_settings.enablePFS) {
      InitializePFS();
    }

    // Setup mount points
    m_mountPoints["/app0"] = m_rootPath + "/app0";
    m_mountPoints["/dev"] = m_rootPath + "/dev";
    m_mountPoints[m_settings.saveDataPath] = m_rootPath + "/savedata";
    m_mountPoints[m_settings.trophyPath] = m_rootPath + "/trophy";
    m_mountPoints[m_settings.systemPath] = m_rootPath + "/system";

    // Create mount point directories
    for (const auto &[virtualPath, hostPath] : m_mountPoints) {
      std::filesystem::create_directories(hostPath);
    }
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("PS4Filesystem initialized with root path: {}, latency={}us",
                 m_rootPath, m_stats.totalLatencyUs);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("PS4Filesystem initialization failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Shuts down the filesystem, closing handles.
 */
void PS4Filesystem::Shutdown() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    for (auto &[fd, handle] : m_fileHandles) {
      if (handle.hostFd >= 3) {
        ::close(handle.hostFd);
      }
    }
    m_files.clear();
    m_fileHandles.clear();
    m_directories.clear();
    m_stats = FilesystemStats();
    lock.unlock();
    ps4::MemoryDiagnostics::GetInstance().UpdateMetrics();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("PS4Filesystem shutdown");
  } catch (const std::exception &e) {
    spdlog::error("PS4Filesystem shutdown failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Opens a file with specified flags and mode.
 * @param path Virtual file path.
 * @param flags File open flags.
 * @param mode File permissions.
 * @return File descriptor, or -1 on failure.
 */
int PS4Filesystem::OpenFile(const std::string &path, int flags, mode_t mode) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    if (path.empty() || path[0] != '/') {
      spdlog::error("OpenFile: Invalid path: {}", path);
      m_stats.cacheMisses++;
      return -1;
    }
    std::string hostPath = m_rootPath + path;
    int hostFd = -1;

#ifdef _WIN32
    int hostFlags = 0;
    if ((flags & O_ACCMODE) == O_RDONLY)
      hostFlags |= _O_RDONLY;
    if ((flags & O_ACCMODE) == O_WRONLY)
      hostFlags |= _O_WRONLY;
    if ((flags & O_ACCMODE) == O_RDWR)
      hostFlags |= _O_RDWR;
    if (flags & O_APPEND)
      hostFlags |= _O_APPEND;
    if (flags & O_CREAT)
      hostFlags |= _O_CREAT;
    if (flags & O_TRUNC)
      hostFlags |= _O_TRUNC;
    if (flags & O_EXCL)
      hostFlags |= _O_EXCL;
    hostFlags |= _O_BINARY;
    int pmode = _S_IREAD;
    if (mode & S_IWUSR)
      pmode |= _S_IWRITE;
    hostFd = ::open(hostPath.c_str(), hostFlags, pmode);
#else
    hostFd = ::open(hostPath.c_str(), flags, mode);
#endif

    if (hostFd < 0) {
      spdlog::error("OpenFile: Failed to open {}: errno={}", hostPath, errno);
      m_stats.cacheMisses++;
      return -1;
    }

    int fd = m_nextFd++;
    FileHandle handle;
    handle.path = path;
    handle.flags = flags;
    handle.offset = 0;
    handle.hostFd = hostFd;
    handle.fd = fd;
    m_fileHandles[fd] = handle;

    FileEntry &entry = m_files[path];
    entry.path = path;
    struct stat st;
    if (::stat(hostPath.c_str(), &st) == 0) {
      entry.size = st.st_size;
      entry.mode = st.st_mode;
      entry.creationTime = st.st_ctime;
      entry.modificationTime = st.st_mtime;
      entry.accessTime = st.st_atime;
      entry.isDir = S_ISDIR(st.st_mode);
    } else {
      entry.size = 0;
      entry.mode = mode;
      entry.creationTime = std::time(nullptr);
      entry.modificationTime = entry.creationTime;
      entry.accessTime = entry.creationTime;
    }

    m_stats.operationCount++;
    m_stats.fileAccessCount++;
    m_stats.cacheHits++;
    lock.unlock();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("OpenFile: Opened {} as FD {}, hostPath={}, latency={}us",
                 path, fd, hostPath, m_stats.totalLatencyUs);
    return fd;
  } catch (const std::exception &e) {
    spdlog::error("OpenFile failed: {}", e.what());
    m_stats.cacheMisses++;
    return -1;
  }
}

/**
 * @brief Closes a file descriptor.
 * @param fd File descriptor.
 * @return 0 on success, -1 on failure.
 */
int PS4Filesystem::CloseFile(int fd) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    auto it = m_fileHandles.find(fd);
    if (it == m_fileHandles.end()) {
      spdlog::error("CloseFile: Invalid FD: {}", fd);
      m_stats.cacheMisses++;
      return -1;
    }
    if (it->second.hostFd >= 3) {
      ::close(it->second.hostFd);
    }
    m_fileHandles.erase(it);
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("CloseFile: Closed FD {}, latency={}us", fd,
                 m_stats.totalLatencyUs);
    return 0;
  } catch (const std::exception &e) {
    spdlog::error("CloseFile failed: {}", e.what());
    m_stats.cacheMisses++;
    return -1;
  }
}

/**
 * @brief Reads data from a file descriptor.
 * @param fd File descriptor.
 * @param buf Output buffer.
 * @param count Bytes to read.
 * @return Bytes read, or -1 on failure.
 */
ssize_t PS4Filesystem::ReadFile(int fd, void *buf, size_t count) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    auto it = m_fileHandles.find(fd);
    if (it == m_fileHandles.end()) {
      spdlog::error("ReadFile: Invalid FD: {}", fd);
      m_stats.cacheMisses++;
      return -1;
    }
    FileHandle &handle = it->second;
    auto fileIt = m_files.find(handle.path);
    if (fileIt != m_files.end() && !fileIt->second.data.empty() &&
        handle.offset < fileIt->second.data.size()) {
      size_t bytesToRead =
          (std::min)(count, fileIt->second.data.size() -
                                static_cast<size_t>(handle.offset));
      std::memcpy(buf, fileIt->second.data.data() + handle.offset, bytesToRead);
      handle.offset += bytesToRead;
      fileIt->second.accessTime = std::time(nullptr);
      fileIt->second.cacheHits++;
      handle.cacheHits++;
      m_stats.cacheHits++;
      m_stats.operationCount++;
      m_stats.fileAccessCount++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace("ReadFile: Read {} bytes from FD {} (cached), offset={}, "
                    "latency={}us",
                    bytesToRead, fd, handle.offset, m_stats.totalLatencyUs);
      return static_cast<ssize_t>(bytesToRead);
    }
    lock.unlock();
    ssize_t bytesRead = ::read(handle.hostFd, buf, count);
    lock.lock();
    if (bytesRead < 0) {
      spdlog::error("ReadFile: Failed for FD {}, count={}: errno={}", fd, count,
                    errno);
      handle.cacheMisses++;
      m_stats.cacheMisses++;
      return -1;
    }
    handle.offset += bytesRead;
    if (fileIt != m_files.end()) {
      fileIt->second.accessTime = std::time(nullptr);
      if (fileIt->second.data.empty() && bytesRead > 0) {
        fileIt->second.data.resize(bytesRead);
        std::memcpy(fileIt->second.data.data(), buf, bytesRead);
      }
      fileIt->second.cacheMisses++;
    }
    handle.cacheMisses++;
    m_stats.operationCount++;
    m_stats.fileAccessCount++;
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("ReadFile: Read {} bytes from FD {}, offset={}, latency={}us",
                  bytesRead, fd, handle.offset, m_stats.totalLatencyUs);
    return bytesRead;
  } catch (const std::exception &e) {
    spdlog::error("ReadFile failed: {}", e.what());
    m_stats.cacheMisses++;
    return -1;
  }
}

/**
 * @brief Reads entire file contents into a vector.
 * @param path File path.
 * @param data Output vector to store file data.
 * @return True on success, false on failure.
 */
bool PS4Filesystem::ReadFile(const std::string &path,
                             std::vector<uint8_t> &data) {
  std::lock_guard<std::shared_mutex> lock(m_mutex);

  try {
    std::ifstream file(ResolvePath(path), std::ios::binary | std::ios::ate);
    if (!file.is_open()) {
      spdlog::error("ReadFile: Cannot open file: {}", path);
      return false;
    }

    std::streamsize size = file.tellg();
    file.seekg(0, std::ios::beg);

    data.resize(static_cast<size_t>(size));
    if (!file.read(reinterpret_cast<char *>(data.data()), size)) {
      spdlog::error("ReadFile: Failed to read file: {}", path);
      return false;
    }

    spdlog::trace("ReadFile: Successfully read {} bytes from {}", size, path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ReadFile failed for {}: {}", path, e.what());
    return false;
  }
}

/**
 * @brief Lists all files in a directory.
 * @param path Directory path.
 * @param recursive Whether to list files recursively.
 * @return Vector of file paths.
 */
std::vector<std::string> PS4Filesystem::ListFiles(const std::string &path,
                                                  bool recursive) {
  std::vector<std::string> files;
  std::lock_guard<std::shared_mutex> lock(m_mutex);

  try {
    std::string resolvedPath = ResolvePath(path);
    std::filesystem::path dirPath(resolvedPath);

    if (!std::filesystem::exists(dirPath) ||
        !std::filesystem::is_directory(dirPath)) {
      spdlog::error(
          "ListFiles: Directory does not exist or is not a directory: {}",
          path);
      return files;
    }

    if (recursive) {
      for (const auto &entry :
           std::filesystem::recursive_directory_iterator(dirPath)) {
        if (entry.is_regular_file()) {
          files.push_back(entry.path().string());
        }
      }
    } else {
      for (const auto &entry : std::filesystem::directory_iterator(dirPath)) {
        if (entry.is_regular_file()) {
          files.push_back(entry.path().string());
        }
      }
    }

    spdlog::trace("ListFiles: Found {} files in {}", files.size(), path);
    return files;
  } catch (const std::exception &e) {
    spdlog::error("ListFiles failed for {}: {}", path, e.what());
    return files;
  }
}

/**
 * @brief Resolves a virtual path to an actual filesystem path.
 * @param virtualPath Virtual path to resolve.
 * @return Resolved filesystem path.
 */
std::string PS4Filesystem::ResolvePath(const std::string &virtualPath) const {
  // Handle absolute paths
  if (virtualPath.empty()) {
    return m_rootPath;
  }

  // Remove leading slash if present
  std::string cleanPath = virtualPath;
  if (cleanPath[0] == '/') {
    cleanPath = cleanPath.substr(1);
  }

  // Check for mount point mappings
  for (const auto &[mountPoint, realPath] : m_mountPoints) {
    if (cleanPath.starts_with(mountPoint)) {
      std::string relativePath = cleanPath.substr(mountPoint.length());
      if (relativePath.empty() || relativePath[0] == '/') {
        return realPath + relativePath;
      }
    }
  }

  // Default: combine with root path
  if (m_rootPath.empty()) {
    return cleanPath;
  }

  return m_rootPath + "/" + cleanPath;
}

/**
 * @brief Writes data to a file descriptor.
 * @param fd File descriptor.
 * @param buf Input buffer.
 * @param count Bytes to write.
 * @return Bytes written, or -1 on failure.
 */
ssize_t PS4Filesystem::WriteFile(int fd, const void *buf, size_t count) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    auto it = m_fileHandles.find(fd);
    if (it == m_fileHandles.end()) {
      spdlog::error("WriteFile: Invalid FD: {}", fd);
      m_stats.cacheMisses++;
      return -1;
    }
    FileHandle &handle = it->second;
    auto fileIt = m_files.find(handle.path);
    lock.unlock();
    ssize_t bytesWritten = ::write(handle.hostFd, buf, count);
    lock.lock();
    if (bytesWritten < 0) {
      spdlog::error("WriteFile: Failed for FD {}, count={}: errno={}", fd,
                    count, errno);
      handle.cacheMisses++;
      m_stats.cacheMisses++;
      return -1;
    }
    handle.offset += bytesWritten;
    if (fileIt != m_files.end()) {
      fileIt->second.size =
          (std::max)(fileIt->second.size, static_cast<uint64_t>(handle.offset));
      fileIt->second.modificationTime = std::time(nullptr);
      if (!fileIt->second.data.empty()) {
        fileIt->second.data.resize(handle.offset);
        std::memcpy(fileIt->second.data.data() + (handle.offset - bytesWritten),
                    buf, bytesWritten);
        fileIt->second.cacheHits++;
      } else {
        fileIt->second.cacheMisses++;
      }
    }
    handle.cacheHits++;
    m_stats.operationCount++;
    m_stats.fileAccessCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("WriteFile: Wrote {} bytes to FD {}, offset={}, latency={}us",
                  bytesWritten, fd, handle.offset, m_stats.totalLatencyUs);
    return bytesWritten;
  } catch (const std::exception &e) {
    spdlog::error("WriteFile failed: {}", e.what());
    m_stats.cacheMisses++;
    return -1;
  }
}

/**
 * @brief Seeks to a position in a file.
 * @param fd File descriptor.
 * @param offset Offset to seek to.
 * @param whence Seek origin.
 * @return New offset, or -1 on failure.
 */
off_t PS4Filesystem::SeekFile(int fd, off_t offset, int whence) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    auto it = m_fileHandles.find(fd);
    if (it == m_fileHandles.end()) {
      spdlog::error("SeekFile: Invalid FD: {}", fd);
      m_stats.cacheMisses++;
      return -1;
    }
    FileHandle &handle = it->second;
    lock.unlock();
    off_t newOffset = ::lseek(handle.hostFd, offset, whence);
    lock.lock();
    if (newOffset < 0) {
      spdlog::error(
          "SeekFile: Failed for FD {}, offset={}, whence={}: errno={}", fd,
          offset, whence, errno);
      handle.cacheMisses++;
      m_stats.cacheMisses++;
      return -1;
    }
    handle.offset = newOffset;
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("SeekFile: FD {}, new offset={}, whence={}, latency={}us", fd,
                  newOffset, whence, m_stats.totalLatencyUs);
    return newOffset;
  } catch (const std::exception &e) {
    spdlog::error("SeekFile failed: {}", e.what());
    m_stats.cacheMisses++;
    return -1;
  }
}

/**
 * @brief Retrieves file statistics.
 * @param path File path.
 * @param buf Output stat buffer.
 * @return 0 on success, -1 on failure.
 */
int PS4Filesystem::StatFile(const std::string &path, struct stat *buf) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_mutex, std::defer_lock);
  lock.lock();
  try {
    auto it = m_files.find(path);
    if (it != m_files.end()) {
      FileEntry &entry = it->second;
      buf->st_size = entry.size;
      buf->st_mode = entry.mode;
      buf->st_ctime = entry.creationTime;
      buf->st_mtime = entry.modificationTime;
      buf->st_atime = entry.accessTime;
      entry.cacheHits++;
      m_stats.cacheHits++;
      m_stats.operationCount++;
      m_stats.fileAccessCount++;
      auto end = std::chrono::steady_clock::now();
      m_stats.totalLatencyUs +=
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      spdlog::trace("StatFile: Path {} (cached), size={}, latency={}us", path,
                    entry.size, m_stats.totalLatencyUs);
      lock.unlock();
      return 0;
    }
    std::string hostPath = m_rootPath + path;
    lock.unlock();
    if (::stat(hostPath.c_str(), buf) < 0) {
      spdlog::error("StatFile: Path not found: {}, errno={}", hostPath, errno);
      m_stats.cacheMisses++;
      return -1;
    }
    lock.lock();
    m_stats.operationCount++;
    m_stats.fileAccessCount++;
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("StatFile: Path {}, size={}, latency={}us", hostPath,
                  buf->st_size, m_stats.totalLatencyUs);
    lock.unlock();
    return 0;
  } catch (const std::exception &e) {
    spdlog::error("StatFile failed: {}", e.what());
    m_stats.cacheMisses++;
    lock.unlock();
    return -1;
  }
}

/**
 * @brief Creates a directory with specified mode.
 * @param path Directory path.
 * @param mode Directory permissions.
 * @return True on success, false otherwise.
 */
bool PS4Filesystem::CreateDirectory(const std::string &path, mode_t mode) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    std::string fullPath = m_rootPath + path;
    lock.unlock();
    std::filesystem::create_directories(fullPath);
    lock.lock();
    struct stat st;
    if (::stat(fullPath.c_str(), &st) == 0) {
      m_directories.push_back(fullPath);
      FileEntry &entry = m_files[path];
      entry.path = path;
      entry.size = 0;
      entry.mode = mode ? mode : m_settings.defaultDirMode;
      entry.creationTime = st.st_ctime;
      entry.modificationTime = st.st_mtime;
      entry.accessTime = st.st_atime;
      entry.isDir = true;
      entry.cacheHits++;
    }
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::trace("CreateDirectory: path={}, latency={}us", fullPath,
                  m_stats.totalLatencyUs);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateDirectory failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Creates a directory (wide string).
 * @param path Directory path.
 * @return True on success, false otherwise.
 */
bool PS4Filesystem::CreateDirectoryW(const std::wstring &path) {
  return MountDirectory(path);
}

/**
 * @brief Mounts a directory.
 * @param path Directory path.
 * @return True on success, false otherwise.
 */
bool PS4Filesystem::MountDirectory(const std::wstring &path) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    std::string utf8Path = std::filesystem::path(path).string();
    lock.unlock();
    std::filesystem::create_directories(path);
    lock.lock();
    m_directories.push_back(utf8Path);
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("MountDirectory: path={}, latency={}us", utf8Path,
                 m_stats.totalLatencyUs);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("MountDirectory failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Allocates virtual memory.
 * @param size Size to allocate.
 * @param alignment Alignment requirement.
 * @param shared True if shared memory.
 * @return Virtual address, or 0 on failure.
 */
uint64_t PS4Filesystem::AllocateVirtualMemory(uint64_t size, uint64_t alignment,
                                              bool shared) {
  auto start = std::chrono::steady_clock::now();
  try {
    uint64_t addr =
        m_emulator.GetOrbisOS().AllocateVirtualMemory(size, alignment, shared);
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return addr;
  } catch (const std::exception &e) {
    spdlog::error("AllocateVirtualMemory failed: {}", e.what());
    m_stats.cacheMisses++;
    return 0;
  }
}

/**
 * @brief Frees virtual memory.
 * @param address Virtual address.
 * @return True on success, false otherwise.
 */
bool PS4Filesystem::FreeVirtualMemory(uint64_t address) {
  auto start = std::chrono::steady_clock::now();
  try {
    m_emulator.GetOrbisOS().FreeVirtualMemory(address);
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return true;
  } catch (const std::exception &e) {
    spdlog::error("FreeVirtualMemory failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Sets memory protection.
 * @param address Virtual address.
 * @param size Size of region.
 * @param protection Protection flags.
 * @return True on success, false otherwise.
 */
bool PS4Filesystem::ProtectMemory(uint64_t address, uint64_t size,
                                  int protection) {
  auto start = std::chrono::steady_clock::now();
  try {
    bool success =
        m_emulator.GetOrbisOS().ProtectMemory(address, size, protection);
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return success;
  } catch (const std::exception &e) {
    spdlog::error("ProtectMemory failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Gets the current process ID.
 * @return Process ID.
 */
uint64_t PS4Filesystem::SceKernelGetProcessId() {
  auto start = std::chrono::steady_clock::now();
  try {
    uint64_t pid = m_emulator.GetOrbisOS().SceKernelGetProcessId();
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return pid;
  } catch (const std::exception &e) {
    spdlog::error("SceKernelGetProcessId failed: {}", e.what());
    m_stats.cacheMisses++;
    return 1;
  }
}

/**
 * @brief Dumps the filesystem state as a string.
 * @return String representation of the state.
 */
std::string PS4Filesystem::DumpState() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_mutex, std::defer_lock);
  lock.lock();
  try {
    std::ostringstream oss;
    oss << "PS4 Filesystem State Dump\n";
    oss << "========================\n";
    oss << "Root Path: " << m_rootPath << "\n";
    oss << "Open File Handles: " << m_fileHandles.size() << "\n";
    oss << "Total Files: " << m_files.size() << "\n";
    oss << "Directories: " << m_directories.size() << "\n";
    oss << "Mount Points: " << m_mountPoints.size() << "\n";
    oss << "Device Files: " << m_deviceFiles.size() << "\n";
    oss << "Stats: Ops=" << m_stats.operationCount
        << ", Latency=" << m_stats.totalLatencyUs << "us, ";
    oss << "Hits=" << m_stats.cacheHits << ", Misses=" << m_stats.cacheMisses
        << ", ";
    oss << "Accesses=" << m_stats.fileAccessCount << "\n";
    oss << "Game Loaded: " << (m_gameLoaded ? "Yes" : "No");
    if (m_gameLoaded) {
      oss << " (" << m_loadedGamePath << ")";
    }
    oss << "\n";
    lock.unlock();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return oss.str();
  } catch (const std::exception &e) {
    spdlog::error("DumpState failed: {}", e.what());
    lock.unlock();
    return "Error dumping state: " + std::string(e.what());
  }
}

/**
 * @brief Saves the filesystem state to a stream.
 * @param out Output stream.
 */
void PS4Filesystem::SaveState(std::ostream &out) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_mutex, std::defer_lock);
  lock.lock();
  try {
    uint64_t fileCount = m_files.size();
    out.write(reinterpret_cast<const char *>(&fileCount), sizeof(fileCount));
    for (const auto &[path, entry] : m_files) {
      uint64_t pathLen = path.size();
      out.write(reinterpret_cast<const char *>(&pathLen), sizeof(pathLen));
      out.write(path.c_str(), pathLen);
      uint64_t hostPathLen = entry.hostPath.size();
      out.write(reinterpret_cast<const char *>(&hostPathLen),
                sizeof(hostPathLen));
      out.write(entry.hostPath.c_str(), hostPathLen);
      out.write(reinterpret_cast<const char *>(&entry.size),
                sizeof(entry.size));
      out.write(reinterpret_cast<const char *>(&entry.protection),
                sizeof(entry.protection));
      out.write(reinterpret_cast<const char *>(&entry.mode),
                sizeof(entry.mode));
      out.write(reinterpret_cast<const char *>(&entry.creationTime),
                sizeof(entry.creationTime));
      out.write(reinterpret_cast<const char *>(&entry.modificationTime),
                sizeof(entry.modificationTime));
      out.write(reinterpret_cast<const char *>(&entry.accessTime),
                sizeof(entry.accessTime));
      out.write(reinterpret_cast<const char *>(&entry.isDir),
                sizeof(entry.isDir));
      uint64_t dataSize = entry.data.size();
      out.write(reinterpret_cast<const char *>(&dataSize), sizeof(dataSize));
      if (dataSize > 0) {
        out.write(reinterpret_cast<const char *>(entry.data.data()), dataSize);
      }
      out.write(reinterpret_cast<const char *>(&entry.fileType),
                sizeof(entry.fileType));
      out.write(reinterpret_cast<const char *>(&entry.ps4Permissions),
                sizeof(entry.ps4Permissions));
      uint64_t mountPointLen = entry.mountPoint.size();
      out.write(reinterpret_cast<const char *>(&mountPointLen),
                sizeof(mountPointLen));
      out.write(entry.mountPoint.c_str(), mountPointLen);
      out.write(reinterpret_cast<const char *>(&entry.isEncrypted),
                sizeof(entry.isEncrypted));
      out.write(reinterpret_cast<const char *>(&entry.blockSize),
                sizeof(entry.blockSize));
      uint64_t checksumSize = entry.checksum.size();
      out.write(reinterpret_cast<const char *>(&checksumSize),
                sizeof(checksumSize));
      if (checksumSize > 0) {
        out.write(reinterpret_cast<const char *>(entry.checksum.data()),
                  checksumSize);
      }
      out.write(reinterpret_cast<const char *>(&entry.present),
                sizeof(entry.present));
      out.write(reinterpret_cast<const char *>(&entry.cacheHits),
                sizeof(entry.cacheHits));
      out.write(reinterpret_cast<const char *>(&entry.cacheMisses),
                sizeof(entry.cacheMisses));
    }
    uint64_t handleCount = m_fileHandles.size();
    out.write(reinterpret_cast<const char *>(&handleCount),
              sizeof(handleCount));
    for (const auto &[fd, handle] : m_fileHandles) {
      out.write(reinterpret_cast<const char *>(&fd), sizeof(fd));
      uint64_t pathLen = handle.path.size();
      out.write(reinterpret_cast<const char *>(&pathLen), sizeof(pathLen));
      out.write(handle.path.c_str(), pathLen);
      out.write(reinterpret_cast<const char *>(&handle.flags),
                sizeof(handle.flags));
      out.write(reinterpret_cast<const char *>(&handle.offset),
                sizeof(handle.offset));
      out.write(reinterpret_cast<const char *>(&handle.hostFd),
                sizeof(handle.hostFd));
      out.write(reinterpret_cast<const char *>(&handle.fd), sizeof(handle.fd));
      out.write(reinterpret_cast<const char *>(&handle.cacheHits),
                sizeof(handle.cacheHits));
      out.write(reinterpret_cast<const char *>(&handle.cacheMisses),
                sizeof(handle.cacheMisses));
    }
    uint64_t dirCount = m_directories.size();
    out.write(reinterpret_cast<const char *>(&dirCount), sizeof(dirCount));
    for (const auto &dir : m_directories) {
      uint64_t dirLen = dir.size();
      out.write(reinterpret_cast<const char *>(&dirLen), sizeof(dirLen));
      out.write(dir.c_str(), dirLen);
    }
    lock.unlock();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("PS4Filesystem state saved");
  } catch (const std::exception &e) {
    spdlog::error("SaveState failed: {}", e.what());
    lock.unlock();
  }
}

/**
 * @brief Loads the filesystem state from a stream.
 * @param in Input stream.
 */
void PS4Filesystem::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex, std::defer_lock);
  lock.lock();
  try {
    m_files.clear();
    m_fileHandles.clear();
    m_directories.clear();
    uint64_t fileCount;
    in.read(reinterpret_cast<char *>(&fileCount), sizeof(fileCount));
    for (uint64_t i = 0; i < fileCount; ++i) {
      uint64_t pathLen;
      in.read(reinterpret_cast<char *>(&pathLen), sizeof(pathLen));
      std::string path(pathLen, '\0');
      in.read(&path[0], pathLen);
      FileEntry entry;
      entry.path = path;
      uint64_t hostPathLen;
      in.read(reinterpret_cast<char *>(&hostPathLen), sizeof(hostPathLen));
      entry.hostPath.resize(hostPathLen);
      in.read(&entry.hostPath[0], hostPathLen);
      in.read(reinterpret_cast<char *>(&entry.size), sizeof(entry.size));
      in.read(reinterpret_cast<char *>(&entry.protection),
              sizeof(entry.protection));
      in.read(reinterpret_cast<char *>(&entry.mode), sizeof(entry.mode));
      in.read(reinterpret_cast<char *>(&entry.creationTime),
              sizeof(entry.creationTime));
      in.read(reinterpret_cast<char *>(&entry.modificationTime),
              sizeof(entry.modificationTime));
      in.read(reinterpret_cast<char *>(&entry.accessTime),
              sizeof(entry.accessTime));
      in.read(reinterpret_cast<char *>(&entry.isDir), sizeof(entry.isDir));
      uint64_t dataSize;
      in.read(reinterpret_cast<char *>(&dataSize), sizeof(dataSize));
      entry.data.resize(dataSize);
      if (dataSize > 0) {
        in.read(reinterpret_cast<char *>(entry.data.data()), dataSize);
      }
      in.read(reinterpret_cast<char *>(&entry.fileType),
              sizeof(entry.fileType));
      in.read(reinterpret_cast<char *>(&entry.ps4Permissions),
              sizeof(entry.ps4Permissions));
      uint64_t mountPointLen;
      in.read(reinterpret_cast<char *>(&mountPointLen), sizeof(mountPointLen));
      entry.mountPoint.resize(mountPointLen);
      in.read(&entry.mountPoint[0], mountPointLen);
      in.read(reinterpret_cast<char *>(&entry.isEncrypted),
              sizeof(entry.isEncrypted));
      in.read(reinterpret_cast<char *>(&entry.blockSize),
              sizeof(entry.blockSize));
      uint64_t checksumSize;
      in.read(reinterpret_cast<char *>(&checksumSize), sizeof(checksumSize));
      entry.checksum.resize(checksumSize);
      if (checksumSize > 0) {
        in.read(reinterpret_cast<char *>(entry.checksum.data()), checksumSize);
      }
      in.read(reinterpret_cast<char *>(&entry.present), sizeof(entry.present));
      in.read(reinterpret_cast<char *>(&entry.cacheHits),
              sizeof(entry.cacheHits));
      in.read(reinterpret_cast<char *>(&entry.cacheMisses),
              sizeof(entry.cacheMisses));
      m_files[path] = entry;
    }
    uint64_t handleCount;
    in.read(reinterpret_cast<char *>(&handleCount), sizeof(handleCount));
    for (uint64_t i = 0; i < handleCount; ++i) {
      int fd;
      in.read(reinterpret_cast<char *>(&fd), sizeof(fd));
      FileHandle handle;
      uint64_t pathLen;
      in.read(reinterpret_cast<char *>(&pathLen), sizeof(pathLen));
      handle.path.resize(pathLen);
      in.read(&handle.path[0], pathLen);
      in.read(reinterpret_cast<char *>(&handle.flags), sizeof(handle.flags));
      in.read(reinterpret_cast<char *>(&handle.offset), sizeof(handle.offset));
      in.read(reinterpret_cast<char *>(&handle.hostFd), sizeof(handle.hostFd));
      in.read(reinterpret_cast<char *>(&handle.fd), sizeof(handle.fd));
      in.read(reinterpret_cast<char *>(&handle.cacheHits),
              sizeof(handle.cacheHits));
      in.read(reinterpret_cast<char *>(&handle.cacheMisses),
              sizeof(handle.cacheMisses));
      m_fileHandles[fd] = handle;
      m_nextFd = std::max(m_nextFd, fd + 1);
    }
    uint64_t dirCount;
    in.read(reinterpret_cast<char *>(&dirCount), sizeof(dirCount));
    for (uint64_t i = 0; i < dirCount; ++i) {
      uint64_t dirLen;
      in.read(reinterpret_cast<char *>(&dirLen), sizeof(dirLen));
      std::string dir(dirLen, '\0');
      in.read(&dir[0], dirLen);
      m_directories.push_back(dir);
    }
    lock.unlock();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("PS4Filesystem state loaded");
  } catch (const std::exception &e) {
    spdlog::error("LoadState failed: {}", e.what());
    lock.unlock();
  }
}

/**
 * @brief Retrieves filesystem statistics.
 * @return Current statistics.
 */
FilesystemStats PS4Filesystem::GetStats() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_mutex, std::defer_lock);
  lock.lock();
  try {
    FilesystemStats currentStats = m_stats;
    lock.unlock();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return currentStats;
  } catch (const std::exception &e) {
    spdlog::error("GetStats failed: {}", e.what());
    lock.unlock();
    return m_stats;
  }
}

/**
 * @brief Sets filesystem settings.
 * @param settings New settings.
 */
void PS4Filesystem::SetSettings(const EnhancedSettings &settings) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    m_settings = settings;
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("SetSettings: Updated settings, latency={}us",
                 m_stats.totalLatencyUs);
  } catch (const std::exception &e) {
    spdlog::error("SetSettings failed: {}", e.what());
    m_stats.cacheMisses++;
  }
}

/**
 * @brief Gets current filesystem settings.
 * @return Current settings.
 */
const PS4Filesystem::EnhancedSettings &PS4Filesystem::GetSettings() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_mutex, std::defer_lock);
  lock.lock();
  try {
    lock.unlock();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return m_settings;
  } catch (const std::exception &e) {
    spdlog::error("GetSettings failed: {}", e.what());
    lock.unlock();
    return m_settings;
  }
}

/**
 * @brief Saves settings to a file.
 * @param filename File path.
 * @return True on success, false otherwise.
 */
bool PS4Filesystem::SaveSettings(const std::string &filename) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_mutex, std::defer_lock);
  lock.lock();
  try {
    std::ofstream out(filename, std::ios::binary);
    if (!out) {
      spdlog::error("SaveSettings: Cannot open file: {}", filename);
      lock.unlock();
      return false;
    }
    out.write(reinterpret_cast<const char *>(&m_settings), sizeof(m_settings));
    out.close();
    lock.unlock();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("PS4Filesystem settings saved to: {}", filename);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("SaveSettings failed: {}", e.what());
    lock.unlock();
    return false;
  }
}

/**
 * @brief Loads settings from a file.
 * @param filename File path.
 * @return True on success, false otherwise.
 */
bool PS4Filesystem::LoadSettings(const std::string &filename) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex, std::defer_lock);
  lock.lock();
  try {
    std::ifstream in(filename, std::ios::binary);
    if (!in) {
      spdlog::error("LoadSettings: Cannot open file: {}", filename);
      lock.unlock();
      return false;
    }
    in.read(reinterpret_cast<char *>(&m_settings), sizeof(m_settings));
    in.close();
    lock.unlock();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("PS4Filesystem settings loaded from: {}", filename);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("LoadSettings failed: {}", e.what());
    lock.unlock();
    return false;
  }
}

/**
 * @brief Loads a game executable.
 * @param gamePath Path to the game executable.
 * @return True on success, false otherwise.
 */
bool PS4Filesystem::LoadGame(const std::string &gamePath) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    m_loadedGamePath = gamePath;
    m_gameLoaded = !gamePath.empty();
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("LoadGame: Loaded {}, latency={}us", gamePath,
                 m_stats.totalLatencyUs);
    return m_gameLoaded;
  } catch (const std::exception &e) {
    spdlog::error("LoadGame failed: {}", e.what());
    m_stats.cacheMisses++;
    return false;
  }
}

/**
 * @brief Starts the loaded game.
 * @return True on success, false otherwise.
 */
bool PS4Filesystem::StartGame() {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_mutex, std::defer_lock);
  lock.lock();
  try {
    if (!m_gameLoaded || m_loadedGamePath.empty()) {
      spdlog::error("StartGame: No game loaded");
      lock.unlock();
      return false;
    }
    spdlog::info("Starting game: {}", m_loadedGamePath);
    // Game start logic would be handled by the emulator, not filesystem
    lock.unlock();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return true;
  } catch (const std::exception &e) {
    spdlog::error("StartGame failed: {}", e.what());
    lock.unlock();
    return false;
  }
}

/**
 * @brief Gets the path of the loaded game.
 * @return Game path, or empty string if none loaded.
 */
std::string PS4Filesystem::GetLoadedGamePath() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_mutex, std::defer_lock);
  lock.lock();
  try {
    std::string path = m_gameLoaded ? m_loadedGamePath : "";
    lock.unlock();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return path;
  } catch (const std::exception &e) {
    spdlog::error("GetLoadedGamePath failed: {}", e.what());
    lock.unlock();
    return "";
  }
}

/**
 * @brief Checks if a game is loaded.
 * @return True if a game is loaded, false otherwise.
 */
bool PS4Filesystem::IsGameLoaded() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_mutex, std::defer_lock);
  lock.lock();
  try {
    bool loaded = m_gameLoaded;
    lock.unlock();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return loaded;
  } catch (const std::exception &e) {
    spdlog::error("IsGameLoaded failed: {}", e.what());
    lock.unlock();
    return false;
  }
}

/**
 * @brief Gets the game directory path.
 * @return Game directory path, or empty string if none set.
 */
std::string PS4Filesystem::GetGameDirectory() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_mutex, std::defer_lock);
  lock.lock();
  try {
    std::string dir = m_gameDirectory;
    lock.unlock();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    return dir;
  } catch (const std::exception &e) {
    spdlog::error("GetGameDirectory failed: {}", e.what());
    lock.unlock();
    return "";
  }
}

/**
 * @brief Sets the game directory path.
 * @param path Game directory path.
 */
void PS4Filesystem::SetGameDirectory(const std::string &path) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    m_gameDirectory = path;
    lock.unlock();
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("Game directory set to: {}", path);
  } catch (const std::exception &e) {
    spdlog::error("SetGameDirectory failed: {}", e.what());
  }
}

/**
 * @brief Removes a directory and its contents.
 * @param path Directory path.
 * @return True on success, false otherwise.
 */
bool PS4Filesystem::RemoveDirectory(const std::string &path) {
  try {
    if (!std::filesystem::exists(path)) {
      spdlog::warn("Directory does not exist: {}", path);
      return true; // Consider it success if directory doesn't exist
    }

    if (!std::filesystem::is_directory(path)) {
      spdlog::error("Path is not a directory: {}", path);
      return false;
    }

    std::filesystem::remove_all(path);
    spdlog::info("Successfully removed directory: {}", path);
    return true;

  } catch (const std::filesystem::filesystem_error &e) {
    spdlog::error("Failed to remove directory {}: {}", path, e.what());
    return false;
  } catch (const std::exception &e) {
    spdlog::error("Unexpected error removing directory {}: {}", path, e.what());
    return false;
  }
}

// PS4-specific filesystem methods implementation

/**
 * @brief Initializes device files for PS4 emulation.
 */
void PS4Filesystem::InitializeDeviceFiles() {
  // Create standard PS4 device files
  m_deviceFiles["/dev/null"] = PS4FileType::Device;
  m_deviceFiles["/dev/zero"] = PS4FileType::Device;
  m_deviceFiles["/dev/random"] = PS4FileType::Device;
  m_deviceFiles["/dev/urandom"] = PS4FileType::Device;
  m_deviceFiles["/dev/console"] = PS4FileType::Device;
  m_deviceFiles["/dev/dipsw"] = PS4FileType::Device;
  m_deviceFiles["/dev/hid"] = PS4FileType::Device;
  m_deviceFiles["/dev/gc"] = PS4FileType::Device;
  m_deviceFiles["/dev/rng"] = PS4FileType::Device;

  spdlog::info("Initialized {} device files", m_deviceFiles.size());
}

/**
 * @brief Creates a device file with specified type.
 * @param path Virtual device path.
 * @param deviceType Type of device file.
 * @return True on success, false otherwise.
 */
bool PS4Filesystem::CreateDeviceFile(const std::string &path,
                                     PS4FileType deviceType) {
  std::unique_lock<std::shared_mutex> lock(m_mutex);
  try {
    m_deviceFiles[path] = deviceType;

    FileEntry entry;
    entry.path = path;
    entry.hostPath = MapToHostPath(path);
    entry.fileType = deviceType;
    entry.isDir = false;
    entry.present = true;
    entry.size = 0;
    entry.mode = 0666; // Device files are typically readable/writable
    entry.ps4Permissions = 0666;

    auto now = std::chrono::duration_cast<std::chrono::seconds>(
                   std::chrono::system_clock::now().time_since_epoch())
                   .count();
    entry.creationTime = now;
    entry.modificationTime = now;
    entry.accessTime = now;

    m_files[path] = entry;
    spdlog::debug("Created device file: {} -> {}", path, entry.hostPath);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("Failed to create device file {}: {}", path, e.what());
    return false;
  }
}

/**
 * @brief Handles access to device files with PS4-specific behavior.
 * @param path Device file path.
 * @param buffer Data buffer.
 * @param size Buffer size.
 * @param isWrite True for write access, false for read.
 * @return True on success, false otherwise.
 */
bool PS4Filesystem::HandleDeviceAccess(const std::string &path, void *buffer,
                                       size_t size, bool isWrite) {
  try {
    if (path == "/dev/null") {
      // Null device - discard writes, return zeros for reads
      if (isWrite) {
        return true; // Discard all writes
      } else {
        std::memset(buffer, 0, size);
        return true;
      }
    } else if (path == "/dev/zero") {
      // Zero device - return zeros for reads, discard writes
      if (isWrite) {
        return true; // Discard writes
      } else {
        std::memset(buffer, 0, size);
        return true;
      }
    } else if (path == "/dev/random" || path == "/dev/urandom") {
      if (isWrite) {
        return true; // Discard writes to random devices
      } else {
        // Generate random data
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<int> dis(0, 255);

        uint8_t *buf = static_cast<uint8_t *>(buffer);
        for (size_t i = 0; i < size; ++i) {
          buf[i] = static_cast<uint8_t>(dis(gen));
        }
        return true;
      }
    }
    return false;
  } catch (const std::exception &e) {
    spdlog::error("HandleDeviceAccess failed for {}: {}", path, e.what());
    return false;
  }
}

PS4FileType PS4Filesystem::DetermineFileType(const std::string &path) const {
  try {
    if (path.empty()) {
      return PS4FileType::Regular;
    }

    // Check for directory
    if (path.back() == '/' || path.back() == '\\') {
      return PS4FileType::Directory;
    }

    // Get file extension
    size_t dotPos = path.find_last_of('.');
    if (dotPos == std::string::npos) {
      return PS4FileType::Regular;
    }

    std::string ext = path.substr(dotPos + 1);
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);

    if (ext == "elf" || ext == "bin") {
      return PS4FileType::Regular; // Use existing enum value
    } else if (ext == "so" || ext == "dll" || ext == "sprx") {
      return PS4FileType::Regular; // Use existing enum value
    } else if (ext == "pkg") {
      return PS4FileType::Regular; // Use existing enum value
    } else if (ext == "sfo") {
      return PS4FileType::System; // Use existing enum value
    } else if (ext == "txt" || ext == "log") {
      return PS4FileType::Regular; // Use existing enum value
    }

    return PS4FileType::Regular;
  } catch (const std::exception &e) {
    spdlog::error("DetermineFileType failed for {}: {}", path, e.what());
    return PS4FileType::Regular;
  }
}

std::vector<uint8_t>
PS4Filesystem::CalculateChecksum(const std::vector<uint8_t> &data) const {
  try {
    uint32_t crc = 0xFFFFFFFF;
    const uint32_t polynomial = 0xEDB88320;

    for (uint8_t byte : data) {
      crc ^= byte;
      for (int i = 0; i < 8; ++i) {
        if (crc & 1) {
          crc = (crc >> 1) ^ polynomial;
        } else {
          crc >>= 1;
        }
      }
    }
    crc ^= 0xFFFFFFFF;

    std::vector<uint8_t> checksum(4);
    checksum[0] = (crc >> 24) & 0xFF;
    checksum[1] = (crc >> 16) & 0xFF;
    checksum[2] = (crc >> 8) & 0xFF;
    checksum[3] = crc & 0xFF;
    return checksum;
  } catch (const std::exception &e) {
    spdlog::error("Checksum calculation failed: {}", e.what());
    return {};
  }
}

bool PS4Filesystem::WriteFile(const std::string &path, const void *data,
                              size_t size) {
  try {
    // Open file for writing, create if doesn't exist
    int fd = OpenFile(path, O_WRONLY | O_CREAT | O_TRUNC, 0644);
    if (fd < 0) {
      spdlog::error("WriteFile: Failed to open file for writing: {}", path);
      return false;
    }

    // Write data to file descriptor
    ssize_t bytesWritten = WriteFile(fd, data, size);
    CloseFile(fd);

    if (bytesWritten != static_cast<ssize_t>(size)) {
      spdlog::error("WriteFile: Failed to write all data to {}, "
                    "expected {}, wrote {}",
                    path, size, bytesWritten);
      return false;
    }

    spdlog::debug("WriteFile: Successfully wrote {} bytes to {}", size, path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("WriteFile failed for {}: {}", path, e.what());
    return false;
  }
}

bool PS4Filesystem::CreateVirtualDirectory(const std::string &path) {
  try {
    std::unique_lock<std::shared_mutex> lock(m_mutex);

    // Add to directories list if not already present
    if (std::find(m_directories.begin(), m_directories.end(), path) ==
        m_directories.end()) {
      m_directories.push_back(path);
    }

    // Create directory entry in file system
    FileEntry entry;
    entry.fileType = PS4FileType::Directory;
    entry.size = 0;
    entry.mode = 0755;
    entry.modificationTime = std::time(nullptr);
    entry.present = true;
    entry.isDir = true;

    m_files[path] = entry;

    spdlog::debug("Created virtual directory: {}", path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("CreateVirtualDirectory failed for {}: {}", path, e.what());
    return false;
  }
}

bool PS4Filesystem::ValidatePS4Permissions(const std::string &path, int mode) {
  try {
    std::shared_lock<std::shared_mutex> lock(m_mutex);

    auto it = m_files.find(path);
    if (it == m_files.end()) {
      return false;
    }

    const FileEntry &entry = it->second;

    // Check read permission
    if ((mode & O_RDONLY) && !(entry.mode & 0444)) {
      return false;
    }

    // Check write permission
    if ((mode & O_WRONLY) && !(entry.mode & 0222)) {
      return false;
    }

    // Check execute permission (define O_EXEC if not available)
#ifndef O_EXEC
#define O_EXEC 0x040000
#endif
    if ((mode & O_EXEC) && !(entry.mode & 0111)) {
      return false;
    }

    return true;
  } catch (const std::exception &e) {
    spdlog::error("ValidatePS4Permissions failed for {}: {}", path, e.what());
    return false;
  }
}

std::string PS4Filesystem::MapToHostPath(const std::string &virtualPath) {
  try {
    std::shared_lock<std::shared_mutex> lock(m_mutex);

    // Handle special paths
    if (virtualPath.starts_with("/app0/")) {
      return m_rootPath + "/app0" + virtualPath.substr(5);
    } else if (virtualPath.starts_with("/system/")) {
      return m_rootPath + "/system" + virtualPath.substr(7);
    } else if (virtualPath.starts_with("/user/")) {
      return m_rootPath + "/user" + virtualPath.substr(5);
    } else if (virtualPath.starts_with("/tmp/")) {
      return m_rootPath + "/tmp" + virtualPath.substr(4);
    }

    // Default mapping
    return m_rootPath + virtualPath;
  } catch (const std::exception &e) {
    spdlog::error("MapToHostPath failed for {}: {}", virtualPath, e.what());
    return virtualPath;
  }
}

bool PS4Filesystem::InitializePFS() {
  try {
    std::unique_lock<std::shared_mutex> lock(m_mutex);

    // Create standard PS4 directories
    std::vector<std::string> standardDirs = {"/app0", "/system", "/user",
                                             "/tmp",  "/dev",    "/proc"};

    for (const auto &dir : standardDirs) {
      FileEntry entry;
      entry.fileType = PS4FileType::Directory;
      entry.size = 0;
      entry.mode = 0755;
      entry.modificationTime = std::time(nullptr);
      entry.present = true;
      entry.isDir = true;

      m_files[dir] = entry;
      m_directories.push_back(dir);
    }

    // Create device files
    std::vector<std::string> deviceFiles = {"/dev/null", "/dev/zero",
                                            "/dev/random", "/dev/urandom"};

    for (const auto &dev : deviceFiles) {
      FileEntry entry;
      entry.fileType = PS4FileType::Device;
      entry.size = 0;
      entry.mode = 0666;
      entry.modificationTime = std::time(nullptr);
      entry.present = true;
      entry.isDir = false;

      m_files[dev] = entry;
      m_deviceFiles[dev] = PS4FileType::Device;
    }

    spdlog::info("PS4 Filesystem initialized successfully");
    return true;
  } catch (const std::exception &e) {
    spdlog::error("InitializePFS failed: {}", e.what());
    return false;
  }
}

bool PS4Filesystem::EncryptFile(const std::string &path,
                                const std::vector<uint8_t> &key) {
  try {
    std::unique_lock<std::shared_mutex> lock(m_mutex);

    auto it = m_files.find(path);
    if (it == m_files.end()) {
      return false;
    }

    FileEntry &entry = it->second;
    if (entry.data.empty()) {
      return true; // Nothing to encrypt
    }

    // Simple XOR encryption for demonstration
    for (size_t i = 0; i < entry.data.size(); ++i) {
      entry.data[i] ^= key[i % key.size()];
    }

    entry.isEncrypted = true;
    entry.encryptionKey = key;

    spdlog::debug("Encrypted file: {}", path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("EncryptFile failed for {}: {}", path, e.what());
    return false;
  }
}

bool PS4Filesystem::DecryptFile(const std::string &path) {
  try {
    std::unique_lock<std::shared_mutex> lock(m_mutex);

    auto it = m_files.find(path);
    if (it == m_files.end()) {
      return false;
    }

    FileEntry &entry = it->second;
    if (!entry.isEncrypted || entry.encryptionKey.empty()) {
      return true; // Nothing to decrypt
    }

    // Simple XOR decryption (same as encryption for XOR)
    for (size_t i = 0; i < entry.data.size(); ++i) {
      entry.data[i] ^= entry.encryptionKey[i % entry.encryptionKey.size()];
    }

    entry.isEncrypted = false;
    entry.encryptionKey.clear();

    spdlog::debug("Decrypted file: {}", path);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("DecryptFile failed for {}: {}", path, e.what());
    return false;
  }
}

} // namespace ps4
