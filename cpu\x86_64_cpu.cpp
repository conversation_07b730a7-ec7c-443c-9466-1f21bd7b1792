// Copyright 2025 <Copyright Owner>

#include <algorithm>
#include <chrono>
#include <fstream>
#include <immintrin.h>
#include <istream>
#include <mutex>
#include <ostream>
#include <queue>
#include <stdexcept>
#include <string>
#include <thread>
#include <vector>

#include <spdlog/spdlog.h>

#include "cache/cache.h"
#include "cpu/decoded_instruction.h"
#include "cpu/instruction_decoder.h"
#include "cpu/x86_64_cpu.h"
#include "cpu/x86_64_pipeline.h"
#include "emulator/interrupt_handler.h"
#include "jit/x86_64_jit_compiler.h"
#include "memory/tlb.h"
#include "ps4/ps4_emulator.h"

namespace x86_64 {
struct CPUException : std::runtime_error {
  explicit CPUException(const std::string &msg) : std::runtime_error(msg) {}
};

X86_64CPU::X86_64CPU(ps4::PS4Emulator &emulator, ps4::PS4MMU &mmu,
                     uint32_t cpuId)
    : m_emulator(emulator), mmu(mmu), m_cpuId(cpuId), registers{},
      xmmRegisters{}, rflags(DEFAULT_RFLAGS), running(false),
      jit(std::make_unique<X86_64JITCompiler>(this)),
      decoder(std::make_unique<InstructionDecoder>()),
      pipeline(std::make_unique<Pipeline>(*this)), apic(cpuId),
      utilization(0.0f) {
  // IMPROVEMENT: Use constants and better initialization
  registers.fill(0);

  // Initialize XMM registers with proper AVX support detection
#ifdef __AVX__
  xmmRegisters.fill(_mm256_setzero_si256());
#else
  for (auto &reg : xmmRegisters) {
    reg = _mm256_castsi128_si256(_mm_setzero_si128());
  }
#endif

  spdlog::info("X86_64CPU[{}] created with {} XMM registers", m_cpuId,
               XMM_REGISTER_COUNT);
}

X86_64CPU::~X86_64CPU() {
  Shutdown();
  spdlog::info("X86_64CPU[{}] destroyed", m_cpuId);
}

bool X86_64CPU::Initialize() {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);

  // IMPROVEMENT: Use constants and better error handling
  try {
    running = true;
    tlb.Clear();
    interruptQueue = {};

    // Reset all general-purpose registers
    registers.fill(0);

    // Reset all XMM/YMM registers
    xmmRegisters.fill(_mm256_setzero_si256());

    // Set default stack pointer
    registers[static_cast<size_t>(Register::RSP)] = DEFAULT_STACK_POINTER;

    // Set default RFLAGS
    rflags = DEFAULT_RFLAGS;

    // Reset performance counters
    utilization = 0.0f;

    // Reset pipeline state
    pipeline->ResetStats();

    spdlog::info(
        "X86_64CPU[{}] initialized: RIP=0x{:x}, RSP=0x{:x}, RFLAGS=0x{:x}",
        m_cpuId, _getRegister(Register::RIP), _getRegister(Register::RSP),
        rflags);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}] initialization failed: {}", m_cpuId, e.what());
    running = false;
    return false;
  }
}

void X86_64CPU::Shutdown() {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  running = false;
  tlb.Clear();
  interruptQueue = {};
  utilization = 0.0f;

  pipeline->Flush();
  spdlog::info("X86_64CPU[{}] shut down", m_cpuId);
}

void X86_64CPU::ResetState() {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);

  // IMPROVEMENT: Use constants and comprehensive reset
  utilization = 0.0f;
  interruptQueue = {};

  // Flush pipeline before resetting registers
  pipeline->Flush();

  // Reset all registers to zero
  registers.fill(0);

  // Reset XMM registers
  xmmRegisters.fill(_mm256_setzero_si256());

  // Set default values using constants
  registers[static_cast<size_t>(Register::RSP)] = DEFAULT_STACK_POINTER;
  rflags = DEFAULT_RFLAGS;

  // Reset control registers
  cr3 = 0;
  cr2 = 0;
  cs = 0;
  ss = 0;
  tssBase = 0;
  kernelSS = 0;
  processId = 0;

  spdlog::info("X86_64CPU[{}] state reset to defaults", m_cpuId);
}

void X86_64CPU::QueueInterrupt(uint8_t vector, uint8_t priority) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  interruptQueue.push({vector, priority});
  spdlog::debug("X86_64CPU[{}]: Queued interrupt vector=0x{:x}, priority={}",
                m_cpuId, vector, priority);
}

void X86_64CPU::TriggerInterrupt(uint8_t vector, uint64_t errorCode,
                                 bool isSoftwareInterrupt) {
  // CRITICAL FIX: Prevent deadlock by not holding CPU mutex when calling
  // InterruptHandler The InterruptHandler will call back into CPU methods, so
  // we must not hold our mutex
  InterruptHandler &handler = m_emulator.GetInterruptHandler();
  try {
    // Call interrupt handler without holding our mutex to prevent deadlock
    handler.HandleInterrupt(vector, errorCode, isSoftwareInterrupt);
    spdlog::debug("X86_64CPU[{}]: Triggered interrupt vector=0x{:x}", m_cpuId,
                  vector);
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Failed to handle interrupt vector=0x{:x}: {}",
                  m_cpuId, vector, e.what());
    // Only acquire our mutex when we need to modify our internal state
    std::unique_lock<std::recursive_timed_mutex> lock(mutex);
    interruptQueue.push({vector, 0});
  }
}

void X86_64CPU::ExecuteCycle() {
  // CRITICAL DEADLOCK FIX: Use try_lock to prevent blocking deadlocks
  std::unique_lock<std::recursive_timed_mutex> lock(mutex, std::try_to_lock);
  if (!lock.owns_lock()) {
    // If we can't acquire the lock immediately, it means potential deadlock
    spdlog::warn("X86_64CPU[{}]: Could not acquire CPU mutex immediately - "
                 "potential deadlock avoided",
                 m_cpuId);

    // Try once more with a timeout
    auto timeout = std::chrono::milliseconds(10);
    if (!mutex.try_lock_for(timeout)) {
      spdlog::critical(
          "X86_64CPU[{}]: DEADLOCK PREVENTED - CPU mutex acquisition timeout",
          m_cpuId);
      return;
    }
    lock = std::unique_lock<std::recursive_timed_mutex>(mutex, std::adopt_lock);
  }

  if (!running) {
    spdlog::warn("X86_64CPU[{}]: Not running, skipping cycle", m_cpuId);
    return;
  }

  auto start = std::chrono::steady_clock::now();
  try {
    // CRITICAL FIX: Handle interrupts without holding our mutex to prevent
    // deadlocks
    if (GetFlag(FLAG_IF) && !interruptQueue.empty()) {
      auto irq = interruptQueue.top();
      interruptQueue.pop();

      // CRITICAL: Record state before releasing lock
      uint64_t currentRip = _getRegister(Register::RIP);

      // Release our mutex before calling TriggerInterrupt to prevent deadlock
      lock.unlock();

      // CRITICAL FIX: Validate we have a valid RIP before triggering interrupt
      if (currentRip == 0 || currentRip < 0x1000) {
        spdlog::critical("X86_64CPU[{}]: Invalid RIP 0x{:x} before interrupt - "
                         "preventing execution",
                         m_cpuId, currentRip);
        return;
      }

      TriggerInterrupt(irq.vector, 0, false);

      // Reacquire lock for UpdateUtilization - use try_lock again
      if (!mutex.try_lock()) {
        spdlog::warn("X86_64CPU[{}]: Could not reacquire mutex after interrupt",
                     m_cpuId);
        return;
      }
      lock = std::unique_lock<std::recursive_timed_mutex>(mutex, std::adopt_lock);
      UpdateUtilization(start);
      return;
    }

    uint64_t rip = _getRegister(Register::RIP);

    // CRITICAL FIX: Validate RIP before any execution
    if (rip == 0 || rip < 0x1000 || rip >= 0x800000000000ULL) {
      spdlog::critical(
          "X86_64CPU[{}]: CRITICAL - Invalid RIP 0x{:x} in ExecuteCycle",
          m_cpuId, rip);
      running = false;
      return;
    }

    // Release lock before JIT execution to prevent deadlocks
    lock.unlock();

    // CRITICAL FIX: JIT execution can trigger interrupts, so don't hold our
    // mutex
    bool jitSuccess = false;
    try {
      jitSuccess = jit->ExecuteCompiledCode(rip);
    } catch (const std::exception &e) {
      spdlog::error("X86_64CPU[{}]: JIT execution exception at 0x{:x}: {}",
                    m_cpuId, rip, e.what());
      jitSuccess = false;
    }

    if (jitSuccess) {
      // Try to reacquire lock for utilization update
      if (mutex.try_lock()) {
        lock = std::unique_lock<std::recursive_timed_mutex>(mutex, std::adopt_lock);
        UpdateUtilization(start);
      }
      return;
    }

    spdlog::debug("X86_64CPU[{}]: JIT failed at 0x{:x}, using pipeline",
                  m_cpuId, rip);

    // CRITICAL FIX: Pipeline execution can also trigger interrupts - prevent
    // deadlock
    try {
      pipeline->Step();
    } catch (const std::exception &e) {
      spdlog::error("X86_64CPU[{}]: Pipeline execution exception at 0x{:x}: {}",
                    m_cpuId, rip, e.what());
    }

    // Try to reacquire lock for utilization update
    if (mutex.try_lock()) {
      lock = std::unique_lock<std::recursive_timed_mutex>(mutex, std::adopt_lock);
      UpdateUtilization(start);
    }
  } catch (const CPUException &e) {
    // Ensure we have the lock for accessing GetRegister and running
    if (!lock.owns_lock() && mutex.try_lock()) {
      lock = std::unique_lock<std::recursive_timed_mutex>(mutex, std::adopt_lock);
    }

    if (lock.owns_lock()) {
      uint64_t errorRip = _getRegister(Register::RIP);
      spdlog::critical("X86_64CPU[{}]: Exception at RIP=0x{:x}: {}", m_cpuId,
                       errorRip, e.what());
      running = false;
    } else {
      spdlog::critical("X86_64CPU[{}]: Exception (could not acquire lock): {}",
                       m_cpuId, e.what());
    }
    throw;
  }
}

void X86_64CPU::UpdateUtilization(
    const std::chrono::steady_clock::time_point &start) {
  auto end = std::chrono::steady_clock::now();
  auto duration =
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
  utilization = (std::min)(1.0f, utilization + duration / 1000.0f);
  spdlog::trace("X86_64CPU[{}] utilization updated: {:.2f}%", m_cpuId,
                utilization * 100.0f);
}

bool X86_64CPU::CalculateParity(uint64_t value) {
  uint8_t byte = value & 0xFF;
  int count = 0;
  for (int i = 0; i < 8; ++i) {
    if ((byte >> i) & 1)
      count++;
  }
  return (count % 2 == 0);
}

void X86_64CPU::UpdateArithmeticFlags(uint64_t op1, uint64_t op2,
                                      uint64_t result, uint8_t sizeInBits,
                                      bool isSubtract) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);

  // CRITICAL FIX: Validate size parameter to prevent undefined behavior
  if (sizeInBits == 0 || sizeInBits > 64) {
    spdlog::error("X86_64CPU[{}]: Invalid size for flag calculation: {} bits",
                  m_cpuId, sizeInBits);
    return;
  }

  uint64_t mask =
      (sizeInBits == 64) ? 0xFFFFFFFFFFFFFFFFULL : (1ULL << sizeInBits) - 1;
  uint64_t signBit = 1ULL << (sizeInBits - 1);

  result &= mask;
  op1 &= mask;
  op2 &= mask;

  // Clear only arithmetic flags, preserve other flags
  rflags &= ~ARITHMETIC_FLAGS_MASK;

  // Zero flag
  if (result == 0)
    rflags |= FLAG_ZF;

  // Sign flag
  if (result & signBit)
    rflags |= FLAG_SF;

  // Parity flag
  if (CalculateParity(result))
    rflags |= FLAG_PF;

  // Carry and Auxiliary Carry flags
  if (isSubtract) {
    // CRITICAL FIX: Proper carry flag calculation for subtraction
    if (op1 < op2)
      rflags |= FLAG_CF;
    // Auxiliary carry for subtraction (borrow from bit 4)
    if ((op1 & 0xF) < (op2 & 0xF))
      rflags |= FLAG_AF;
  } else {
    // CRITICAL FIX: Proper carry flag calculation for addition
    // Check if addition caused overflow beyond the mask
    uint64_t fullResult = op1 + op2;
    if (fullResult > mask)
      rflags |= FLAG_CF;
    // Auxiliary carry for addition (carry from bit 3 to bit 4)
    if ((op1 & 0xF) + (op2 & 0xF) > 0xF)
      rflags |= FLAG_AF;
  }

  // Overflow flag calculation
  bool s1 = (op1 & signBit) != 0;
  bool s2 = (op2 & signBit) != 0;
  bool sr = (result & signBit) != 0;

  if (isSubtract) {
    // Overflow occurs when subtracting a negative from positive gives negative,
    // or subtracting positive from negative gives positive
    if ((s1 && !s2 && !sr) || (!s1 && s2 && sr))
      rflags |= FLAG_OF;
  } else {
    // Overflow occurs when adding two numbers of same sign gives opposite sign
    if (s1 == s2 && s1 != sr)
      rflags |= FLAG_OF;
  }

  spdlog::trace("X86_64CPU[{}] flags updated: RFLAGS=0x{:x}", m_cpuId, rflags);
}

uint64_t
X86_64CPU::ReadOperandValue(const DecodedInstruction::Operand &operand) const {
  uint64_t nextRip = GetRegister(Register::RIP);
  uint8_t sizeInBits = operand.size;

  switch (operand.type) {
  case DecodedInstruction::Operand::Type::REGISTER:
    return GetRegister(operand.reg) & ((1ULL << sizeInBits) - 1);
  case DecodedInstruction::Operand::Type::IMMEDIATE:
    switch (sizeInBits) {
    case 8:
      return static_cast<uint64_t>(static_cast<int8_t>(operand.immediate));
    case 16:
      return static_cast<uint64_t>(static_cast<int16_t>(operand.immediate));
    case 32:
      return static_cast<uint64_t>(static_cast<int32_t>(operand.immediate));
    case 64:
      return operand.immediate;
    default:
      spdlog::error("X86_64CPU[{}]: Invalid immediate size: {} bits", m_cpuId,
                    sizeInBits);
      throw CPUException("Invalid immediate size");
    }
  case DecodedInstruction::Operand::Type::MEMORY: {
    uint64_t addr = CalculateMemoryAddress(operand);
    size_t bytesToRead = sizeInBits / 8;
    uint64_t value = 0;
    mmu.ReadVirtual(addr, &value, bytesToRead, GetProcessId());
    spdlog::trace("Read memory at 0x{:x}, value=0x{:x}, size={}", addr, value,
                  bytesToRead);
    return value & ((1ULL << sizeInBits) - 1);
  }
  case DecodedInstruction::Operand::Type::XMM: {
    std::unique_lock<std::recursive_timed_mutex> lock(mutex);
    __m256i reg = xmmRegisters[static_cast<size_t>(operand.reg) -
                               static_cast<size_t>(Register::XMM0)];
    alignas(32) uint64_t tmp[4];
    _mm256_store_si256((__m256i *)tmp, reg);

    // CRITICAL FIX: Handle different SIMD data sizes correctly
    uint64_t result = 0;
    switch (sizeInBits) {
    case 32: // Single 32-bit value (e.g., scalar float)
      result = tmp[0] & 0xFFFFFFFF;
      break;
    case 64: // Single 64-bit value (e.g., scalar double)
      result = tmp[0];
      break;
    case 128: // Full XMM register (128-bit) - CRITICAL FIX: Proper handling for
              // SIMD operations
      // For 128-bit operations, this function should ideally not be used
      // Callers should use ReadXmmOperandValue() for full register access
      // However, for compatibility with existing code that expects a uint64_t
      // return, we'll return the lower 64 bits but ensure proper SIMD context
      spdlog::warn(
          "ReadOperandValue called for 128-bit XMM operation at XMM{} - "
          "returning lower 64 bits. Consider using ReadXmmOperandValue() "
          "for full SIMD data access",
          static_cast<int>(operand.reg));
      result = tmp[0]; // Return lower 64 bits with proper warning
      break;
    case 256: // Full YMM register (256-bit) - CRITICAL FIX: Proper handling for
              // SIMD operations
      // For 256-bit operations, this function should ideally not be used
      // Callers should use ReadXmmOperandValue() for full register access
      // However, for compatibility with existing code that expects a uint64_t
      // return, we'll return the lower 64 bits but ensure proper SIMD context
      spdlog::warn(
          "ReadOperandValue called for 256-bit YMM operation at XMM{} - "
          "returning lower 64 bits. Consider using ReadXmmOperandValue() "
          "for full SIMD data access",
          static_cast<int>(operand.reg));
      result = tmp[0]; // Return lower 64 bits with proper warning
      break;
    default:
      result = tmp[0] & ((1ULL << sizeInBits) - 1);
      break;
    }

    spdlog::trace("Read XMM{}: value=0x{:x}, size={} bits",
                  static_cast<int>(operand.reg), result, sizeInBits);
    return result;
  }
  default:
    spdlog::error("X86_64CPU[{}]: Invalid operand type: {}", m_cpuId,
                  static_cast<int>(operand.type));
    throw CPUException("Invalid operand type");
  }
}

void X86_64CPU::WriteOperandValue(const DecodedInstruction::Operand &operand,
                                  uint64_t value) {
  uint8_t sizeInBits = operand.size;

  switch (operand.type) {
  case DecodedInstruction::Operand::Type::REGISTER:
    SetRegister(operand.reg, value & ((1ULL << sizeInBits) - 1));
    spdlog::trace("Write register {}: value=0x{:x}",
                  static_cast<int>(operand.reg), value);
    break;
  case DecodedInstruction::Operand::Type::MEMORY:
    WriteMemoryOperand(operand, value);
    break;
  case DecodedInstruction::Operand::Type::XMM: {
    // Handle different SIMD data sizes correctly
    __m256i newValue;
    switch (sizeInBits) {
    case 32: // Single 32-bit value (e.g., scalar float)
      // Zero upper bits, set lower 32 bits
      newValue =
          _mm256_set_epi32(0, 0, 0, 0, 0, 0, 0, static_cast<int32_t>(value));
      break;
    case 64: // Single 64-bit value (e.g., scalar double)
      // Zero upper bits, set lower 64 bits
      newValue = _mm256_set_epi64x(0, 0, 0, static_cast<int64_t>(value));
      break;
    case 128: // Full XMM register (128-bit) - CRITICAL FIX
      // For 128-bit operations, this function should not be used
      // Use WriteXmmOperandValue() for full register operations
      spdlog::warn("WriteOperandValue called for 128-bit XMM operation - use "
                   "WriteXmmOperandValue instead");
      // As fallback, set lower 64 bits and zero upper bits
      newValue = _mm256_set_epi64x(0, 0, 0, static_cast<int64_t>(value));
      break;
    case 256: // Full YMM register (256-bit) - CRITICAL FIX
      // For 256-bit operations, this function should not be used
      // Use WriteXmmOperandValue() for full register operations
      spdlog::warn("WriteOperandValue called for 256-bit YMM operation - use "
                   "WriteXmmOperandValue instead");
      // As fallback, broadcast to all lanes (incorrect but better than nothing)
      newValue = _mm256_set1_epi64x(static_cast<int64_t>(value));
      break;
    default:
      // Default behavior: zero upper bits, set lower bits
      newValue = _mm256_set_epi64x(
          0, 0, 0, static_cast<int64_t>(value & ((1ULL << sizeInBits) - 1)));
      break;
    }
    WriteXmmOperand(operand, newValue);
    spdlog::trace("Write XMM{}: value=0x{:x}, size={} bits",
                  static_cast<int>(operand.reg), value, sizeInBits);
    break;
  }
  default:
    spdlog::error("X86_64CPU[{}]: Invalid operand type for write: {}", m_cpuId,
                  static_cast<int>(operand.type));
    throw CPUException("Write to invalid operand type");
  }
}

uint64_t X86_64CPU::CalculateMemoryAddress(
    const DecodedInstruction::Operand &operand) const {
  if (operand.type != DecodedInstruction::Operand::Type::MEMORY) {
    spdlog::error("X86_64CPU[{}]: Invalid operand type for memory address "
                  "calculation: {}",
                  m_cpuId, static_cast<int>(operand.type));
    throw CPUException("Invalid operand type for memory address");
  }

  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  uint64_t addr = 0;
  if (operand.memory.base == Register::RIP) {
    addr = _getRegister(Register::RIP);
  } else if (operand.memory.base != Register::NONE) {
    addr = _getRegister(operand.memory.base);
  }
  if (operand.memory.index != Register::NONE) {
    addr += _getRegister(operand.memory.index) * operand.memory.scale;
  }
  addr += static_cast<int64_t>(operand.memory.displacement);
  spdlog::trace("Calculated memory address: 0x{:x}", addr);
  return addr;
}

void X86_64CPU::WriteMemoryOperand(const DecodedInstruction::Operand &operand,
                                   uint64_t value) {
  uint8_t sizeInBits = operand.size;
  uint64_t addr = CalculateMemoryAddress(operand);
  size_t bytesToWrite = sizeInBits / 8;
  mmu.WriteVirtual(addr, &value, bytesToWrite, GetProcessId());
  spdlog::trace("Write memory at 0x{:x}, value=0x{:x}, size={}", addr, value,
                bytesToWrite);
}

void X86_64CPU::WriteXmmOperand(const DecodedInstruction::Operand &operand,
                                const __m256i &value) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  xmmRegisters[static_cast<size_t>(operand.reg) -
               static_cast<size_t>(Register::XMM0)] = value;
  spdlog::trace("Write XMM{}: value set", static_cast<int>(operand.reg));
}

__m256i X86_64CPU::ReadXmmOperandValue(
    const DecodedInstruction::Operand &operand) const {
  if (operand.type != DecodedInstruction::Operand::Type::XMM) {
    spdlog::error("X86_64CPU[{}]: Invalid operand type for XMM read: {}",
                  m_cpuId, static_cast<int>(operand.type));
    throw CPUException("Invalid operand type for XMM read");
  }

  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  __m256i reg = xmmRegisters[static_cast<size_t>(operand.reg) -
                             static_cast<size_t>(Register::XMM0)];

  uint8_t sizeInBits = operand.size;
  switch (sizeInBits) {
  case 128: // XMM register (128-bit)
    // Zero upper 128 bits, keep lower 128 bits
    return _mm256_inserti128_si256(_mm256_setzero_si256(),
                                   _mm256_extracti128_si256(reg, 0), 0);
  case 256: // YMM register (256-bit)
    return reg;
  default:
    // For smaller sizes, return the full register and let caller handle
    // extraction
    return reg;
  }
}

void X86_64CPU::WriteXmmOperandValue(const DecodedInstruction::Operand &operand,
                                     const __m256i &value) {
  if (operand.type != DecodedInstruction::Operand::Type::XMM) {
    spdlog::error("X86_64CPU[{}]: Invalid operand type for XMM write: {}",
                  m_cpuId, static_cast<int>(operand.type));
    throw CPUException("Invalid operand type for XMM write");
  }

  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  size_t regIndex =
      static_cast<size_t>(operand.reg) - static_cast<size_t>(Register::XMM0);

  uint8_t sizeInBits = operand.size;
  switch (sizeInBits) {
  case 128: // XMM register (128-bit)
    // Zero upper 128 bits, set lower 128 bits
    xmmRegisters[regIndex] = _mm256_inserti128_si256(
        _mm256_setzero_si256(), _mm256_extracti128_si256(value, 0), 0);
    break;
  case 256: // YMM register (256-bit)
    xmmRegisters[regIndex] = value;
    break;
  default:
    // For other sizes, preserve upper bits and modify only the relevant portion
    xmmRegisters[regIndex] = value;
    break;
  }

  spdlog::trace("Write XMM{}: full register updated, size={} bits",
                static_cast<int>(operand.reg), sizeInBits);
}

void X86_64CPU::FetchDecodeExecute() {
  uint64_t rip = GetRegister(Register::RIP);
  DecodedInstruction instr;

  try {
    // CRITICAL FIX: Comprehensive RIP validation to prevent null pointer
    // execution
    if (rip == 0 || rip == 0xDEADBEEF || rip == 0xCCCCCCCC ||
        rip == 0xFEEEFEEE) {
      spdlog::critical("X86_64CPU[{}]: CRITICAL - Invalid RIP detected: 0x{:x}",
                       m_cpuId, rip);
      spdlog::critical(
          "X86_64CPU[{}]: This indicates severe memory corruption or deadlock",
          m_cpuId);

      // Emergency RIP recovery - try to set to a safe default or previous known
      // good value
      std::unique_lock<std::recursive_timed_mutex> lock(mutex);
      if (registers[static_cast<size_t>(Register::RSP)] != 0) {
        // Try to recover from stack
        try {
          uint64_t stackTop = registers[static_cast<size_t>(Register::RSP)];
          if (stackTop > 0x1000 && stackTop < 0x800000000000ULL) {
            lock.unlock();
            uint64_t returnAddr = 0;
            if (mmu.ReadVirtual(stackTop, &returnAddr, 8, processId) &&
                returnAddr > 0x1000) {
              lock.lock();
              _setRegister(Register::RIP, returnAddr);
              _setRegister(Register::RSP, stackTop + 8);
              spdlog::warn("X86_64CPU[{}]: Emergency RIP recovery to 0x{:x}",
                           m_cpuId, returnAddr);
              lock.unlock();
              return;
            }
          }
        } catch (...) {
          // Recovery failed, continue with error handling
        }
      }

      // If recovery fails, stop execution to prevent cascade
      running = false;
      lock.unlock();
      TriggerInterrupt(EXC_GP, 0, false);
      return;
    }

    // Additional validation for suspicious addresses
    if (rip < 0x1000 || rip >= 0x800000000000ULL) {
      spdlog::error(
          "X86_64CPU[{}]: Suspicious RIP address: 0x{:x} (outside valid range)",
          m_cpuId, rip);
      TriggerInterrupt(EXC_GP, 0, false);
      return;
    }

    // IMPROVEMENT: Use constant for instruction buffer size and better
    // validation
    std::vector<uint8_t> instBuffer(
        INSTRUCTION_BUFFER_SIZE); // CRITICAL FIX: Safe memory read with
                                  // validation
    try {
      if (!mmu.ReadVirtual(rip, instBuffer.data(), instBuffer.size(),
                           GetProcessId())) {
        spdlog::error("X86_64CPU[{}]: Memory read failed at RIP=0x{:x}",
                      m_cpuId, rip);
        TriggerInterrupt(EXC_PF, rip, false);
        return;
      }
    } catch (const std::exception &e) {
      spdlog::error("X86_64CPU[{}]: Exception reading memory at RIP=0x{:x}: {}",
                    m_cpuId, rip, e.what());
      TriggerInterrupt(EXC_PF, rip, false);
      return;
    }

    // Decode the instruction
    auto decodeInfo =
        decoder->Decode(rip, instBuffer.data(), instBuffer.size(), instr);

    // CRITICAL FIX: Better error handling for decode failures
    if (decodeInfo.error != DecoderError::Success ||
        instr.instType == InstructionType::Unknown || instr.length == 0 ||
        instr.length > MAX_INSTRUCTION_LENGTH) {
      spdlog::warn("X86_64CPU[{}]: Decode failed at 0x{:x}: error={}, type={}, "
                   "length={}",
                   m_cpuId, rip, static_cast<int>(decodeInfo.error),
                   static_cast<int>(instr.instType), instr.length);
      TriggerInterrupt(EXC_UD, 0, false);
      return;
    }

    // Validate instruction before execution
    if (!instr.validate()) {
      spdlog::error("X86_64CPU[{}]: Invalid instruction at 0x{:x}", m_cpuId,
                    rip);
      TriggerInterrupt(EXC_UD, 0, false);
      return;
    }

    uint64_t nextRip = rip + instr.length;
    spdlog::trace("X86_64CPU[{}]: Interpreting at 0x{:x}: type={}, length={}",
                  m_cpuId, rip, static_cast<int>(instr.instType), instr.length);

    bool repActive = (instr.instType == InstructionType::Movsb ||
                      instr.instType == InstructionType::Movsd ||
                      instr.instType == InstructionType::Movsq ||
                      instr.instType == InstructionType::Stosb ||
                      instr.instType == InstructionType::Stosq ||
                      instr.instType == InstructionType::Cmpsb ||
                      instr.instType == InstructionType::Cmpsd ||
                      instr.instType == InstructionType::Cmpsq) &&
                     (instr.repPrefix || instr.repePrefix || instr.repnePrefix);
    uint64_t initialRCX = repActive ? GetRegister(Register::RCX) : 0;
    bool loopCondition = repActive && initialRCX > 0;

    if (repActive && initialRCX > 0) {
      std::unique_lock<std::recursive_timed_mutex> lock(mutex);
      SetRegister(Register::RCX, initialRCX - 1);
    }

    while (loopCondition) {
      switch (instr.instType) {
      case InstructionType::Nop:
        break;
      case InstructionType::Mov:
      case InstructionType::Movsb:
      case InstructionType::Movsd:
      case InstructionType::Movsq: {
        uint64_t rsi = GetRegister(Register::RSI);
        uint64_t rdi = GetRegister(Register::RDI);
        uint8_t size = (instr.instType == InstructionType::Movsb)   ? 1
                       : (instr.instType == InstructionType::Movsd) ? 4
                       : (instr.instType == InstructionType::Movsq)
                           ? 8
                           : instr.operands[0].size / 8;
        if (instr.instType == InstructionType::Mov) {
          uint64_t value = ReadOperandValue(instr.operands[1]);
          WriteOperandValue(instr.operands[0], value);
        } else {
          std::vector<uint8_t> buffer(size);
          mmu.ReadVirtual(rsi, buffer.data(), size, GetProcessId());
          mmu.WriteVirtual(rdi, buffer.data(), size, GetProcessId());
          int64_t delta = GetFlag(FLAG_DF) ? -size : size;
          std::unique_lock<std::recursive_timed_mutex> lock(mutex);
          SetRegister(Register::RSI, rsi + delta);
          SetRegister(Register::RDI, rdi + delta);
        }
        spdlog::trace("MOVS{}: src=0x{:x}, dst=0x{:x}, size={}",
                      size == 1   ? "B"
                      : size == 4 ? "D"
                                  : "Q",
                      rsi, rdi, size);
        break;
      }
      case InstructionType::Add: {
        uint64_t op1 = ReadOperandValue(instr.operands[0]);
        uint64_t op2 = ReadOperandValue(instr.operands[1]);
        uint64_t result = op1 + op2;
        WriteOperandValue(instr.operands[0], result);
        UpdateArithmeticFlags(op1, op2, result, instr.operands[0].size, false);
        spdlog::trace("ADD: op1=0x{:x}, op2=0x{:x}, result=0x{:x}", op1, op2,
                      result);
        break;
      }
      case InstructionType::Sub: {
        uint64_t op1 = ReadOperandValue(instr.operands[0]);
        uint64_t op2 = ReadOperandValue(instr.operands[1]);
        uint64_t result = op1 - op2;
        WriteOperandValue(instr.operands[0], result);
        UpdateArithmeticFlags(op1, op2, result, instr.operands[0].size, true);
        spdlog::trace("SUB: op1=0x{:x}, op2=0x{:x}, result=0x{:x}", op1, op2,
                      result);
        break;
      }
      case InstructionType::Push: {
        uint64_t value = ReadOperandValue(instr.operands[0]);
        Push(value, 8);
        spdlog::trace("PUSH: value=0x{:x}", value);
        break;
      }
      case InstructionType::Pop: {
        uint64_t value = Pop(8);
        WriteOperandValue(instr.operands[0], value);
        spdlog::trace("POP: value=0x{:x}", value);
        break;
      }
      case InstructionType::Call: {
        std::unique_lock<std::recursive_timed_mutex> lock(mutex);
        SetRegister(Register::RIP, nextRip);
        uint64_t target =
            instr.operands[0].type ==
                    DecodedInstruction::Operand::Type::IMMEDIATE
                ? nextRip +
                      static_cast<int64_t>(ReadOperandValue(instr.operands[0]))
                : ReadOperandValue(instr.operands[0]);
        lock.unlock();
        Push(nextRip, 8);
        nextRip = target;
        spdlog::trace("CALL: target=0x{:x}", target);
        break;
      }
      case InstructionType::Jump: {
        std::unique_lock<std::recursive_timed_mutex> lock(mutex);
        SetRegister(Register::RIP, nextRip);
        nextRip = instr.operands[0].type ==
                          DecodedInstruction::Operand::Type::IMMEDIATE
                      ? nextRip + static_cast<int64_t>(
                                      ReadOperandValue(instr.operands[0]))
                      : ReadOperandValue(instr.operands[0]);
        spdlog::trace("JMP: target=0x{:x}", nextRip);
        break;
      }
      case InstructionType::Ret: {
        std::unique_lock<std::recursive_timed_mutex> lock(mutex);
        SetRegister(Register::RIP, nextRip);
        lock.unlock();
        nextRip = Pop(8);
        if (instr.operandCount == 1) {
          std::unique_lock<std::recursive_timed_mutex> lock(mutex);
          SetRegister(Register::RSP,
                      GetRegister(Register::RSP) + instr.operands[0].immediate);
        }
        spdlog::trace("RET: target=0x{:x}", nextRip);
        break;
      }
      case InstructionType::Iret: {
        std::unique_lock<std::recursive_timed_mutex> lock(mutex);
        SetRegister(Register::RIP, nextRip);
        lock.unlock();
        uint64_t nr = Pop(8);
        uint64_t ncs = Pop(8);
        uint64_t nrf = Pop(8);
        uint64_t nrsp = Pop(8);
        uint64_t nss = Pop(8);
        {
          std::unique_lock<std::recursive_timed_mutex> lock(mutex);
          SetRflags(nrf);
          SetRegister(Register::RSP, nrsp);
          nextRip = nr;
          spdlog::debug("X86_64CPU[{}]: IRET to 0x{:x}", m_cpuId, nextRip);
        }
        break;
      }
      case InstructionType::Cmp: {
        // CRITICAL FIX: Correct CMP instruction implementation
        uint64_t op1 = ReadOperandValue(instr.operands[0]);
        uint64_t op2 = ReadOperandValue(instr.operands[1]);
        uint64_t result = op1 - op2;

        // CRITICAL FIX: Correct parameter order for UpdateArithmeticFlags
        // CMP performs subtraction: op1 - op2, so pass parameters in correct
        // order
        UpdateArithmeticFlags(op1, op2, result, instr.operands[0].size, true);

        spdlog::trace(
            "X86_64CPU[{}]: CMP: op1=0x{:x}, op2=0x{:x}, result=0x{:x}",
            m_cpuId, op1, op2, result);
        break;
      }
      case InstructionType::Test: {
        // CRITICAL FIX: Correct TEST instruction implementation
        uint64_t op1 = ReadOperandValue(instr.operands[0]);
        uint64_t op2 = ReadOperandValue(instr.operands[1]);
        uint64_t result = op1 & op2;

        // CRITICAL FIX: TEST instruction should use logical flags, not
        // arithmetic flags TEST clears CF and OF, and sets ZF, SF, PF based on
        // result
        UpdateLogicalFlags(result, instr.operands[0].size);

        spdlog::trace(
            "X86_64CPU[{}]: TEST: op1=0x{:x}, op2=0x{:x}, result=0x{:x}",
            m_cpuId, op1, op2, result);
        break;
      }
      case InstructionType::Lea: {
        uint64_t addr = CalculateMemoryAddress(instr.operands[1]);
        WriteOperandValue(instr.operands[0], addr);
        spdlog::trace("LEA: address=0x{:x}", addr);
        break;
      }
      case InstructionType::Jcc: {
        std::unique_lock<std::recursive_timed_mutex> lock(mutex);
        SetRegister(Register::RIP, nextRip);
        if (CheckCondition(instr.opcode & 0x0F)) {
          nextRip += instr.operands[0].immediate;
        }
        spdlog::trace("Jcc: condition=0x{:x}, target=0x{:x}",
                      instr.opcode & 0x0F, nextRip);
        break;
      }
      case InstructionType::Lidt: {
        std::unique_lock<std::recursive_timed_mutex> lock(mutex);
        SetRegister(Register::RIP, nextRip);
        if (instr.operandCount == 1 &&
            instr.operands[0].type ==
                DecodedInstruction::Operand::Type::MEMORY) {
          uint64_t memAddr = CalculateMemoryAddress(instr.operands[0]);
          lock.unlock();
          uint16_t limit = GetMemory().Read<uint16_t>(memAddr, GetProcessId());
          uint64_t base =
              GetMemory().Read<uint64_t>(memAddr + 2, GetProcessId());
          {
            std::unique_lock<std::recursive_timed_mutex> lock(mutex);
            SetIDTR(base, limit);
            spdlog::info("X86_64CPU[{}]: LIDT: base=0x{:x}, limit=0x{:x}",
                         m_cpuId, base, limit);
          }
        } else {
          lock.unlock();
          TriggerInterrupt(EXC_UD, 0, false);
          return;
        }
        break;
      }
      case InstructionType::Stosb:
      case InstructionType::Stosd:
      case InstructionType::Stosq: {
        uint64_t rdi = GetRegister(Register::RDI);
        uint64_t value = GetRegister(Register::RAX);
        uint8_t size = (instr.instType == InstructionType::Stosb)   ? 1
                       : (instr.instType == InstructionType::Stosd) ? 4
                                                                    : 8;
        value &= ((1ULL << (size * 8)) - 1);
        mmu.WriteVirtual(rdi, &value, size, GetProcessId());
        int64_t delta = GetFlag(FLAG_DF) ? -size : size;
        std::unique_lock<std::recursive_timed_mutex> lock(mutex);
        SetRegister(Register::RDI, rdi + delta);
        spdlog::trace("STOS{}: dst=0x{:x}, value=0x{:x}, size={}",
                      size == 1   ? "B"
                      : size == 4 ? "D"
                                  : "Q",
                      rdi, value, size);
        break;
      }
      case InstructionType::Cmpsb:
      case InstructionType::Cmpsd:
      case InstructionType::Cmpsq: {
        uint64_t rsi = GetRegister(Register::RSI);
        uint64_t rdi = GetRegister(Register::RDI);
        uint8_t size = (instr.instType == InstructionType::Cmpsb)   ? 1
                       : (instr.instType == InstructionType::Cmpsd) ? 4
                                                                    : 8;
        std::vector<uint8_t> src(size), dst(size);
        mmu.ReadVirtual(rsi, src.data(), size, GetProcessId());
        mmu.ReadVirtual(rdi, dst.data(), size, GetProcessId());
        uint64_t srcVal = 0, dstVal = 0;
        std::memcpy(&srcVal, src.data(), size);
        std::memcpy(&dstVal, dst.data(), size);
        uint64_t result = srcVal - dstVal;
        UpdateArithmeticFlags(result, srcVal, dstVal, true, size * 8);
        int64_t delta = GetFlag(FLAG_DF) ? -size : size;
        std::unique_lock<std::recursive_timed_mutex> lock(mutex);
        SetRegister(Register::RSI, rsi + delta);
        SetRegister(Register::RDI, rdi + delta);
        if (instr.repePrefix)
          loopCondition = GetFlag(FLAG_ZF);
        else if (instr.repnePrefix)
          loopCondition = !GetFlag(FLAG_ZF);
        spdlog::trace("CMPS{}: src=0x{:x}, dst=0x{:x}, size={}",
                      size == 1   ? "B"
                      : size == 4 ? "D"
                                  : "Q",
                      rsi, rdi, size);
        break;
      }
      case InstructionType::Addps: {
        // CRITICAL FIX: Comprehensive SIMD instruction handling
        if (instr.operandCount >= 2 &&
            instr.operands[0].type == DecodedInstruction::Operand::Type::XMM) {

          try {
            // Get destination register (will be modified)
            __m256i dest = ReadXmmOperandValue(instr.operands[0]);

            // Get source operand (can be XMM register or memory)
            __m256i src;
            if (instr.operands[1].type ==
                DecodedInstruction::Operand::Type::XMM) {
              src = ReadXmmOperandValue(instr.operands[1]);
            } else if (instr.operands[1].type ==
                       DecodedInstruction::Operand::Type::MEMORY) {
              // CRITICAL FIX: Proper memory operand handling for SIMD
              uint64_t addr = CalculateMemoryAddress(instr.operands[1]);
              alignas(32) uint8_t memData[32] = {0}; // Initialize to zero

              // Determine read size based on instruction operand size
              size_t readSize = (instr.operands[1].size == 256) ? 32 : 16;
              mmu.ReadVirtual(addr, memData, readSize, GetProcessId());

              // Load data with proper alignment handling
              if (addr % 16 == 0) {
                src = _mm256_load_si256((__m256i *)memData);
              } else {
                src = _mm256_loadu_si256((__m256i *)memData);
              }
            } else {
              spdlog::error(
                  "X86_64CPU[{}]: Invalid source operand type for ADDPS: {}",
                  m_cpuId, static_cast<int>(instr.operands[1].type));
              TriggerInterrupt(EXC_UD, 0, false);
              return;
            }

            // CRITICAL FIX: Handle different SIMD widths properly
            __m256 result;
            if (instr.operands[0].size == 128) {
              // 128-bit operation (XMM registers)
              __m128 dest128 =
                  _mm256_extractf128_ps(_mm256_castsi256_ps(dest), 0);
              __m128 src128 =
                  _mm256_extractf128_ps(_mm256_castsi256_ps(src), 0);
              __m128 result128 = _mm_add_ps(dest128, src128);

              // Zero upper 128 bits for XMM operation
              result = _mm256_insertf128_ps(_mm256_setzero_ps(), result128, 0);
            } else {
              // 256-bit operation (YMM registers)
              result = _mm256_add_ps(_mm256_castsi256_ps(dest),
                                     _mm256_castsi256_ps(src));
            }

            // Write result back to destination
            WriteXmmOperandValue(instr.operands[0],
                                 _mm256_castps_si256(result));

            spdlog::trace(
                "X86_64CPU[{}]: ADDPS: XMM{} = XMM{} + operand (size={})",
                m_cpuId, static_cast<int>(instr.operands[0].reg),
                static_cast<int>(instr.operands[0].reg),
                instr.operands[0].size);
          } catch (const std::exception &e) {
            spdlog::error("X86_64CPU[{}]: Exception in ADDPS execution: {}",
                          m_cpuId, e.what());
            TriggerInterrupt(EXC_GP, 0, false);
            return;
          }
        } else {
          spdlog::error("X86_64CPU[{}]: Invalid operand configuration for "
                        "ADDPS: count={}, type={}",
                        m_cpuId, instr.operandCount,
                        instr.operandCount > 0
                            ? static_cast<int>(instr.operands[0].type)
                            : -1);
          TriggerInterrupt(EXC_UD, 0, false);
          return;
        }
        break;
      }
      default: {
        spdlog::error("X86_64CPU[{}]: Unsupported instruction: {}", m_cpuId,
                      static_cast<int>(instr.instType));
        TriggerInterrupt(EXC_UD, 0, false);
        return;
      }
      }

      if (repActive && loopCondition) {
        uint64_t rcx = GetRegister(Register::RCX);
        loopCondition = rcx > 0;
        if (loopCondition) {
          std::unique_lock<std::recursive_timed_mutex> lock(mutex);
          SetRegister(Register::RCX, rcx - 1);
        }
      } else {
        loopCondition = false;
      }
    }

    if (GetRegister(Register::RIP) == rip) {
      std::unique_lock<std::recursive_timed_mutex> lock(mutex);
      SetRegister(Register::RIP, nextRip);
    }
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Exception at 0x{:x}: {}", m_cpuId, rip,
                  e.what());
    TriggerInterrupt(EXC_GP, 0, false);
    throw CPUException("Execution failure");
  }
}

uint64_t X86_64CPU::TranslateAddress(uint64_t virtualAddr) {
  size_t hitFlag, useCache;
  {
    std::unique_lock<std::recursive_timed_mutex> lock(mutex);
    hitFlag = 0;
    useCache = 0;
    uint64_t physAddr = tlb.Lookup(virtualAddr, hitFlag, useCache);
    if (physAddr != 0) {
      spdlog::trace("TLB hit for virtual 0x{:x}, physical 0x{:x}", virtualAddr,
                    physAddr);
      return physAddr;
    }
  }

  uint64_t cr3 = GetCR3();
  uint64_t pml4e = GetMemory().template Read<uint64_t>(
      cr3 + ((virtualAddr >> 39) & 0x1FF) * 8, GetProcessId());
  if (!(pml4e & 1)) {
    spdlog::error("Page table fault: PML4E at 0x{:x}",
                  cr3 + ((virtualAddr >> 39) & 0x1FF) * 8);
    throw CPUException("Page table fault: PML4E");
  }
  uint64_t pdpte = GetMemory().Read<uint64_t>(
      (pml4e & ~0xFFF) + ((virtualAddr >> 30) & 0x1FF) * 8, GetProcessId());
  if (!(pdpte & 1)) {
    spdlog::error("Page table fault: PDPTE at 0x{:x}",
                  (pml4e & ~0xFFF) + ((virtualAddr >> 30) & 0x1FF) * 8);
    throw CPUException("Page table fault: PDPTE");
  }
  uint64_t pde = GetMemory().Read<uint64_t>(
      (pdpte & ~0xFFF) + ((virtualAddr >> 21) & 0x1FF) * 8, GetProcessId());
  if (!(pde & 1)) {
    spdlog::error("Page table fault: PDE at 0x{:x}",
                  (pdpte & ~0xFFF) + ((virtualAddr >> 21) & 0x1FF) * 8);
    throw CPUException("Page table fault: PDE");
  }
  uint64_t pte = GetMemory().Read<uint64_t>(
      (pde & ~0xFFF) + ((virtualAddr >> 12) & 0x1FF) * 8, GetProcessId());
  if (!(pte & 1)) {
    spdlog::error("Page table fault: PTE at 0x{:x}",
                  (pde & ~0xFFF) + ((virtualAddr >> 12) & 0x1FF) * 8);
    throw CPUException("Page table fault: PTE");
  }
  uint64_t physAddr = (pte & ~0xFFF) | (virtualAddr & 0xFFF);
  {
    std::unique_lock<std::recursive_timed_mutex> lock(mutex);
    tlb.Insert(virtualAddr, physAddr);
  }
  spdlog::trace("Translated virtual 0x{:x} to physical 0x{:x}", virtualAddr,
                physAddr);

  return physAddr;
}

void X86_64CPU::Push(uint64_t value, uint8_t sizeInBytes) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  uint64_t rsp = _getRegister(Register::RSP) - sizeInBytes;
  _setRegister(Register::RSP, rsp);
  uint64_t processIdCopy =
      processId; // Copy to avoid calling GetProcessId() while holding lock
  lock.unlock();
  mmu.WriteVirtual(rsp, reinterpret_cast<const void *>(&value), sizeInBytes,
                   processIdCopy);
  spdlog::trace("Pushed value 0x{:x} to stack at 0x{:x}, size={}", value, rsp,
                sizeInBytes);
}

uint64_t X86_64CPU::Pop(uint8_t sizeInBytes) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  uint64_t rsp = _getRegister(Register::RSP);
  uint64_t processIdCopy =
      processId; // Copy to avoid calling GetProcessId() while holding lock
  lock.unlock();
  uint64_t value = 0;
  mmu.ReadVirtual(rsp, reinterpret_cast<void *>(&value), sizeInBytes,
                  processIdCopy);
  {
    std::unique_lock<std::recursive_timed_mutex> lock(mutex);
    _setRegister(Register::RSP, rsp + sizeInBytes);
  }
  spdlog::trace("Popped value 0x{:x} from stack at 0x{:x}, size={}", value, rsp,
                sizeInBytes);
  return value;
}

void X86_64CPU::UpdateFlags(uint64_t result, uint64_t op1, uint64_t op2,
                            uint8_t sizeInBits, bool isSubtract) {
  UpdateArithmeticFlags(op1, op2, result, sizeInBits, isSubtract);
}

bool X86_64CPU::CheckCondition(uint8_t conditionCode) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  switch (conditionCode) {
  case 0x0:
    return GetFlag(FLAG_OF);
  case 0x1:
    return !GetFlag(FLAG_OF);
  case 0x2:
    return GetFlag(FLAG_CF);
  case 0x3:
    return !GetFlag(FLAG_CF);
  case 0x4:
    return GetFlag(FLAG_ZF);
  case 0x5:
    return !GetFlag(FLAG_ZF);
  case 0x6:
    return GetFlag(FLAG_CF) || GetFlag(FLAG_ZF);
  case 0x7:
    return !GetFlag(FLAG_CF) && !GetFlag(FLAG_ZF);
  case 0x8:
    return GetFlag(FLAG_SF);
  case 0x9:
    return !GetFlag(FLAG_SF);
  case 0xA:
    return GetFlag(FLAG_PF);
  case 0xB:
    return !GetFlag(FLAG_PF);
  case 0xC:
    return GetFlag(FLAG_SF) != GetFlag(FLAG_OF);
  case 0xD:
    return GetFlag(FLAG_SF) == GetFlag(FLAG_OF);
  case 0xE:
    return GetFlag(FLAG_ZF) || (GetFlag(FLAG_SF) != GetFlag(FLAG_OF));
  case 0xF:
    return !GetFlag(FLAG_ZF) && (GetFlag(FLAG_SF) == GetFlag(FLAG_OF));
  default:
    spdlog::warn("X86_64CPU[{}]: Invalid condition code 0x{:x}", m_cpuId,
                 conditionCode);
    return false;
  }
}

void X86_64CPU::SetIDTR(uint64_t base, uint16_t limit) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  idtrBase = base;
  idtrLimit = limit;
  spdlog::info("X86_64CPU[{}]: Set IDTR: base=0x{:x}, limit=0x{:x}", m_cpuId,
               base, limit);
}

void X86_64CPU::SaveState(std::ostream &out) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  out.write(reinterpret_cast<const char *>(registers.data()),
            registers.size() * sizeof(uint64_t));
  out.write(reinterpret_cast<const char *>(xmmRegisters.data()),
            xmmRegisters.size() * sizeof(__m256i));
  out.write(reinterpret_cast<const char *>(&rflags), sizeof(rflags));
  spdlog::info("X86_64CPU[{}]: Saved state", m_cpuId);
}

void X86_64CPU::LoadState(std::istream &in) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  in.read(reinterpret_cast<char *>(registers.data()),
          registers.size() * sizeof(uint64_t));
  in.read(reinterpret_cast<char *>(xmmRegisters.data()),
          xmmRegisters.size() * sizeof(__m256i));
  in.read(reinterpret_cast<char *>(&rflags), sizeof(rflags));
  spdlog::info("X86_64CPU[{}]: Loaded state", m_cpuId);
}

uint64_t X86_64CPU::GetRegister(Register r) const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return _getRegister(r);
}

void X86_64CPU::SetRegister(Register r, uint64_t v) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  _setRegister(r, v);
}

ps4::PS4MMU &X86_64CPU::GetMemory() { return m_emulator.GetMemory(); }

uint32_t X86_64CPU::GetCPUId() const { return m_cpuId; }

void X86_64CPU::InvalidateTLB(uint64_t virtAddr) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  tlb.Invalidate(virtAddr);
}

uint64_t X86_64CPU::_getRegister(Register r) const {
  return registers[static_cast<size_t>(r)];
}

void X86_64CPU::_setRegister(Register r, uint64_t v) {
  registers[static_cast<size_t>(r)] = v;
}

void X86_64CPU::SetRflags(uint64_t f) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  rflags = f;
  spdlog::trace("X86_64CPU[{}] RFLAGS set: 0x{:x}", m_cpuId, f);
}

uint64_t X86_64CPU::GetRflags() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return rflags;
}

bool X86_64CPU::GetFlag(uint64_t m) const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return (rflags & m) != 0;
}

void X86_64CPU::SetFlag(uint64_t flag, bool value) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  if (value) {
    rflags |= flag;
  } else {
    rflags &= ~flag;
  }
}

void X86_64CPU::UpdateLogicalFlags(uint64_t result, uint8_t sizeInBits) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);

  // CRITICAL FIX: Validate size parameter
  if (sizeInBits == 0 || sizeInBits > 64) {
    spdlog::error(
        "X86_64CPU[{}]: Invalid size for logical flag calculation: {} bits",
        m_cpuId, sizeInBits);
    return;
  }

  uint64_t mask =
      (sizeInBits == 64) ? 0xFFFFFFFFFFFFFFFFULL : (1ULL << sizeInBits) - 1;
  uint64_t signBit = 1ULL << (sizeInBits - 1);

  result &= mask;

  // CRITICAL FIX: Clear logical flags AND carry/overflow flags for TEST
  // instruction TEST instruction clears CF and OF, sets ZF, SF, PF based on
  // result, AF is undefined
  rflags &= ~(FLAG_ZF | FLAG_SF | FLAG_PF | FLAG_CF | FLAG_OF);

  // Set flags based on result
  if (result == 0)
    rflags |= FLAG_ZF;
  if (result & signBit)
    rflags |= FLAG_SF;
  if (CalculateParity(result))
    rflags |= FLAG_PF;

  // CF and OF are explicitly cleared (already done above)
  // AF is undefined for logical operations, so we leave it unchanged

  spdlog::trace("X86_64CPU[{}]: Logical flags updated: RFLAGS=0x{:x}", m_cpuId,
                rflags);
}

uint64_t X86_64CPU::GetCR3() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return cr3;
}

uint64_t X86_64CPU::GetCR2() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return cr2;
}

uint16_t X86_64CPU::GetCS() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return cs;
}

uint16_t X86_64CPU::GetSS() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return ss;
}

uint64_t X86_64CPU::GetTSSBase() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return tssBase;
}

uint16_t X86_64CPU::GetKernelSS() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return kernelSS;
}

X86_64JITCompiler &X86_64CPU::GetJITCompiler() {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return *jit;
}

Pipeline &X86_64CPU::GetPipeline() {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return *pipeline;
}

uint64_t X86_64CPU::GetProcessId() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return processId;
}

void X86_64CPU::Execute() { FetchDecodeExecute(); }

void X86_64CPU::SetContext(const CPUContext &ctx) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  registers = ctx.registers;
  rflags = ctx.rflags;
  xmmRegisters = ctx.xmmRegisters;
  _setRegister(Register::RIP, ctx.rip);
  _setRegister(Register::RSP, ctx.rsp);
  spdlog::info("X86_64CPU[{}]: Context set: RIP=0x{:x}, RSP=0x{:x}", m_cpuId,
               ctx.rip, ctx.rsp);
}

std::unordered_map<std::string, uint64_t> X86_64CPU::GetDiagnostics() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  std::unordered_map<std::string, uint64_t> diagnostics;
  diagnostics["cycles"] = pipeline->GetStats().cycles;
  diagnostics["instructionsExecuted"] =
      pipeline->GetStats().instructionsExecuted;
  diagnostics["stalls"] = pipeline->GetStats().stalls;
  diagnostics["data_hazard_stalls"] = pipeline->GetStats().data_hazard_stalls;
  diagnostics["memory_stalls"] = pipeline->GetStats().memory_stalls;
  diagnostics["branch_hits"] = pipeline->GetStats().branch_hits;
  diagnostics["branch_mispredictions"] =
      pipeline->GetStats().branch_mispredictions;
  diagnostics["interruptQueueSize"] = interruptQueue.size();
  spdlog::trace("X86_64CPU[{}]: Diagnostics retrieved", m_cpuId);
  return diagnostics;
}

bool X86_64CPU::SwitchToFiber(uint64_t fiberId) {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  try {
    // Save current CPU state
    CPUContext currentContext;
    currentContext.registers = registers;
    currentContext.rflags = rflags;
    currentContext.xmmRegisters = xmmRegisters;
    currentContext.rip = GetRegister(Register::RIP);
    currentContext.rsp = GetRegister(Register::RSP);

    // Get fiber manager from emulator
    auto &fiberManager = m_emulator.GetFiberManager();

    // Switch to the specified fiber
    bool success = fiberManager.SwitchToFiber(fiberId);
    if (!success) {
      spdlog::error("X86_64CPU[{}]: Failed to switch to fiber {}", m_cpuId,
                    fiberId);
      return false;
    }

    // The fiber manager should have updated the CPU context
    // In a full implementation, we would restore the fiber's saved context here
    spdlog::info("X86_64CPU[{}]: Successfully switched to fiber {}", m_cpuId,
                 fiberId);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("X86_64CPU[{}]: Exception during fiber switch to {}: {}",
                  m_cpuId, fiberId, e.what());
    return false;
  }
}

bool X86_64CPU::IsRunning() const {
  std::unique_lock<std::recursive_timed_mutex> lock(mutex);
  return running;
}
} // namespace x86_64
