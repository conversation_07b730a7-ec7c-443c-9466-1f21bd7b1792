<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_SETSCHEDPARAM(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_setschedparam, pthread_getschedparam - control thread
scheduling 
</P>
<P>parameters 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> 
</P>
<P><B>int pthread_setschedparam(pthread_t </B><I>target_thread</I><B>,
int </B><I>policy</I><B>, const struct sched_param *</B><I>param</I><B>);</B>
</P>
<P><B>int pthread_getschedparam(pthread_t </B><I>target_thread</I><B>,
int *</B><I>policy</I><B>, struct sched_param *</B><I>param</I><B>);</B>
</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P><B>pthread_setschedparam</B> sets the scheduling parameters for
the thread <I>target_thread</I> as indicated by <I>policy</I> and
<I>param</I>. <I>policy</I> can be either <B>SCHED_OTHER</B>
(regular, non-real-time scheduling), <B>SCHED_RR</B> (real-time,
round-robin) or <B>SCHED_FIFO</B> (real-time, first-in first-out).
<I>param</I> specifies the scheduling priority for the two real-time
policies.</P>
<P><B>PThreads4W</B> only supports SCHED_OTHER and does not support
the real-time scheduling policies <B>SCHED_RR</B> and <B>SCHED_FIFO.</B>
</P>
<P><B>pthread_getschedparam</B> retrieves the scheduling policy and
scheduling parameters for the thread <I>target_thread</I> and stores
them in the locations pointed to by <I>policy</I> and <I>param</I>,
respectively. 
</P>
<H2><A HREF="#toc3" NAME="sect3">Return Value</A></H2>
<P><B>pthread_setschedparam</B> and <B>pthread_getschedparam</B>
return 0 on success and a non-zero error code on error. 
</P>
<H2><A HREF="#toc4" NAME="sect4">Errors</A></H2>
<P>On error, <B>pthread_setschedparam</B> returns the following error
codes: 
</P>
<DL>
	<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>ENOTSUP</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		<I>policy</I> is not <B>SCHED_OTHER.</B></DD><DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		<B>EINVAL</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		One of the arguments is invalid, or the priority value specified by
		<I>param</I> is not valid for the specified policy.</DD><DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		<B>ESRCH</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		The <I>target_thread</I> is invalid or has already terminated 
		</DD></DL>
</DL>
<P>
On error, <B>pthread_getschedparam</B> returns the following error
codes: 
</P>
<DL>
	<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>ESRCH</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		the <I>target_thread</I> is invalid or has already terminated 
		</DD></DL>
</DL>
<H2>
<A HREF="#toc5" NAME="sect5">Author</A></H2>
<P>Xavier Leroy &lt;<EMAIL>&gt; 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<H2><A HREF="#toc6" NAME="sect6">See Also</A></H2>
<P><A HREF="sched_setscheduler.html"><B>sched_setscheduler</B>(2)</A>
, <A HREF="sched_getscheduler.html"><B>sched_getscheduler</B>(2)</A>
, <A HREF="sched_getparam.html"><B>sched_getparam</B>(2)</A> ,
<A HREF="pthread_attr_init.html"><B>pthread_attr_setschedpolicy</B>(3)</A>
, <A HREF="pthread_attr_init.html"><B>pthread_attr_setschedparam</B>(3)</A>
. 
</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Author</A>
		</P>
	<LI><P><A HREF="#sect6" NAME="toc6">See Also</A> 
	</P>
</UL>
</BODY>
</HTML>