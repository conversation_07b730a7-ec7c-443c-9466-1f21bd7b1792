// Copyright 2025 <Copyright Owner>

#pragma once

#include "gcn_types.h" // GCNShaderType, GCNInstruction
#include <array>
#include <cstdint>
#include <functional>
#include <istream>
#include <memory>
#include <ostream>
#include <shared_mutex>
#include <stdexcept>
#include <string>
#include <unordered_map>
#include <vector>

namespace ps4 {

class GNMRegisterState;
class TileManager;
class GNMShaderTranslator;
class CommandProcessor;
class PS4GPU;

// Forward declaration for callback function types
using ShaderExecutionCallback = std::function<void(GCNShaderType, uint64_t)>;

/**
 * @brief Exception thrown by ShaderEmulator for invalid operations.
 */
struct ShaderEmulatorException : std::runtime_error {
  explicit ShaderEmulatorException(const std::string &msg)
      : std::runtime_error(msg) {}
};

/**
 * @brief Statistics for SIMD lane operations.
 */
struct SIMDLaneStats {
  uint64_t instructionCount = 0; ///< Total instructions executed
  uint64_t totalLatencyUs = 0;   ///< Total latency in microseconds
  uint64_t cacheHits = 0;        ///< Cache hits for instruction execution
  uint64_t cacheMisses = 0;      ///< Cache misses for instruction execution
  uint64_t errorCount = 0;       ///< Total errors encountered
};

/**
 * @brief SIMD lane (16 threads) for shader execution.
 * @details Manages scalar and vector registers for 16 threads, with thread-safe
 *          execution of GCN instructions. Tracks metrics and supports caching.
 */
class SIMDLane {
public:
  /**
   * @brief Constructs a SIMD lane.
   * @details Initializes registers and metrics. Thread-safe.
   */
  SIMDLane();

  /**
   * @brief Destructor, cleaning up resources.
   * @details Thread-safe.
   */
  ~SIMDLane();

  /**
   * @brief Initializes the SIMD lane.
   * @return True on success, false on failure.
   * @throws ShaderEmulatorException on initialization errors.
   * @details Resets registers and metrics. Thread-safe.
   */
  bool Initialize();

  /**
   * @brief Shuts down the SIMD lane.
   * @details Clears resources and metrics. Thread-safe.
   */
  void Shutdown();

  /**
   * @brief Executes a GCN instruction.
   * @param instr Instruction to execute.
   * @throws ShaderEmulatorException on execution errors.
   * @details Thread-safe. Updates metrics and cache.
   */
  void ExecuteInstruction(const GCNInstruction &instr);

  /**
   * @brief Gets a scalar register value.
   * @param index Register index (0 to NUM_SCALAR_REGS-1).
   * @return Register value.
   * @throws ShaderEmulatorException on invalid index.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  uint32_t GetScalarRegister(uint32_t index) const;

  /**
   * @brief Sets a scalar register value.
   * @param index Register index (0 to NUM_SCALAR_REGS-1).
   * @param value Value to set.
   * @throws ShaderEmulatorException on invalid index.
   * @details Thread-safe. Updates cache and metrics.
   */
  void SetScalarRegister(uint32_t index, uint32_t value);

  /**
   * @brief Gets a vector register value for a thread.
   * @param thread Thread index (0 to NUM_THREADS-1).
   * @param index Register index (0 to NUM_VECTOR_REGS-1).
   * @return Register value.
   * @throws ShaderEmulatorException on invalid thread or index.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  uint32_t GetVectorRegister(uint32_t thread, uint32_t index) const;

  /**
   * @brief Sets a vector register value for a thread.
   * @param thread Thread index (0 to NUM_THREADS-1).
   * @param index Register index (0 to NUM_VECTOR_REGS-1).
   * @param value Value to set.
   * @throws ShaderEmulatorException on invalid thread or index.
   * @details Thread-safe. Updates cache and metrics.
   */
  void SetVectorRegister(uint32_t thread, uint32_t index, uint32_t value);

  /**
   * @brief Gets the execution mask.
   * @return Current execution mask.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  uint16_t GetExecutionMask() const;

  /**
   * @brief Sets the execution mask.
   * @param mask New execution mask.
   * @details Thread-safe. Updates metrics.
   */
  void SetExecutionMask(uint16_t mask);

  /**
   * @brief Retrieves SIMD lane statistics.
   * @return Current statistics.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  SIMDLaneStats GetStats() const;

private:
  static constexpr uint32_t NUM_THREADS = 16; ///< Number of threads per lane
  static constexpr uint32_t NUM_SCALAR_REGS =
      128; ///< Number of scalar registers
  static constexpr uint32_t NUM_VECTOR_REGS =
      256; ///< Number of vector registers per thread

  std::array<uint32_t, NUM_SCALAR_REGS> m_scalarRegisters; ///< Scalar registers
  std::array<std::array<uint32_t, NUM_VECTOR_REGS>, NUM_THREADS>
      m_vectorRegisters; ///< Vector registers
  uint16_t m_executionMask =
      0xFFFF; ///< Execution mask (16 bits for 16 threads)
  mutable std::shared_mutex m_laneMutex; ///< Mutex for thread safety
  mutable SIMDLaneStats m_stats;         ///< Lane statistics

  /**
   * @brief Executes a scalar instruction.
   * @param instr Instruction to execute.
   * @details Thread-safe. Updates metrics.
   */
  void ExecuteScalarInstruction(const GCNInstruction &instr);

  /**
   * @brief Executes a vector instruction.
   * @param instr Instruction to execute.
   * @details Thread-safe. Updates metrics.
   */
  void ExecuteVectorInstruction(const GCNInstruction &instr);

  /**
   * @brief Executes a memory instruction.
   * @param instr Instruction to execute.
   * @details Thread-safe. Updates metrics.
   */
  void ExecuteMemoryInstruction(const GCNInstruction &instr);

  /**
   * @brief Executes a control instruction.
   * @param instr Instruction to execute.
   * @details Thread-safe. Updates metrics.
   */
  void ExecuteControlInstruction(const GCNInstruction &instr);
};

/**
 * @brief Statistics for compute unit operations.
 */
struct ComputeUnitStats {
  uint64_t instructionCount = 0; ///< Total instructions executed
  uint64_t totalLatencyUs = 0;   ///< Total latency in microseconds
  uint64_t cacheHits = 0;        ///< Cache hits for instruction execution
  uint64_t cacheMisses = 0;      ///< Cache misses for instruction execution
  uint64_t errorCount = 0;       ///< Total errors encountered
};

/**
 * @brief Compute Unit (4 SIMD lanes) for shader execution.
 * @details Manages 4 SIMD lanes for parallel shader execution, with thread-safe
 *          instruction dispatching. Tracks metrics and supports caching.
 */
class ComputeUnit {
public:
  /**
   * @brief Constructs a compute unit.
   * @details Initializes SIMD lanes and metrics. Thread-safe.
   */
  ComputeUnit();

  /**
   * @brief Destructor, cleaning up resources.
   * @details Thread-safe.
   */
  ~ComputeUnit();

  /**
   * @brief Initializes the compute unit.
   * @return True on success, false on failure.
   * @throws ShaderEmulatorException on initialization errors.
   * @details Initializes all SIMD lanes and resets metrics. Thread-safe.
   */
  bool Initialize();

  /**
   * @brief Shuts down the compute unit.
   * @details Shuts down all SIMD lanes and clears metrics. Thread-safe.
   */
  void Shutdown();

  /**
   * @brief Executes a GCN instruction across all SIMD lanes.
   * @param instr Instruction to execute.
   * @throws ShaderEmulatorException on execution errors.
   * @details Thread-safe. Updates metrics and cache.
   */
  void ExecuteInstruction(const GCNInstruction &instr);

  /**
   * @brief Gets a SIMD lane by index.
   * @param index Lane index (0 to NUM_SIMD_LANES-1).
   * @return Reference to the SIMD lane.
   * @throws ShaderEmulatorException on invalid index.
   * @details Thread-safe.
   */
  SIMDLane &GetSIMDLane(uint32_t index);

  /**
   * @brief Retrieves compute unit statistics.
   * @return Current statistics.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  ComputeUnitStats GetStats() const;

private:
  static constexpr uint32_t NUM_SIMD_LANES = 4; ///< Number of SIMD lanes

  std::array<SIMDLane, NUM_SIMD_LANES> m_simdLanes; ///< SIMD lanes
  mutable std::shared_mutex m_unitMutex;            ///< Mutex for thread safety
  ComputeUnitStats m_stats;                         ///< Unit statistics
};

/**
 * @brief Shader program structure with metrics.
 */
struct ShaderProgram {
  std::vector<GCNInstruction> instructions; ///< Shader instructions
  uint32_t entryPoint = 0;                  ///< Entry point address
  uint32_t numRegisters = 0;                ///< Number of registers used
  uint32_t numThreads = 0;                  ///< Number of threads
  uint64_t cacheHits = 0;   ///< Cache hits for program execution
  uint64_t cacheMisses = 0; ///< Cache misses for program execution

  /**
   * @brief Serializes the shader program to a stream.
   * @param out Output stream.
   * @details Writes instructions, entry point, registers, threads, and metrics.
   */
  void Serialize(std::ostream &out) const;

  /**
   * @brief Deserializes the shader program from a stream.
   * @param in Input stream.
   * @throws ShaderEmulatorException on deserialization errors.
   */
  void Deserialize(std::istream &in);
};

/**
 * @brief Shader execution context with metrics.
 */
struct ShaderContext {
  uint32_t programCounter = 0; ///< Current program counter
  uint32_t stackPointer = 0;   ///< Current stack pointer
  std::vector<uint32_t> stack; ///< Execution stack
  bool running = false;        ///< Is the context running?
  uint64_t cacheHits = 0;      ///< Cache hits for context execution
  uint64_t cacheMisses = 0;    ///< Cache misses for context execution

  /**
   * @brief Serializes the shader context to a stream.
   * @param out Output stream.
   * @details Writes PC, stack, running state, and metrics.
   */
  void Serialize(std::ostream &out) const;

  /**
   * @brief Deserializes the shader context from a stream.
   * @param in Input stream.
   * @throws ShaderEmulatorException on deserialization errors.
   */
  void Deserialize(std::istream &in);
};

/**
 * @brief Cache entry for executed instructions.
 */
struct InstructionCacheEntry {
  GCNInstruction instruction;
  std::vector<uint32_t> results;
  mutable uint64_t cacheHits = 0;
  mutable uint64_t cacheMisses = 0;
};

/**
 * @brief Statistics for shader emulator operations.
 */
struct ShaderEmulatorStats {
  uint64_t instructionCount = 0; ///< Total instructions executed
  uint64_t totalLatencyUs = 0;   ///< Total latency in microseconds
  uint64_t cacheHits = 0;        ///< Cache hits for instruction execution
  uint64_t cacheMisses = 0;      ///< Cache misses for instruction execution
  uint64_t errorCount = 0;       ///< Total errors encountered
};

/**
 * @brief Emulates GCN shader execution.
 * @details Manages shader loading and execution using compute units, with
 *          thread-safe operations. Integrates with GNMRegisterState for
 *          register data, TileManager for tiled surfaces, GNMShaderTranslator
 * for bytecode translation, and CommandProcessor for PM4 packet processing.
 *          Supports instruction caching, serialization with versioning, and
 * multi-core diagnostics. Handles a variety of GCN instructions including
 * scalar operations (e.g., SMovB32, SAddU32, SSubU32, SMulU32), vector
 * operations (e.g., VMovB32, VAddF32, VSubF32, VMadF32), comparison operations
 * (e.g., VCmpEqF32, VCmpGtF32), memory operations (e.g., BufferLoadDword,
 * BufferStoreDword), and control operations (e.g., SBranch).
 */
class ShaderEmulator {
public:
  ShaderEmulator(GNMRegisterState &gnmState, TileManager &tileManager,
                 GNMShaderTranslator &shaderTranslator,
                 CommandProcessor &commandProcessor);

  /**
   * @brief Destructor, cleaning up resources.
   * @details Thread-safe.
   */
  ~ShaderEmulator();

  /**
   * @brief Loads a shader from bytecode.
   * @param bytecode GCN shader bytecode.
   * @param type Shader type.
   * @return True on success, false on failure.
   * @throws ShaderEmulatorException on loading errors.
   * @details Thread-safe. Updates metrics and cache.
   */
  bool LoadShader(const std::vector<uint32_t> &bytecode, GCNShaderType type);

  /**
   * @brief Executes the loaded shader.
   * @return True on success, false on failure.
   * @throws ShaderEmulatorException on execution errors.
   * @details Thread-safe. Updates metrics and cache.
   */
  bool Execute();

  /**
   * @brief Retrieves a cached instruction result.
   * @param instructionHash Hash of the instruction.
   * @param results Output execution results (if cached).
   * @return True if cached, false otherwise.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  bool GetCachedInstructionResult(uint64_t instructionHash,
                                  std::vector<uint32_t> &results) const;

  /**
   * @brief Clears the instruction cache.
   * @details Thread-safe. Resets cache metrics.
   */
  void ClearInstructionCache();

  /**
   * @brief Retrieves shader emulator statistics.
   * @return Current statistics.
   * @details Thread-safe (read-only). Updates cache metrics.
   */
  ShaderEmulatorStats GetStats() const;

  /**
   * @brief Saves the emulator state to a stream.
   * @param out Output stream.
   * @details Thread-safe (read-only). Serializes with versioning (version 1).
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the emulator state from a stream.
   * @param in Input stream.
   * @details Thread-safe. Expects version 1 serialization format.
   * @throws ShaderEmulatorException on invalid state data.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Sets the shader execution callback.
   * @param callback Callback function to notify PS4GPU of shader execution.
   * @details Thread-safe. Used for notifying PS4GPU when shaders complete
   * execution.
   */
  void SetShaderExecutionCallback(const ShaderExecutionCallback &callback);

private:
  class Impl;                  ///< Implementation details (PIMPL idiom)
  std::unique_ptr<Impl> pImpl; ///< Pointer to implementation
}; // class ShaderEmulator

} // namespace ps4

// End of file – no unterminated comments or stray tokens