<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_KILL(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_sigmask, pthread_kill, sigwait - handling of signals in
threads 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> <BR><B>#include &lt;signal.h&gt;</B>
</P>
<P><B>int pthread_sigmask(int </B><I>how</I><B>, const sigset_t
*</B><I>newmask</I><B>, sigset_t *</B><I>oldmask</I><B>);</B> 
</P>
<P><B>int pthread_kill(pthread_t </B><I>thread</I><B>, int </B><I>signo</I><B>);</B>
</P>
<P><B>int sigwait(const sigset_t *</B>set, <B>int</B> *sig);</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P><B>pthread_sigmask</B> changes the signal mask for the calling
thread as described by the <I>how</I> and <I>newmask</I> arguments.
If <I>oldmask</I> is not <B>NULL</B>, the previous signal mask is
stored in the location pointed to by <I>oldmask</I>. <B>PThreads4W</B>
implements this function but no other function uses the signal mask
yet.</P>
<P>The meaning of the <I>how</I> and <I>newmask</I> arguments is the
same as for <B><SPAN STYLE="font-style: normal">sigprocmask</SPAN></B>(2).
If <I>how</I> is <B>SIG_SETMASK</B>, the signal mask is set to
<I>newmask</I>. If <I>how</I> is <B>SIG_BLOCK</B>, the signals
specified to <I>newmask</I> are added to the current signal mask. If
<I>how</I> is <B>SIG_UNBLOCK</B>, the signals specified to <I>newmask</I>
are removed from the current signal mask. 
</P>
<P>Recall that signal masks are set on a per-thread basis, but signal
actions and signal handlers, as set with <B>sigaction</B>(2), are
shared between all threads. 
</P>
<P><B>pthread_kill</B> send signal number <I>signo</I> to the thread
<I>thread</I>. <B>PThreads4W</B> only supports signal number 0,
which does not send any signal but causes <B>pthread_kill</B> to
return an error if <I>thread</I> is not valid.</P>
<P><B>sigwait</B> suspends the calling thread until one of the
signals in <I>set</I> is delivered to the calling thread. It then
stores the number of the signal received in the location pointed to
by <I>sig</I> and returns. The signals in <I>set</I> must be blocked
and not ignored on entrance to <B>sigwait</B>. If the delivered
signal has a signal handler function attached, that function is <I>not</I>
called. <B>PThreads4W</B> implements this function as a
cancellation point only - it does not wait for any signals and does
not change the location pointed to by <I>sig</I>.</P>
<H2><A HREF="#toc3" NAME="sect3">Cancellation</A></H2>
<P><B>sigwait</B> is a cancellation point. 
</P>
<H2><A HREF="#toc4" NAME="sect4">Return Value</A></H2>
<P>On success, 0 is returned. On failure, a non-zero error code is
returned. 
</P>
<H2><A HREF="#toc5" NAME="sect5">Errors</A></H2>
<P>The <B>pthread_sigmask</B> function returns the following error
codes on error: 
</P>
<DL>
	<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		<I>how</I> is not one of <B>SIG_SETMASK</B>, <B>SIG_BLOCK</B>, or
		<B>SIG_UNBLOCK</B> 
		</DD></DL>
</DL>
<P>
The <B>pthread_kill</B> function returns the following error codes on
error: 
</P>
<DL>
	<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		<I>signo</I> is not a valid signal number or is unsupported.</DD><DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		<B>ESRCH</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		the thread <I>thread</I> does not exist (e.g. it has already
		terminated) 
		</DD></DL>
</DL>
<P>
The <B>sigwait</B> function never returns an error. 
</P>
<H2><A HREF="#toc6" NAME="sect6">Author</A></H2>
<P>Xavier Leroy &lt;<EMAIL>&gt; 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<H2><A HREF="#toc7" NAME="sect7">See Also</A></H2>
<P> 
</P>
<H2><A HREF="#toc8" NAME="sect8">Notes</A></H2>
<P>In any implementation, for <B>sigwait</B> to work reliably, the
signals being waited for must be blocked in all threads, not only in
the calling thread, since otherwise the POSIX semantics for signal
delivery do not guarantee that it’s the thread doing the <B>sigwait</B>
that will receive the signal. The best way to achieve this is to
block those signals before any threads are created, and never unblock
them in the program other than by calling <B>sigwait</B>. This works
because all threads inherit their initial sigmask from their creating
thread.</P>
<H2><A HREF="#toc9" NAME="sect9">Bugs</A></H2>
<P><B>PThreads4W</B> does not implement signals yet and so these
routines have almost no use except to prevent the compiler or linker
from complaining. <B>pthread_kill</B> is useful in determining if the
thread is a valid thread, but since many threads implementations
reuse thread IDs, the valid thread may no longer be the thread you
think it is, and so this method of determining thread validity is not
portable, and very risky. <B>PThreads4W</B> from version 1.0.0
onwards implements pseudo-unique thread IDs, so applications that use
this technique (but really shouldn't) have some protection.</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Cancellation</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect6" NAME="toc6">Author</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect7" NAME="toc7">See
	Also</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect8" NAME="toc8">Notes</A>
		</P>
	<LI><P><A HREF="#sect9" NAME="toc9">Bugs</A> 
	</P>
</UL>
</BODY>
</HTML>
