// Copyright 2025 <Copyright Owner>

#pragma once

#include "../memory/ps4_mmu.h"
#include <SDL.h>
#include <SDL_gamecontroller.h>
#include <array>
#include <atomic>
#include <chrono>
#include <condition_variable>
#include <cstdint>
#include <istream>
#include <mutex>
#include <ostream>
#include <shared_mutex>
#include <thread>
#include <vector>

namespace ps4 {

/**
 * @brief Exception for controller-related errors.
 */
struct ControllerException : std::runtime_error {
  explicit ControllerException(const std::string &msg)
      : std::runtime_error(msg) {}
};

/**
 * @brief Enum for PS4 controller buttons.
 */
enum class ControllerButton : uint32_t {
  CROSS = 1 << 0,      ///< Cross button
  CIRCLE = 1 << 1,     ///< Circle button
  SQUARE = 1 << 2,     ///< Square button
  TRIANGLE = 1 << 3,   ///< Triangle button
  L1 = 1 << 4,         ///< Left shoulder button
  R1 = 1 << 5,         ///< Right shoulder button
  SHARE = 1 << 6,      ///< Share button
  OPTIONS = 1 << 7,    ///< Options button
  L3 = 1 << 8,         ///< Left stick button
  R3 = 1 << 9,         ///< Right stick button
  PS = 1 << 10,        ///< PS button
  DPAD_UP = 1 << 11,   ///< D-pad up
  DPAD_DOWN = 1 << 12, ///< D-pad down
  DPAD_LEFT = 1 << 13, ///< D-pad left
  DPAD_RIGHT = 1 << 14 ///< D-pad right
};

/**
 * @brief Enhanced touchpad data structure.
 */
struct TouchpadData {
  bool active = false;    ///< Touch active
  uint16_t x = 0;         ///< X coordinate (0-1920)
  uint16_t y = 0;         ///< Y coordinate (0-942)
  uint8_t pressure = 0;   ///< Touch pressure (0-255)
  uint32_t timestamp = 0; ///< Touch timestamp
  uint8_t touchId = 0;    ///< Touch ID for multi-touch
};

/**
 * @brief Enhanced motion sensor data.
 */
struct MotionSensorData {
  float gyroX = 0.0f;      ///< Gyroscope X (rad/s)
  float gyroY = 0.0f;      ///< Gyroscope Y (rad/s)
  float gyroZ = 0.0f;      ///< Gyroscope Z (rad/s)
  float accelX = 0.0f;     ///< Accelerometer X (g)
  float accelY = 0.0f;     ///< Accelerometer Y (g)
  float accelZ = 0.0f;     ///< Accelerometer Z (g)
  float magnetX = 0.0f;    ///< Magnetometer X (μT)
  float magnetY = 0.0f;    ///< Magnetometer Y (μT)
  float magnetZ = 0.0f;    ///< Magnetometer Z (μT)
  uint32_t timestamp = 0;  ///< Sensor timestamp
  bool calibrated = false; ///< Calibration status

  // Calibration data
  float calibrationOffsetX = 0.0f; ///< X-axis calibration offset
  float calibrationOffsetY = 0.0f; ///< Y-axis calibration offset
  float calibrationOffsetZ = 0.0f; ///< Z-axis calibration offset
  float calibrationScaleX = 1.0f;  ///< X-axis calibration scale
  float calibrationScaleY = 1.0f;  ///< Y-axis calibration scale
  float calibrationScaleZ = 1.0f;  ///< Z-axis calibration scale
};

/**
 * @brief Enhanced haptic feedback data.
 */
struct HapticFeedback {
  uint8_t leftMotor = 0;  ///< Left motor intensity (0-255)
  uint8_t rightMotor = 0; ///< Right motor intensity (0-255)
  uint16_t duration = 0;  ///< Duration in milliseconds
  uint8_t pattern = 0;    ///< Vibration pattern ID
  float frequency = 0.0f; ///< Frequency for advanced haptics
  float amplitude = 0.0f; ///< Amplitude for advanced haptics
  bool enabled = true;    ///< Haptic feedback enabled
};

/**
 * @brief Enhanced battery information.
 */
struct BatteryInfo {
  uint8_t level = 100;     ///< Battery level (0-100)
  bool charging = false;   ///< Charging status
  bool wired = false;      ///< Wired connection
  uint16_t voltage = 0;    ///< Battery voltage (mV)
  int16_t temperature = 0; ///< Battery temperature (°C * 100)
  uint32_t cycleCount = 0; ///< Battery cycle count
};

/**
 * @brief Structure representing a controller's state.
 */
struct ControllerState {
  uint32_t buttons = 0;      ///< Button states (bitmask)
  uint8_t leftStickX = 128;  ///< Left stick X (0-255)
  uint8_t leftStickY = 128;  ///< Left stick Y (0-255)
  uint8_t rightStickX = 128; ///< Right stick X (0-255)
  uint8_t rightStickY = 128; ///< Right stick Y (0-255)
  uint8_t l2Trigger = 0;     ///< L2 trigger (0-255)
  uint8_t r2Trigger = 0;     ///< R2 trigger (0-255)

  // Enhanced motion and touch data
  MotionSensorData motion;  ///< Motion sensor data
  TouchpadData touchpad[2]; ///< Dual touchpad support
  BatteryInfo battery;      ///< Battery information
  HapticFeedback haptics;   ///< Haptic feedback data

  // Legacy fields for compatibility
  int16_t gyroX = 0;            ///< Gyroscope X (legacy)
  int16_t gyroY = 0;            ///< Gyroscope Y (legacy)
  int16_t gyroZ = 0;            ///< Gyroscope Z (legacy)
  int16_t accelX = 0;           ///< Accelerometer X (legacy)
  int16_t accelY = 0;           ///< Accelerometer Y (legacy)
  int16_t accelZ = 0;           ///< Accelerometer Z (legacy)
  uint8_t batteryLevel = 0;     ///< Battery level (legacy)
  bool touchpadPressed = false; ///< Touchpad pressed state (legacy)
  int16_t touchX = 0;           ///< Touchpad X coordinate (legacy)
  int16_t touchY = 0;           ///< Touchpad Y coordinate (legacy)
  uint8_t rumbleWeak = 0;       ///< Weak rumble intensity (legacy)
  uint8_t rumbleStrong = 0;     ///< Strong rumble intensity (legacy)
  uint8_t ledR = 0;             ///< LED red component (0-255)
  uint8_t ledG = 0;             ///< LED green component (0-255)
  uint8_t ledB = 255;           ///< LED blue component (0-255)

  // LED pattern support
  uint8_t ledPattern = 0;          ///< Current LED pattern
  uint32_t ledPatternDuration = 0; ///< Pattern duration (ms)
  std::chrono::steady_clock::time_point
      ledPatternStartTime; ///< Pattern start time
  bool connected = false;  ///< Connection status

  mutable uint64_t eventCount = 0;  ///< Number of events processed (mutable)
  mutable uint64_t cacheHits = 0;   ///< Cache hits for state access (mutable)
  mutable uint64_t cacheMisses = 0; ///< Cache misses for state access (mutable)
};

/**
 * @brief Structure defining controller register layout.
 */
struct ControllerRegisters {
  static constexpr uint64_t BASE_ADDRESS = 0xF000'0000; ///< Base address in MMU
  static constexpr uint32_t SIZE = 0x28; ///< Size per controller (40 bytes)
};

/**
 * @brief Manages PS4 controllers, interfacing with SDL and MMU.
 * @details Handles up to 8 controllers, processing input events, rumble, LEDs,
 *          and memory-mapped registers, with thread-safe operations and
 * metrics.
 */
class PS4ControllerManager {
public:
  static constexpr int MAX_CONTROLLERS = 8; ///< Maximum number of controllers

  /**
   * @brief Constructs the controller manager.
   * @param memory Reference to the PS4 MMU.
   */
  explicit PS4ControllerManager(PS4MMU &memory);

  /**
   * @brief Destructor, cleaning up resources.
   */
  ~PS4ControllerManager();

  /**
   * @brief Initializes the controller manager.
   * @return True on success, false otherwise.
   */
  bool Initialize();

  /**
   * @brief Shuts down the controller manager, releasing resources.
   */
  void Shutdown();
  /**
   * @brief Processes SDL input events.
   */
  void ProcessEvents();

  /**
   * @brief Handles SDL controller device added events from main thread.
   * @param deviceIndex The device index from SDL_CONTROLLERDEVICEADDED event.
   */
  void HandleControllerDeviceAdded(int deviceIndex);

  /**
   * @brief Handles SDL controller device removed events from main thread.
   * @param instanceId The instance ID from SDL_CONTROLLERDEVICEREMOVED event.
   */
  void HandleControllerDeviceRemoved(int instanceId);

  /**
   * @brief Connects a controller at the specified index.
   * @param index Controller index (0 to MAX_CONTROLLERS-1).
   * @return True on success, false if invalid or already connected.
   */
  bool ConnectController(int index);

  /**
   * @brief Disconnects a controller at the specified index.
   * @param index Controller index (0 to MAX_CONTROLLERS-1).
   * @return True on success, false if invalid or not connected.
   */
  bool DisconnectController(int index);

  /**
   * @brief Checks if a controller is connected.
   * @param index Controller index (0 to MAX_CONTROLLERS-1).
   * @return True if connected, false otherwise.
   */
  bool IsControllerConnected(int index) const;

  /**
   * @brief Retrieves the state of a controller.
   * @param index Controller index (0 to MAX_CONTROLLERS-1).
   * @return Controller state.
   */
  ControllerState GetControllerState(int index) const;

  /**
   * @brief Sets the rumble intensities for a controller.
   * @param index Controller index (0 to MAX_CONTROLLERS-1).
   * @param weak Weak rumble intensity (0-255).
   * @param strong Strong rumble intensity (0-255).
   */
  void SetRumble(int index, uint8_t weak, uint8_t strong);

  /**
   * @brief Sets the rumble intensities for a controller without acquiring
   * mutex.
   * @param index Controller index (0 to MAX_CONTROLLERS-1).
   * @param weak Weak rumble intensity (0-255).
   * @param strong Strong rumble intensity (0-255).
   * @note Caller must hold m_stateMutex lock.
   */
  void SetRumbleUnsafe(int index, uint8_t weak, uint8_t strong);

  /**
   * @brief Sets the LED color for a controller.
   * @param index Controller index (0 to MAX_CONTROLLERS-1).
   * @param r Red component (0-255).
   * @param g Green component (0-255).
   * @param b Blue component (0-255).
   */
  void SetLED(int index, uint8_t r, uint8_t g, uint8_t b);

  /**
   * @brief Sets the LED color for a controller without acquiring mutex.
   * @param index Controller index (0 to MAX_CONTROLLERS-1).
   * @param r Red component (0-255).
   * @param g Green component (0-255).
   * @param b Blue component (0-255).
   * @note Caller must hold m_stateMutex lock.
   */
  void SetLEDUnsafe(int index, uint8_t r, uint8_t g, uint8_t b);

  /**
   * @brief Reads a controller register from the MMU.
   * @param address Register address.
   * @return Register value, or 0 if invalid.
   */
  uint32_t ReadRegister(uint64_t address);

  /**
   * @brief Writes to a controller register in the MMU.
   * @param address Register address.
   * @param value Value to write.
   */
  void WriteRegister(uint64_t address, uint32_t value);

  /**
   * @brief Saves the controller manager state to a stream.
   * @param out Output stream.
   */
  void SaveState(std::ostream &out) const;

  /**
   * @brief Loads the controller manager state from a stream.
   * @param in Input stream.
   */
  void LoadState(std::istream &in);

  /**
   * @brief Enhanced controller methods.
   */
  bool CalibrateMotionSensors(int index);
  bool SetAdvancedHaptics(int index, const HapticFeedback &haptics);
  bool UpdateBatteryInfo(int index);
  bool ProcessTouchpadInput(int index, const TouchpadData &touchData);
  bool SetLEDPattern(int index, uint8_t pattern, uint32_t duration);
  bool GetMotionSensorData(int index, MotionSensorData &motion);
  bool GetTouchpadData(int index, TouchpadData touchData[2]);
  bool GetBatteryInfo(int index, BatteryInfo &battery);

  /**
   * @brief Controller statistics with atomic members to prevent race
   * conditions.
   */
  struct ControllerStats {
    std::atomic<uint64_t> eventCount{0}; ///< Total events processed
    std::atomic<uint64_t> totalLatencyUs{
        0}; ///< Total input latency (microseconds)
    std::atomic<uint64_t> latencyUs{
        0}; ///< Last operation latency (microseconds)
    std::atomic<uint64_t> cacheHits{0};   ///< Cache hits for register access
    std::atomic<uint64_t> cacheMisses{0}; ///< Cache misses for register access
    std::atomic<uint64_t> reconnectAttempts{
        0};                                ///< Number of reconnection attempts
    std::atomic<uint64_t> motionEvents{0}; ///< Motion sensor events processed
    std::atomic<uint64_t> touchEvents{0};  ///< Touchpad events processed
    std::atomic<uint64_t> hapticEvents{0}; ///< Haptic feedback events
    std::atomic<uint64_t> batteryUpdates{0}; ///< Battery status updates

    // Copy constructor for atomic members
    ControllerStats() = default;
    ControllerStats(const ControllerStats &other)
        : eventCount(other.eventCount.load()),
          totalLatencyUs(other.totalLatencyUs.load()),
          latencyUs(other.latencyUs.load()), cacheHits(other.cacheHits.load()),
          cacheMisses(other.cacheMisses.load()),
          reconnectAttempts(other.reconnectAttempts.load()),
          motionEvents(other.motionEvents.load()),
          touchEvents(other.touchEvents.load()),
          hapticEvents(other.hapticEvents.load()),
          batteryUpdates(other.batteryUpdates.load()) {}

    // Assignment operator for atomic members
    ControllerStats &operator=(const ControllerStats &other) {
      if (this != &other) {
        eventCount.store(other.eventCount.load());
        totalLatencyUs.store(other.totalLatencyUs.load());
        latencyUs.store(other.latencyUs.load());
        cacheHits.store(other.cacheHits.load());
        cacheMisses.store(other.cacheMisses.load());
        reconnectAttempts.store(other.reconnectAttempts.load());
        motionEvents.store(other.motionEvents.load());
        touchEvents.store(other.touchEvents.load());
        hapticEvents.store(other.hapticEvents.load());
        batteryUpdates.store(other.batteryUpdates.load());
      }
      return *this;
    }
  };

  // return stats by reference so callers can modify fields in const methods
  ControllerStats &GetControllerStats() { return m_stats; }
  const ControllerStats &GetControllerStats() const { return m_stats; }

  /**
   * @brief Gets the maximum number of controllers.
   * @return Number of controllers.
   */
  int GetControllerCount() const { return MAX_CONTROLLERS; }

  /**
   * @brief Checks the state of a specific button.
   * @param index Controller index (0 to MAX_CONTROLLERS-1).
   * @param button Button to check.
   * @return True if pressed, false otherwise.
   */
  bool GetButtonState(int index, int button) const;

  /**
   * @brief Gets the state of a specific axis.
   * @param index Controller index (0 to MAX_CONTROLLERS-1).
   * @param axis Axis to check.
   * @return Axis value.
   */
  int16_t GetAxisState(int index, int axis) const;

  /**
   * @brief Polls for controller events.
   */
  void PollEvents();

private:
  /**
   * @brief Updates MMU registers for a controller.
   * @param index Controller index (0 to MAX_CONTROLLERS-1).
   */
  void UpdateMemoryRegisters(int index);

  /**
   * @brief Input processing loop for the input thread.
   */
  void InputLoop();

  /**
   * @brief Maps an SDL controller to an index.
   * @param index Controller index (0 to MAX_CONTROLLERS-1).
   */
  void MapSDLController(int index);

  /**
   * @brief Maps an SDL controller to an index without acquiring mutex.
   * @param index Controller index (0 to MAX_CONTROLLERS-1).
   * @note Caller must hold m_stateMutex lock.
   */
  void MapSDLControllerUnsafe(int index);

  /**
   * @brief Unmaps an SDL controller from an index.
   * @param index Controller index (0 to MAX_CONTROLLERS-1).
   */
  void UnmapSDLController(int index);

  /**
   * @brief Unmaps an SDL controller from an index without acquiring mutex.
   * @param index Controller index (0 to MAX_CONTROLLERS-1).
   * @note Caller must hold m_stateMutex lock.
   */
  void UnmapSDLControllerUnsafe(int index);

  /**
   * @brief Attempts to reconnect a dropped controller.
   * @param index Controller index (0 to MAX_CONTROLLERS-1).
   * @return True if reconnected, false otherwise.
   */
  bool ReconnectController(int index);

  /**
   * @brief Attempts to reconnect a dropped controller without acquiring mutex.
   * @param index Controller index (0 to MAX_CONTROLLERS-1).
   * @return True if reconnected, false otherwise.
   * @note Caller must hold m_stateMutex lock.
   */
  bool ReconnectControllerUnsafe(int index);

  PS4MMU &m_memory; ///< Reference to MMU
  std::array<SDL_GameController *, MAX_CONTROLLERS>
      m_sdlControllers{}; ///< SDL controllers
  std::array<ControllerState, MAX_CONTROLLERS>
      m_states{};                                  ///< Controller states
  std::array<bool, MAX_CONTROLLERS> m_connected{}; ///< Connection status
  bool m_running = false;                 ///< Input thread running state
  std::thread m_inputThread;              ///< Input processing thread
  mutable std::shared_mutex m_stateMutex; ///< Mutex for thread safety
  std::condition_variable
      m_inputCondition;            ///< Condition variable for input thread
  mutable ControllerStats m_stats; ///< Controller statistics (mutable)

  // Enhanced controller processing helpers
  void ProcessMotionSensorData(int index);
  void ProcessTouchpadData(int index);
  void ProcessBatteryData(int index);
  void ProcessAdvancedHaptics(int index);
  void UpdateLegacyFields(int index);

  // Motion sensor calibration data
  struct CalibrationData {
    float gyroOffset[3] = {0.0f, 0.0f, 0.0f};
    float accelOffset[3] = {0.0f, 0.0f, 0.0f};
    float magnetOffset[3] = {0.0f, 0.0f, 0.0f};
    bool calibrated = false;
  };
  std::array<CalibrationData, MAX_CONTROLLERS> m_calibration;

  // Haptic pattern data
  struct HapticPattern {
    std::vector<uint8_t> leftMotorPattern;
    std::vector<uint8_t> rightMotorPattern;
    uint32_t duration;
    uint32_t currentStep;
    bool active;
  };
  std::array<HapticPattern, MAX_CONTROLLERS> m_hapticPatterns;
};

} // namespace ps4