// Copyright 2025 xAI
#include "apic.h"
#include <chrono>
#include <cstdint>
#include <mutex>
#include <stdexcept>
#include <string>
#include <iostream>
#include <unordered_map>
#include <array>

#ifdef _MSC_VER
#include <intrin.h>
#endif

// Include necessary headers for complete type definitions
#include "ps4_emulator.h"
#include "x86_64_cpu.h"
#include "interrupt_handler.h"

// Forward declarations to avoid circular dependencies
namespace ps4 {
class PS4Emulator;
}
namespace x86_64 {
class X86_64CPU;
}

// Simple logging replacement - disable spdlog usage
#ifndef SPDLOG_ACTIVE_LEVEL
#define SPDLOG_ACTIVE_LEVEL SPDLOG_LEVEL_OFF
#endif
#include <spdlog/spdlog.h>

// Override spdlog functions to be no-ops for this compilation unit
#ifdef spdlog
#undef spdlog
#endif

namespace spdlog {
static inline void info(...) {}
static inline void warn(...) {}
static inline void error(...) {}
static inline void trace(...) {}
}

namespace x86_64 {
struct APICException : std::runtime_error {
  explicit APICException(const std::string &msg) : std::runtime_error(msg) {}
};

/**
 * @brief Constructs an APIC instance for a specific core.
 * @details Initializes the APIC with a core ID, default base address, and
 * registers.
 * @param coreId The ID of the associated CPU core.
 */
APIC::APIC(uint32_t coreId)
    : m_coreId(coreId), m_baseAddress(0xFEE00000), m_enabled(false),
      m_timerCycles(0) {
  m_lapicRegisters.fill(0);
  m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::ID)] = coreId;
  m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::VERSION)] =
      0x14; // APIC version
  m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::SPURIOUS)] =
      0x1FF; // Spurious vector enabled
  spdlog::info("APIC initialized for core {}", coreId);
}

/**
 * @brief Destructs the APIC, ensuring proper shutdown.
 */
APIC::~APIC() noexcept {
  Shutdown();
  spdlog::info("APIC for core {} destroyed", m_coreId);
}

/**
 * @brief Initializes the APIC.
 * @details Sets up registers and enables the APIC for interrupt processing.
 * @return True on success, false on failure.
 */
bool APIC::Initialize() {
  std::lock_guard<std::mutex> lock(m_apicMutex);
  try {
    m_enabled = true;
    m_stats = APICStats();
    m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::SPURIOUS)] |=
        0x100; // Enable APIC
    m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::TIMER)] =
        0x20; // Timer vector
    spdlog::info("APIC for core {} initialized", m_coreId);
    return true;
  } catch (const std::exception &e) {
    spdlog::error("APIC initialization failed for core {}: {}", m_coreId,
                  e.what());
    return false;
  }
}

/**
 * @brief Shuts down the APIC, disabling interrupt processing.
 */
void APIC::Shutdown() {
  std::lock_guard<std::mutex> lock(m_apicMutex);
  m_enabled = false;
  m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::SPURIOUS)] &=
      ~0x100; // Disable APIC
  spdlog::info("APIC for core {} shut down", m_coreId);
}

/**
 * @brief Reads an APIC register.
 * @details Retrieves the value of the specified LAPIC register, thread-safely.
 * @param reg The register to read.
 * @return The register value.
 */
uint32_t APIC::ReadRegister(LAPICRegister reg) {
  std::lock_guard<std::mutex> lock(m_apicMutex);
  auto start = std::chrono::steady_clock::now();
  if (!m_enabled) {
    spdlog::warn("Read from disabled APIC core {} register {}", m_coreId,
                 static_cast<uint32_t>(reg));
    return 0;
  }
  uint32_t value = m_lapicRegisters[static_cast<uint32_t>(reg)];
  m_stats.operationCount++;
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
  spdlog::trace("APIC core {} read register {}: 0x{:x}", m_coreId,
                static_cast<uint32_t>(reg), value);
  return value;
}

/**
 * @brief Writes to an APIC register.
 * @details Updates the specified LAPIC register, handling special cases like
 * ICR and timer writes.
 * @param reg The register to write.
 * @param value The value to write.
 */
void APIC::WriteRegister(LAPICRegister reg, uint32_t value) {
  std::lock_guard<std::mutex> lock(m_apicMutex);
  auto start = std::chrono::steady_clock::now();
  if (!m_enabled) {
    spdlog::warn("Write to disabled APIC core {} register {}", m_coreId,
                 static_cast<uint32_t>(reg));
    return;
  }
  try {
    switch (reg) {
    case LAPICRegister::EOI:
      spdlog::trace("APIC core {} EOI written", m_coreId);
      break;
    case LAPICRegister::ICR_LOW:
      m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::ICR_LOW)] = value;
      HandleICRWrite(
          value,
          m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::ICR_HIGH)]);
      break;
    case LAPICRegister::ICR_HIGH:
      m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::ICR_HIGH)] = value;
      break;
    case LAPICRegister::SPURIOUS:
      m_lapicRegisters[static_cast<uint32_t>(reg)] = value & 0x1FF;
      m_enabled = (value & 0x100) != 0;
      spdlog::trace("APIC core {} spurious register set: enabled={}", m_coreId,
                    m_enabled);
      break;
    case LAPICRegister::TIMER_INITIAL_COUNT:
      m_lapicRegisters[static_cast<uint32_t>(reg)] = value;
      m_timerCycles = value;
      spdlog::trace("APIC core {} timer initial count set: {}", m_coreId,
                    value);
      break;
    default:
      m_lapicRegisters[static_cast<uint32_t>(reg)] = value;
      spdlog::trace("APIC core {} write register {}: 0x{:x}", m_coreId,
                    static_cast<uint32_t>(reg), value);
    }
    m_stats.operationCount++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
  } catch (const std::exception &e) {
    spdlog::error("APIC core {} register write failed: {}", m_coreId, e.what());
  }
}

/**
 * @brief Sends an Inter-Processor Interrupt (IPI).
 * @details Dispatches an IPI to the specified destination core with the given
 * vector and type.
 * @param destination The destination core ID or logical ID.
 * @param vector The interrupt vector.
 * @param type The IPI type.
 */
void APIC::SendIPI(uint32_t destination, uint32_t vector, IPIType type) {
  std::lock_guard<std::mutex> lock(m_apicMutex);
  auto start = std::chrono::steady_clock::now();
  if (!m_enabled) {
    spdlog::warn("IPI sent from disabled APIC core {}", m_coreId);
    return;
  }
  uint32_t targetCore = destination;
  try {
    if (type == IPIType::LOWEST_PRIORITY) {
      targetCore = FindLowestPriorityCore();
      if (targetCore == UINT32_MAX) {
        spdlog::error("No valid target for lowest priority IPI from core {}",
                      m_coreId);
        return;
      }
    } else {
      targetCore = FindLogicalDestination(destination);
      if (targetCore == UINT32_MAX) {
        spdlog::trace(
            "No valid logical destination 0x{:x} for IPI from core {}",
            destination, m_coreId);
        return;
      }
    }
    
    // Implement proper IPI delivery mechanism
    // Get target CPU and its APIC for IPI delivery
    ps4::PS4Emulator *emulator = &ps4::PS4Emulator::GetInstance();
    if (emulator && targetCore < emulator->GetCPUCount()) {
      x86_64::X86_64CPU &targetCpu = emulator->GetCPU(targetCore);
      x86_64::APIC &targetApic = targetCpu.GetAPIC();

      // Check if target APIC is enabled before delivery
      if (targetApic.ReadRegister(LAPICRegister::SPURIOUS) & 0x100) {
        // Deliver the interrupt to the target APIC
        targetApic.HandleInterrupt(vector);
        spdlog::trace("IPI delivered to core {}: vector=0x{:x}, type={}",
                      targetCore, vector, static_cast<uint32_t>(type));
      } else {
        spdlog::warn("IPI to disabled APIC core {} dropped", targetCore);
      }
    } else {
      spdlog::error("Invalid target core {} for IPI delivery", targetCore);
    }

    m_stats.operationCount++;
    m_stats.interruptCounts[vector]++;
    m_stats.ipiTypeCounts[type]++;
    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    spdlog::info("APIC core {} sent IPI to core {}: vector=0x{:x}, type={}",
                 m_coreId, targetCore, vector, static_cast<uint32_t>(type));
  } catch (const std::exception &e) {
    spdlog::error("APIC core {} failed to send IPI: {}", m_coreId, e.what());
  }
}

/**
 * @brief Handles an interrupt received by the APIC.
 * @details Processes the interrupt vector, forwarding it to the CPU’s interrupt
 * handler.
 * @param vector The interrupt vector.
 */
void APIC::HandleInterrupt(uint32_t vector) {
  std::lock_guard<std::mutex> lock(m_apicMutex);
  auto start = std::chrono::steady_clock::now();
  if (!m_enabled) {
    spdlog::warn("Interrupt 0x{:x} ignored on disabled APIC core {}", vector,
                 m_coreId);
    return;
  }
try {
  // Get the associated CPU and trigger the interrupt through the interrupt
  // handler
  ps4::PS4Emulator *emulator = &ps4::PS4Emulator::GetInstance();
  if (emulator) {
    x86_64::X86_64CPU &cpu = emulator->GetCPU(m_coreId);
    x86_64::InterruptHandler &intHandler = emulator->GetInterruptHandler();

    // Check priority against TPR (Task Priority Register)
    uint32_t tpr =
        m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::TPR)];
    uint32_t vectorPriority = (vector >> 4) & 0xF;
    uint32_t tprPriority = (tpr >> 4) & 0xF;

    if (vectorPriority > tprPriority) {
      // Trigger the interrupt through the CPU's interrupt handler
      intHandler.HandleInterrupt(static_cast<uint8_t>(vector), 0);
      spdlog::trace(
          "Interrupt 0x{:x} delivered to core {} (priority {} > TPR {})",
          vector, m_coreId, vectorPriority, tprPriority);
    } else {
      spdlog::trace("Interrupt 0x{:x} blocked by TPR on core {} (priority {} "
                    "<= TPR {})",
                    vector, m_coreId, vectorPriority, tprPriority);
    }
  } else {
    spdlog::error(
        "Cannot deliver interrupt: emulator instance not available");
  }

  m_stats.operationCount++;
  m_stats.interruptCounts[vector]++;
  auto end = std::chrono::steady_clock::now();
  m_stats.totalLatencyUs +=
      std::chrono::duration_cast<std::chrono::microseconds>(end - start)
          .count();
  spdlog::trace("APIC core {} handled interrupt 0x{:x}", m_coreId, vector);
} catch (const std::exception &e) {
  spdlog::error("APIC core {} failed to handle interrupt 0x{:x}: {}", m_coreId,
                vector, e.what());
}
}

/**
 * @brief Updates the APIC timer state.
 * @details Decrements the timer count and triggers a timer interrupt if
 * necessary.
 * @param cycles Number of CPU cycles to advance.
 */
void APIC::UpdateTimer(uint64_t cycles) {
  std::lock_guard<std::mutex> lock(m_apicMutex);
  if (!m_enabled || m_timerCycles == 0) {
    return;
  }
  try {
    m_timerCycles = (m_timerCycles > cycles) ? m_timerCycles - cycles : 0;
    if (m_timerCycles == 0) {
      uint32_t vector =
          m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::TIMER)] & 0xFF;
      HandleInterrupt(vector);
      m_stats.timerInterrupts++;
      // Reload timer if periodic mode is enabled
      if (m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::TIMER)] &
          0x20000) {
        m_timerCycles = m_lapicRegisters[static_cast<uint32_t>(
            LAPICRegister::TIMER_INITIAL_COUNT)];
      }
      spdlog::trace("APIC core {} timer interrupt triggered: vector=0x{:x}",
                    m_coreId, vector);
    }
  } catch (const std::exception &e) {
    spdlog::error("APIC core {} timer update failed: {}", m_coreId, e.what());
  }
}

/**
 * @brief Handles writes to the Interrupt Command Register (ICR).
 * @details Processes ICR low and high values to send IPIs based on the
 * specified parameters.
 * @param icrLow The low 32 bits of the ICR.
 * @param icrHigh The high 32 bits of the ICR.
 */
void APIC::HandleICRWrite(uint32_t icrLow, uint32_t icrHigh) {
  uint32_t vector = icrLow & 0xFF;
  uint32_t deliveryMode = (icrLow >> 8) & 0x7;
  uint32_t destinationMode =
      (icrLow >> 11) & 0x1; // TODO: Use for physical vs logical addressing
  uint32_t destination = (icrHigh >> 24) & 0xFF;
  IPIType type = static_cast<IPIType>(deliveryMode);
  spdlog::trace(
      "APIC core {} ICR write: vector=0x{:x}, mode=0x{:x}, dest=0x{:x}",
      m_coreId, vector, deliveryMode, destination);
  SendIPI(destination, vector, type);
}

/**
 * @brief Finds the core corresponding to a logical destination ID.
 * @details Maps the logical ID to a physical core ID.
 * @param logicalId The logical destination ID.
 * @return The core ID, or UINT32_MAX if not found.
 */
uint32_t APIC::FindLogicalDestination(
    uint32_t logicalId) { // Implement proper logical destination mapping
  try {
    ps4::PS4Emulator *emulator = &ps4::PS4Emulator::GetInstance();
    if (!emulator) {
      return UINT32_MAX;
    }

    uint32_t coreCount = emulator->GetCPUCount();

    // Check destination format register to determine addressing mode
    uint32_t dfr = m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::DFR)];
    bool flatModel = (dfr & 0xF0000000) == 0xF0000000;

    if (flatModel) {
      // Flat model: bit N in logical ID corresponds to core N
      for (uint32_t coreId = 0; coreId < coreCount && coreId < 32; ++coreId) {
        if (logicalId & (1U << coreId)) {
          x86_64::X86_64CPU &cpu = emulator->GetCPU(coreId);
          x86_64::APIC &apic = cpu.GetAPIC();
          uint32_t ldr = apic.ReadRegister(LAPICRegister::LDR);

          // Check if this core's logical destination register matches
          if ((ldr >> 24) & (1U << coreId)) {
            return coreId;
          }
        }
      }
    } else {
      // Cluster model: use cluster ID and member mask
      uint32_t clusterId = (logicalId >> 4) & 0xF;
      uint32_t memberMask = logicalId & 0xF;

      for (uint32_t coreId = 0; coreId < coreCount; ++coreId) {
        x86_64::X86_64CPU &cpu = emulator->GetCPU(coreId);
        x86_64::APIC &apic = cpu.GetAPIC();
        uint32_t ldr = apic.ReadRegister(LAPICRegister::LDR);

        uint32_t coreCluster = (ldr >> 28) & 0xF;
        uint32_t coreMask = (ldr >> 24) & 0xF;

        if (coreCluster == clusterId && (coreMask & memberMask)) {
          return coreId;
        }
      }
    }
  } catch (const std::exception &e) {
    spdlog::error("Logical destination mapping failed: {}", e.what());
  }

  // Fallback: direct mapping for cores 0-7 if no match found
  if (logicalId < 8) {
    return logicalId;
  }
  return UINT32_MAX;
}

/**
 * @brief Finds the core with the lowest priority for IPI delivery.
 * @details Selects a core based on TPR values from each core's APIC.
 * @return The core ID, or UINT32_MAX if none available.
 */
uint32_t APIC::FindLowestPriorityCore() { // Implement proper priority-based
                                          // core selection using TPR values
  try {
    ps4::PS4Emulator *emulator = &ps4::PS4Emulator::GetInstance();
    if (!emulator) {
      return 0; // Fallback to core 0
    }

    uint32_t coreCount = emulator->GetCPUCount();
    uint32_t selectedCore = UINT32_MAX;
    uint32_t lowestPriority = UINT32_MAX;

    // Search for the core with the lowest TPR (Task Priority Register) value
    for (uint32_t coreId = 0; coreId < coreCount; ++coreId) {
      try {
        x86_64::X86_64CPU &cpu = emulator->GetCPU(coreId);
        x86_64::APIC &apic = cpu.GetAPIC();

        // Check if APIC is enabled
        uint32_t spurious = apic.ReadRegister(LAPICRegister::SPURIOUS);
        if (!(spurious & 0x100)) {
          continue; // Skip disabled APICs
        }

        uint32_t tpr = apic.ReadRegister(LAPICRegister::TPR);
        uint32_t priority = (tpr >> 4) & 0xF; // Extract priority class

        // Lower TPR value means higher interrupt acceptance priority
        if (priority < lowestPriority) {
          lowestPriority = priority;
          selectedCore = coreId;
        }
      } catch (const std::exception &e) {
        spdlog::warn("Error accessing core {} for priority selection: {}",
                     coreId, e.what());
        continue;
      }
    }

    if (selectedCore != UINT32_MAX) {
      spdlog::trace(
          "Selected core {} for lowest priority IPI (TPR priority={})",
          selectedCore, lowestPriority);
      return selectedCore;
    }
  } catch (const std::exception &e) {
    spdlog::error("Priority-based core selection failed: {}", e.what());
  }

  // Fallback to core 0 if no suitable core found
  uint32_t targetCore = 0;

  // Use our own TPR as a baseline
  uint32_t ourTpr = m_lapicRegisters[static_cast<uint32_t>(LAPICRegister::TPR)];
  spdlog::trace("Using fallback core {} for lowest priority IPI (TPR={})",
                targetCore, ourTpr);

  return targetCore;
}

/**
 * @brief Counts trailing zeros in a 32-bit value.
 * @param value The input value.
 * @return The number of trailing zeros.
 */
uint32_t APIC::CountTrailingZeros(uint32_t value) {
  if (value == 0)
    return 32;
#ifdef _MSC_VER
  unsigned long idx;
  _BitScanForward(&idx, value);
  return idx;
#else
  return __builtin_ctz(value);
#endif
}

/**
 * @brief Counts leading zeros in a 32-bit value.
 * @param value The input value.
 * @return The number of leading zeros.
 */
uint32_t APIC::CountLeadingZeros(uint32_t value) {
  if (value == 0)
    return 32;
#ifdef _MSC_VER
  unsigned long idx;
  _BitScanReverse(&idx, value);
  return 31 - idx;
#else
  return __builtin_clz(value);
#endif
}

/**
 * @brief Saves the APIC state to a stream.
 * @details Serializes register values, statistics, and timer state.
 * @param out The output stream.
 */
void APIC::SaveState(std::ostream &out) const {
  std::lock_guard<std::mutex> lock(m_apicMutex);
  uint32_t version = 1;
  out.write(reinterpret_cast<const char *>(&version), sizeof(version));
  out.write(reinterpret_cast<const char *>(&m_coreId), sizeof(m_coreId));
  out.write(reinterpret_cast<const char *>(&m_baseAddress),
            sizeof(m_baseAddress));
  out.write(reinterpret_cast<const char *>(&m_enabled), sizeof(m_enabled));
  out.write(reinterpret_cast<const char *>(m_lapicRegisters.data()),
            m_lapicRegisters.size() * sizeof(uint32_t));
  out.write(reinterpret_cast<const char *>(&m_stats.operationCount),
            sizeof(m_stats.operationCount));
  out.write(reinterpret_cast<const char *>(&m_stats.totalLatencyUs),
            sizeof(m_stats.totalLatencyUs));
  out.write(reinterpret_cast<const char *>(&m_timerCycles),
            sizeof(m_timerCycles));
  uint32_t interruptCount =
      static_cast<uint32_t>(m_stats.interruptCounts.size());
  out.write(reinterpret_cast<const char *>(&interruptCount),
            sizeof(interruptCount));
  for (const auto &entry : m_stats.interruptCounts) {
    out.write(reinterpret_cast<const char *>(&entry.first),
              sizeof(entry.first));
    out.write(reinterpret_cast<const char *>(&entry.second),
              sizeof(entry.second));
  }
  uint32_t ipiTypeCount = static_cast<uint32_t>(m_stats.ipiTypeCounts.size());
  out.write(reinterpret_cast<const char *>(&ipiTypeCount),
            sizeof(ipiTypeCount));
  for (const auto &entry : m_stats.ipiTypeCounts) {
    out.write(reinterpret_cast<const char *>(&entry.first),
              sizeof(entry.first));
    out.write(reinterpret_cast<const char *>(&entry.second),
              sizeof(entry.second));
  }
  out.write(reinterpret_cast<const char *>(&m_stats.timerInterrupts),
            sizeof(m_stats.timerInterrupts));
  spdlog::info("APIC core {} state saved", m_coreId);
}

/**
 * @brief Loads the APIC state from a stream.
 * @details Deserializes register values, statistics, and timer state.
 * @param in The input stream.
 */
void APIC::LoadState(std::istream &in) {
  std::lock_guard<std::mutex> lock(m_apicMutex);
  uint32_t version;
  in.read(reinterpret_cast<char *>(&version), sizeof(version));
  if (version != 1) {
    spdlog::error("Unsupported APIC state version: {}", version);
    throw APICException("Invalid APIC state version");
  }
  in.read(reinterpret_cast<char *>(&m_coreId), sizeof(m_coreId));
  in.read(reinterpret_cast<char *>(&m_baseAddress), sizeof(m_baseAddress));
  in.read(reinterpret_cast<char *>(&m_enabled), sizeof(m_enabled));
  in.read(reinterpret_cast<char *>(m_lapicRegisters.data()),
          m_lapicRegisters.size() * sizeof(uint32_t));
  in.read(reinterpret_cast<char *>(&m_stats.operationCount),
          sizeof(m_stats.operationCount));
  in.read(reinterpret_cast<char *>(&m_stats.totalLatencyUs),
          sizeof(m_stats.totalLatencyUs));
  in.read(reinterpret_cast<char *>(&m_timerCycles), sizeof(m_timerCycles));
  uint32_t interruptCount;
  in.read(reinterpret_cast<char *>(&interruptCount), sizeof(interruptCount));
  m_stats.interruptCounts.clear();
  for (uint32_t i = 0; i < interruptCount && in.good(); ++i) {
    uint32_t vector;
    uint64_t count;
    in.read(reinterpret_cast<char *>(&vector), sizeof(vector));
    in.read(reinterpret_cast<char *>(&count), sizeof(count));
    m_stats.interruptCounts[vector] = count;
  }
  uint32_t ipiTypeCount;
  in.read(reinterpret_cast<char *>(&ipiTypeCount), sizeof(ipiTypeCount));
  m_stats.ipiTypeCounts.clear();
  for (uint32_t i = 0; i < ipiTypeCount && in.good(); ++i) {
    IPIType type;
    uint64_t count;
    in.read(reinterpret_cast<char *>(&type), sizeof(type));
    in.read(reinterpret_cast<char *>(&count), sizeof(count));
    m_stats.ipiTypeCounts[type] = count;
  }
  in.read(reinterpret_cast<char *>(&m_stats.timerInterrupts),
          sizeof(m_stats.timerInterrupts));
  spdlog::info("APIC core {} state loaded", m_coreId);
}

/**
 * @brief Retrieves APIC operation statistics.
 * @return The APIC statistics structure.
 */
APICStats APIC::GetStats() const {
  std::lock_guard<std::mutex> lock(m_apicMutex);
  return m_stats;
}
} // namespace x86_64