<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_SELF(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_self - return identifier of current thread 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> 
</P>
<P><B>pthread_t pthread_self(void);</B> 
</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P><B>pthread_self</B> return the thread identifier for the calling
thread. 
</P>
<P><B>PThreads4W</B> also provides support for <B>Win32</B> native
threads to interact with POSIX threads through the pthreads API.
Whereas all threads created via a call to pthread_create have a POSIX
thread ID and thread state, the library ensures that any Win32 native
threads that interact through the Pthreads API also generate a POSIX
thread ID and thread state when and if necessary. This provides full
<SPAN LANG="en-GB">reciprocity</SPAN> between Win32 and POSIX
threads. Win32 native threads that generate a POSIX thread ID and
state are treated by the library as having been created with the
<B>PTHREAD_CREATE_DETACHED</B> attribute.</P>
<P>Any Win32 native thread may call <B>pthread_self</B> directly to
return it's POSIX thread identifier. The ID and state will be
generated if it does not already exist. Win32 native threads do not
need to call <B>pthread_self</B> before calling PThreads4W routines
unless that routine requires a pthread_t parameter.</P>
<H2><A HREF="#toc3" NAME="sect3">Author</A></H2>
<P>Xavier Leroy &lt;<EMAIL>&gt; 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<H2><A HREF="#toc4" NAME="sect4">See Also</A></H2>
<P><A HREF="pthread_equal.html"><B>pthread_equal</B>(3)</A> ,
<A HREF="pthread_join.html"><B>pthread_join</B>(3)</A> ,
<A HREF="pthread_detach.html"><B>pthread_detach</B>(3)</A> ,
<A HREF="pthread_setschedparam.html"><B>pthread_setschedparam</B>(3)</A>
, <A HREF="pthread_setschedparam.html"><B>pthread_getschedparam</B>(3)</A>
. 
</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Author</A>
		</P>
	<LI><P><A HREF="#sect4" NAME="toc4">See Also</A> 
	</P>
</UL>
</BODY>
</HTML>
