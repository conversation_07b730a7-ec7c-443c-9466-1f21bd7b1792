<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_SETCANCELSTATE(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_cancel, pthread_setcancelstate, pthread_setcanceltype,
pthread_testcancel - thread cancellation 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> 
</P>
<P><B>int pthread_cancel(pthread_t </B><I>thread</I><B>);</B> 
</P>
<P><B>int pthread_setcancelstate(int </B><I>state</I><B>, int
*</B><I>oldstate</I><B>);</B> 
</P>
<P><B>int pthread_setcanceltype(int </B><I>type</I><B>, int
*</B><I>oldtype</I><B>);</B> 
</P>
<P><B>void pthread_testcancel(void);</B> 
</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P>Cancellation is the mechanism by which a thread can terminate the
execution of another thread. More precisely, a thread can send a
cancellation request to another thread. Depending on its settings,
the target thread can then either ignore the request, honor it
immediately, or defer it until it reaches a cancellation point. 
</P>
<P>When a thread eventually honors a cancellation request, it
performs as if <B>pthread_exit(PTHREAD_CANCELED)</B> has been called
at that point: all cleanup handlers are executed in reverse order,
destructor functions for thread-specific data are called, and finally
the thread stops executing with the return value <B>PTHREAD_CANCELED</B>.
See <A HREF="pthread_exit.html"><B>pthread_exit</B>(3)</A> for more
information. 
</P>
<P><B>pthread_cancel</B> sends a cancellation request to the thread
denoted by the <I>thread</I> argument. 
</P>
<P><B>pthread_setcancelstate</B> changes the cancellation state for
the calling thread -- that is, whether cancellation requests are
ignored or not. The <I>state</I> argument is the new cancellation
state: either <B>PTHREAD_CANCEL_ENABLE</B> to enable cancellation, or
<B>PTHREAD_CANCEL_DISABLE</B> to disable cancellation (cancellation
requests are ignored). If <I>oldstate</I> is not <B>NULL</B>, the
previous cancellation state is stored in the location pointed to by
<I>oldstate</I>, and can thus be restored later by another call to
<B>pthread_setcancelstate</B>. 
</P>
<P><B>pthread_setcanceltype</B> changes the type of responses to
cancellation requests for the calling thread: asynchronous
(immediate) or deferred. The <I>type</I> argument is the new
cancellation type: either <B>PTHREAD_CANCEL_ASYNCHRONOUS</B> to
cancel the calling thread as soon as the cancellation request is
received, or <B>PTHREAD_CANCEL_DEFERRED</B> to keep the cancellation
request pending until the next cancellation point. If <I>oldtype</I>
is not <B>NULL</B>, the previous cancellation state is stored in the
location pointed to by <I>oldtype</I>, and can thus be restored later
by another call to <B>pthread_setcanceltype</B>. 
</P>
<P><B>PThreads4W</B> provides two levels of support for
<B>PTHREAD_CANCEL_ASYNCHRONOUS</B>: full and partial. Full support
requires an additional DLL and driver be installed on the Windows
system (see the See Also section below) that allows blocked threads
to be cancelled immediately. Partial support means that the target
thread will not cancel until it resumes execution naturally. Partial
support is provided if either the DLL or the driver are not
automatically detected by the PThreads4W library at run-time.</P>
<P>Threads are always created by <A HREF="pthread_create.html"><B>pthread_create</B>(3)</A>
with cancellation enabled and deferred. That is, the initial
cancellation state is <B>PTHREAD_CANCEL_ENABLE</B> and the initial
type is <B>PTHREAD_CANCEL_DEFERRED</B>. 
</P>
<P>Cancellation points are those points in the program execution
where a test for pending cancellation requests is performed and
cancellation is executed if positive. The following POSIX threads
functions are cancellation points: 
</P>
<P><A HREF="pthread_join.html"><B>pthread_join</B>(3)</A>
<BR><A HREF="pthread_cond_wait.html"><B>pthread_cond_wait</B>(3)</A>
<BR><A HREF="pthread_cond_timedwait.html"><B>pthread_cond_timedwait</B>(3)</A>
<BR><A HREF="pthread_testcancel.html"><B>pthread_testcancel</B>(3)</A>
<BR><A HREF="sem_wait.html"><B>sem_wait</B>(3)</A> <BR><A HREF="sem_timedwait.html"><B>sem_timedwait</B>(3)</A>
<BR><A HREF="sigwait.html"><B>sigwait</B>(3)</A> (not supported under
<B>PThreads4W</B>)</P>
<P><B>PThreads4W</B> provides two functions to enable additional
cancellation points to be created in user functions that block on
Win32 HANDLEs:</P>
<P><A HREF="pthreadCancelableWait.html">pthreadCancelableWait()</A>
<BR><A HREF="pthreadCancelableTimedWait.html">pthreadCancelableTimedWait()</A></P>
<P>All other POSIX threads functions are guaranteed not to be
cancellation points. That is, they never perform cancellation in
deferred cancellation mode. 
</P>
<P><B>pthread_testcancel</B> does nothing except testing for pending
cancellation and executing it. Its purpose is to introduce explicit
checks for cancellation in long sequences of code that do not call
cancellation point functions otherwise. 
</P>
<H2><A HREF="#toc3" NAME="sect3">Return Value</A></H2>
<P><B>pthread_cancel</B>, <B>pthread_setcancelstate</B> and
<B>pthread_setcanceltype</B> return 0 on success and a non-zero error
code on error. 
</P>
<H2><A HREF="#toc4" NAME="sect4">Errors</A></H2>
<P><B>pthread_cancel</B> returns the following error code on error: 
</P>
<DL>
	<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>ESRCH</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		no thread could be found corresponding to that specified by the
		<I>thread</I> ID. 
		</DD></DL>
</DL>
<P>
<B>pthread_setcancelstate</B> returns the following error code on
error: 
</P>
<DL>
	<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		the <I>state</I> argument is not 
		</DD></DL>
</DL>
<BLOCKQUOTE>
<B>PTHREAD_CANCEL_ENABLE</B> nor <B>PTHREAD_CANCEL_DISABLE</B> 
</BLOCKQUOTE>
<P><B>pthread_setcanceltype</B> returns the following error code on
error: 
</P>
<DL>
	<DL>
		<DT STYLE="margin-right: 1cm; margin-bottom: 0.5cm"><B>EINVAL</B> 
		</DT><DD STYLE="margin-right: 1cm; margin-bottom: 0.5cm">
		the <I>type</I> argument is not 
		</DD></DL>
</DL>
<BLOCKQUOTE>
<B>PTHREAD_CANCEL_DEFERRED</B> nor <B>PTHREAD_CANCEL_ASYNCHRONOUS</B>
</BLOCKQUOTE>
<H2><A HREF="#toc5" NAME="sect5">Author</A></H2>
<P>Xavier Leroy &lt;<EMAIL>&gt; 
</P>
<P>Modified by Ross Johnson for use with <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A>.</P>
<H2><A HREF="#toc6" NAME="sect6">See Also</A></H2>
<P><A HREF="pthread_exit.html"><B>pthread_exit</B>(3)</A> ,
<A HREF="pthread_cleanup_push.html"><B>pthread_cleanup_push</B>(3)</A>
, <A HREF="pthread_cleanup_pop.html"><B>pthread_cleanup_pop</B>(3)</A>
, PThreads4W package README file 'Prerequisites' section. 
</P>
<H2><A HREF="#toc7" NAME="sect7">Bugs</A></H2>
<P>POSIX specifies that a number of system calls (basically, all
system calls that may block, such as <A HREF="read.html"><B>read</B>(2)</A>
, <A HREF="write.html"><B>write</B>(2)</A> , <A HREF="wait.html"><B>wait</B>(2)</A>
, etc.) and library functions that may call these system calls (e.g.
<A HREF="fprintf.html"><B>fprintf</B>(3)</A> ) are cancellation
points. <B>PThreads4W</B> is not integrated enough with the C
library to implement this, and thus none of the C library functions
is a cancellation point. 
</P>
<P>A workaround for these calls is to temporarily switch to
asynchronous cancellation (assuming full asynchronous cancellation
support is installed). So, checking for cancellation during a <B>read</B>
system call, for instance, can be achieved as follows: 
</P>
<BLOCKQUOTE><BR><BR>
</BLOCKQUOTE>
<PRE STYLE="margin-left: 1cm; margin-right: 1cm">pthread_setcanceltype(PTHREAD_CANCEL_ASYNCHRONOUS, &amp;oldCancelType);
read(fd, buffer, length);
pthread_setcanceltype(oldCancelType, NULL);</PRE>
<HR>
<BLOCKQUOTE><A NAME="toc"></A><B>Table of Contents</B></BLOCKQUOTE>
<UL>
	<LI><BLOCKQUOTE STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Return
	Value</A> 
	</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Errors</A>
		</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Author</A>
		</BLOCKQUOTE>
	<LI><BLOCKQUOTE STYLE="margin-bottom: 0cm"><A HREF="#sect6" NAME="toc6">See
	Also</A> 
	</BLOCKQUOTE>
	<LI><BLOCKQUOTE><A HREF="#sect7" NAME="toc7">Bugs</A> 
	</BLOCKQUOTE>
</UL>
</BODY>
</HTML>
