#include "memory_prefetcher.h"
#include <algorithm>
#include <chrono>
#include <cmath>
#include <spdlog/spdlog.h>
#include <thread>

// Add constants for configuration and magic numbers


namespace ps4 {
  
constexpr size_t kCacheLineSize = 64; // Standard cache line size in bytes
constexpr size_t kPatternCheckInterval =
    10; // Interval for checking access patterns
constexpr size_t kVisualizationInterval =
    100; // Interval for updating visualization data
constexpr size_t kMaxPrefetchCount =
    5; // Maximum number of addresses to prefetch per access
constexpr uint64_t kPatternExpirationNs =
    1'000'000'000ULL; // 1 second expiration for patterns to keep them relevant

// Helper to get current time in nanoseconds using steady_clock
static inline uint64_t CurrentTimeNs() {
  return std::chrono::duration_cast<std::chrono::nanoseconds>(
             std::chrono::steady_clock::now().time_since_epoch())
      .count();
}

MemoryPrefetcher::MemoryPrefetcher() {
  // CRITICAL FIX: Ensure all statistics are properly initialized to zero
  m_prefetchHits = 0;
  m_prefetchMisses = 0;
  m_totalAccesses = 0;
  m_totalPrefetchRequests = 0;
  m_totalPrefetches = 0;
  m_sequentialPatterns = 0;
  m_stridePatterns = 0;

  // Reserve capacity for patterns and visualization
  m_recentPatterns.reserve(100); // Use the class constant value
  m_patterns.reserve(100);
  m_prefetchedAddresses.reserve(200);
}

bool MemoryPrefetcher::Initialize() {
  std::lock_guard<std::mutex> lock(m_prefetcherMutex);

  // Reset all internal structures
  m_recentAccesses.clear();
  m_patterns.clear();
  m_lastAccessByProcess.clear();
  m_prefetchedAddresses.clear();
  m_recentPatterns.clear();

  // Reset statistics
  m_prefetchHits = 0;
  m_prefetchMisses = 0;
  m_totalAccesses = 0;
  m_totalPrefetchRequests = 0;
  m_totalPrefetches = 0;
  m_sequentialPatterns = 0;
  m_stridePatterns = 0;

  spdlog::info("Memory prefetcher initialized");
  return true;
}

void MemoryPrefetcher::RegisterMemoryAccess(uint64_t address, size_t size,
                                            bool isWrite, uint64_t processId) {
  if (!m_enabled) {
    return;
  }

  std::lock_guard<std::mutex> lock(m_prefetcherMutex);

  // Get current timestamp
  uint64_t timestamp = CurrentTimeNs();

  // Check if this was a prefetch hit
  auto it = m_prefetchedAddresses.find(address);
  if (it != m_prefetchedAddresses.end()) {
    // This was a prefetch hit
    RecordPrefetchHit(address);
    // Remove from prefetched addresses to avoid double counting
    m_prefetchedAddresses.erase(it);
#ifdef DEBUG_PREFETCHER
    spdlog::info("Prefetch hit for address 0x{:x}", address);
#endif
  } else {
    // This was not a prefetch hit
    RecordPrefetchMiss(address);
#ifdef DEBUG_PREFETCHER
    spdlog::info("Prefetch miss for address 0x{:x}", address);
#endif
  }

  // Add to recent accesses for pattern detection
  MemoryAccess access;
  access.address = address;
  access.size = size;
  access.isWrite = isWrite;
  access.processId = processId;
  access.timestamp = timestamp;

  m_recentAccesses.push_back(access);

  // Maintain maximum size
  if (m_recentAccesses.size() > MAX_RECENT_ACCESSES) {
    m_recentAccesses.pop_front();
  }

  // Update last access by process
  m_lastAccessByProcess[processId] = address;

  // Detect patterns periodically (every kPatternCheckInterval accesses)
  if (m_totalAccesses % kPatternCheckInterval == 0) {
    DetectPatterns();
  }

  // Attempt to prefetch based on this access
  PrefetchData(address, processId);

  // Update visualization data periodically
  if (m_totalAccesses % kVisualizationInterval == 0) {
    UpdateVisualizationData();
  }

  // Periodic cleanup of stale prefetch data to prevent memory bloat
  if (m_totalAccesses % kVisualizationInterval == 0) {
    CleanupStalePrefetchData(timestamp);
  }
}

std::vector<uint64_t> MemoryPrefetcher::GetPrefetchAddresses(uint64_t address,
                                                             size_t size) {
  if (!m_enabled) {
    return {};
  }

  std::lock_guard<std::mutex> lock(m_prefetcherMutex);
  std::vector<uint64_t> prefetchAddresses;

  // Check if we have any patterns that match this address
  for (const auto &[baseAddr, pattern] : m_patterns) {
    // Skip low confidence patterns
    if (pattern.confidence < PATTERN_CONFIDENCE_THRESHOLD) {
      continue;
    }

    // Check if this address is part of a known pattern
    if (pattern.stride != 0 &&
        (address >= baseAddr &&
         address < baseAddr + std::abs(pattern.stride) * 32)) {
      // Calculate offset from pattern base
      int64_t offset = static_cast<int64_t>(address - baseAddr);

      // Calculate relative position in the pattern
      int64_t position = offset / pattern.stride;

      // Predict next addresses based on the pattern
      int prefetchCount = static_cast<int>(
          std::min(static_cast<float>(kMaxPrefetchCount),
                   m_aggressiveness * static_cast<float>(kMaxPrefetchCount)));

      for (int i = 1; i <= prefetchCount; ++i) {
        uint64_t prefetchAddr = baseAddr + (position + i) * pattern.stride;
        prefetchAddresses.push_back(prefetchAddr);

        // Also add the address to our prefetched list
        m_prefetchedAddresses[prefetchAddr] = CurrentTimeNs();
      }
    }
  }

  // Also do some simple next-line prefetching
  uint64_t nextLine = (address / kCacheLineSize + 1) * kCacheLineSize;
  prefetchAddresses.push_back(nextLine);
  m_prefetchedAddresses[nextLine] = CurrentTimeNs();

  return prefetchAddresses;
}

void MemoryPrefetcher::ExecutePrefetchHint(uint64_t address, size_t size) {
  if (!m_enabled) {
    return;
  }

  std::lock_guard<std::mutex> lock(m_prefetcherMutex);

  // Align address to cache line boundary
  uint64_t alignedAddress = address & ~(kCacheLineSize - 1);
  uint64_t currentTime = CurrentTimeNs();

  // Check if we've already prefetched this address recently
  auto it = m_prefetchedAddresses.find(alignedAddress);
  if (it != m_prefetchedAddresses.end() &&
      (currentTime - it->second) < 1000000) { // 1ms threshold
    return;                                   // Already prefetched recently
  }

  // Add the address to prefetched addresses
  m_prefetchedAddresses[alignedAddress] = currentTime;

  // Trigger asynchronous prefetch operation
  // In a real implementation, this would:
  // 1. Check if the data is already in cache
  // 2. If not, initiate a memory load operation
  // 3. Store the data in a prefetch buffer or cache

  // For now, we'll simulate the prefetch by scheduling it
  std::thread([this, alignedAddress, size]() {
    try {
      // Simulate memory access latency
      std::this_thread::sleep_for(std::chrono::microseconds(100));

      // In a real implementation, we would:
      // - Load the data from memory into cache/buffer
      // - Update cache statistics
      // - Handle any memory protection or access violations

      spdlog::trace("Prefetch completed for address 0x{:x}, size={}",
                    alignedAddress, size);

      // Update statistics
      std::lock_guard<std::mutex> lock(m_prefetcherMutex);
      m_totalPrefetches++;

    } catch (const std::exception &e) {
      spdlog::warn("Prefetch failed for address 0x{:x}: {}", alignedAddress,
                   e.what());
    }
  }).detach();

  spdlog::debug("Initiated prefetch for address 0x{:x}, size={}",
                alignedAddress, size);
}

MemoryPrefetcher::Stats MemoryPrefetcher::GetStats() const {
  std::lock_guard<std::mutex> lock(m_prefetcherMutex);
  Stats stats;
  stats.hitRate =
      (m_prefetchHits + m_prefetchMisses) > 0
          ? 100.0f * static_cast<float>(m_prefetchHits) /
                static_cast<float>(m_prefetchHits + m_prefetchMisses)
          : 0.0f;
  stats.totalAccesses = m_totalAccesses;
  stats.prefetchRequests = m_totalPrefetchRequests;
  stats.prefetchHits = m_prefetchHits;
  stats.sequentialPatterns = m_sequentialPatterns;
  stats.stridePatterns = m_stridePatterns;
  return stats;
}

std::vector<std::pair<uint64_t, int64_t>>
MemoryPrefetcher::GetRecentPatterns() const {
  std::lock_guard<std::mutex> lock(m_prefetcherMutex);
  return m_recentPatterns;
}

uint64_t MemoryPrefetcher::GetTotalPrefetchRequests() const {
  std::lock_guard<std::mutex> lock(m_prefetcherMutex);
  return m_totalPrefetchRequests;
}

uint64_t MemoryPrefetcher::GetTotalPrefetches() const {
  std::lock_guard<std::mutex> lock(m_prefetcherMutex);
  return m_totalPrefetches;
}

float MemoryPrefetcher::GetAveragePrefetchesPerRequest() const {
  std::lock_guard<std::mutex> lock(m_prefetcherMutex);
  if (m_totalPrefetchRequests == 0)
    return 0.0f;
  return static_cast<float>(m_totalPrefetches) /
         static_cast<float>(m_totalPrefetchRequests);
}

void MemoryPrefetcher::SetAggressiveness(float aggressiveness) {
  std::lock_guard<std::mutex> lock(m_prefetcherMutex);
  m_aggressiveness = std::clamp(aggressiveness, 0.0f, 3.0f);
}

void MemoryPrefetcher::SetEnabled(bool enabled) {
  std::lock_guard<std::mutex> lock(m_prefetcherMutex);
  m_enabled = enabled;
}

void MemoryPrefetcher::Reset() { Initialize(); }

void MemoryPrefetcher::DetectPatterns() {
  // Group accesses by process
  std::unordered_map<uint64_t, bool> processesWithActivity;

  for (const auto &access : m_recentAccesses) {
    processesWithActivity[access.processId] = true;
  }

  // Analyze each process separately
  for (const auto &[processId, _] : processesWithActivity) {
    AnalyzeStridePatterns(processId);
  }

  // Clean up old patterns (expire patterns not seen recently)
  const uint64_t currentTime = CurrentTimeNs();

  for (auto it = m_patterns.begin(); it != m_patterns.end();) {
    if (currentTime - it->second.lastTimestamp > kPatternExpirationNs) {
      it = m_patterns.erase(it);
    } else {
      ++it;
    }
  }

  // Prune patterns list to maximum allowed size
  if (m_patterns.size() > 100) {
    std::vector<std::pair<uint64_t, uint64_t>> patternTimestamps;
    patternTimestamps.reserve(m_patterns.size());
    for (const auto &p : m_patterns) {
      patternTimestamps.emplace_back(p.first, p.second.lastTimestamp);
    }
    std::sort(patternTimestamps.begin(), patternTimestamps.end(),
              [](auto &a, auto &b) { return a.second < b.second; });
    size_t pruneCount = m_patterns.size() - 100;
    for (size_t i = 0; i < pruneCount; ++i) {
      m_patterns.erase(patternTimestamps[i].first);
    }
  }

  // Prune old prefetch records to avoid unbounded growth
  for (auto it = m_prefetchedAddresses.begin();
       it != m_prefetchedAddresses.end();) {
    if (currentTime - it->second > kPatternExpirationNs) {
      it = m_prefetchedAddresses.erase(it);
    } else {
      ++it;
    }
  }
}

void MemoryPrefetcher::AnalyzeStridePatterns(uint64_t processId) {
  // Filter accesses for this process
  std::vector<MemoryAccess> processAccesses;

  for (const auto &access : m_recentAccesses) {
    if (access.processId == processId) {
      processAccesses.push_back(access);
    }
  }

  // Need at least 3 accesses to detect a pattern
  if (processAccesses.size() < 3) {
    return;
  }

  // Sort by timestamp
  std::sort(processAccesses.begin(), processAccesses.end(),
            [](const MemoryAccess &a, const MemoryAccess &b) {
              return a.timestamp < b.timestamp;
            });

  // Detect stride patterns
  for (size_t i = 0; i < processAccesses.size() - 2; ++i) {
    // Only look at read accesses for pattern detection
    if (processAccesses[i].isWrite) {
      continue;
    }

    uint64_t addr1 = processAccesses[i].address;
    uint64_t addr2 = processAccesses[i + 1].address;
    uint64_t addr3 = processAccesses[i + 2].address;

    // Calculate strides
    int64_t stride1 = static_cast<int64_t>(addr2) - static_cast<int64_t>(addr1);
    int64_t stride2 = static_cast<int64_t>(addr3) - static_cast<int64_t>(addr2);

    // Detect constant stride pattern
    if (stride1 == stride2 && stride1 != 0) {
      // We found a potential pattern
      uint64_t baseAddr = addr1;

      // Check if we already have this pattern
      auto it = m_patterns.find(baseAddr);
      if (it != m_patterns.end() && it->second.stride == stride1) {
        // Increase confidence and update timestamp
        it->second.confidence = std::min(it->second.confidence + 1, 10u);
        it->second.lastTimestamp = processAccesses[i + 2].timestamp;
        // Increment stride pattern count
        if (stride1 == kCacheLineSize || stride1 == -kCacheLineSize) {
          m_sequentialPatterns++;
        } else {
          m_stridePatterns++;
        }
      } else {
        // Add new pattern
        PatternEntry entry;
        entry.baseAddress = baseAddr;
        entry.stride = stride1;
        entry.confidence = 1;
        entry.lastTimestamp = processAccesses[i + 2].timestamp;

        m_patterns[baseAddr] = entry;
        // Increment pattern count
        if (stride1 == kCacheLineSize || stride1 == -kCacheLineSize) {
          m_sequentialPatterns++;
        } else {
          m_stridePatterns++;
        }
      }
    }
  }
}

void MemoryPrefetcher::PrefetchData(uint64_t address, uint64_t processId) {
  // Count this prefetch request
  m_totalPrefetchRequests++;

  // Get prefetch addresses based on this access
  auto prefetchAddresses =
      GetPrefetchAddresses(address, 8); // Assume 8-byte access size

  // Count total predicted prefetches
  m_totalPrefetches += prefetchAddresses.size();

  // Log prefetching if enabled
  if (!prefetchAddresses.empty() && spdlog::should_log(spdlog::level::debug)) {
    spdlog::debug("Prefetching {} addresses for process {}",
                  prefetchAddresses.size(), processId);
  }
}

void MemoryPrefetcher::RecordPrefetchHit(uint64_t address) {
  m_prefetchHits++;
  m_totalAccesses++;

  if (spdlog::should_log(spdlog::level::debug)) {
    spdlog::debug("Prefetch hit at address 0x{:x}", address);
  }
}

void MemoryPrefetcher::RecordPrefetchMiss(uint64_t address) {
  m_prefetchMisses++;
  m_totalAccesses++;
}

void MemoryPrefetcher::UpdateVisualizationData() {
  m_recentPatterns.clear();

  // Copy high-confidence patterns to visualization data
  for (const auto &[baseAddr, pattern] : m_patterns) {
    if (pattern.confidence >= PATTERN_CONFIDENCE_THRESHOLD) {
      m_recentPatterns.emplace_back(pattern.baseAddress, pattern.stride);
    }
  }
}

void MemoryPrefetcher::CleanupStalePrefetchData(uint64_t currentTime) {
  for (auto it = m_prefetchedAddresses.begin();
       it != m_prefetchedAddresses.end();) {
    if (currentTime - it->second > kPatternExpirationNs) {
      it = m_prefetchedAddresses.erase(it);
    } else {
      ++it;
    }
  }
#ifdef DEBUG_PREFETCHER
  spdlog::info("Cleaned up stale prefetch data, remaining entries: {}",
               m_prefetchedAddresses.size());
#endif
}

} // namespace ps4