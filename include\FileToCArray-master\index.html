﻿<!DOCTYPE html>
<html>
<head>
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-123032687-2"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'UA-123032687-2');
    </script>

    <title>File to C style array converter</title>
    <meta charset="utf-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="description" content="Coverts any file to a C style array. Useful if you want to embed/store a file (binary, text, image, whatever) into your code! It can also do image color format and size coversion. Use it for your Arduino or other embedded projects." />
    <link rel="icon" type="image/png" href="Images/favicon.png">
    <link rel="stylesheet" href="css/style.css" />
    <script src="https://code.jquery.com/jquery-2.1.4.min.js" type="text/javascript"></script>
    <script src="scripts/arrayUtils.js" type="text/javascript"></script>
    <script src="scripts/imageConverter.js" type="text/javascript"></script>
    <script src="scripts/stringConverter.js" type="text/javascript"></script>
    <script src="scripts/main.js" type="text/javascript"></script>
</head>
<body>
    <h1><img src="Images/icon.svg" width="32px"> File to C style array converter<span class="versionInfo" >v:&nbsp;<span id="versionInfo"></span></span>
        <br/>
        <span class="additionalInfo">
        <span id="description">&nbsp;</span> <a href="" id="gitLink" target="_blank"></a>
        </span>
    </h1>
    <hr/>
    <p>
        Select a file: <input type="file" id="inFileInput" value="" />
    </p>
    <fieldset>
        <legend>File info 
            <div class="tooltip">
                <span class="tooltiptext">This section contains basic information on the uploaded file.<br/>If it is an image, the size in pixel and a preview will be shown</span>
            </div>
        </legend>
        <div>
            <label for="fiName">Name</label>
            <span id="fiName"></span>
        </div>
        <div>
            <label for="fiSize">Size</label>
            <span id="fiSize"></span>
        </div>
        <div>
            <label for="fiType">Type</label>
            <span id="fiType"></span>
        </div>
        <div>
            <label for="fiLastModifiedDate">Last modified</label>
            <span id="fiLastModifiedDate"></span>
        </div>
        <div id="divPreview" class="fileIsImage">
            <label for="imgPreview">Preview
                <div class="tooltip">
                    <span class="tooltiptext">The grey border around the image is for visibility only.<br/>If it is shorter than 32px, or taller than 100px, it will be resized. Again, for visibility reasons.</span>
                </div>
            </label>
            <!-- preview image goes here -->
        </div>
    </fieldset>
    <fieldset id="fsConversionOptions">
        <legend>Conversion settings</legend>
        <div>
            <label for="selFormat">Code format
                <div class="tooltip">
                    <span class="tooltiptext">The array declaration will contain bytes in this format.<br/>So if you have choosen "Hex (0x00)", it will give you this output:
<pre>
static const unsigned char awesomeimage[] = {
  0x00, 0xff, 0xff
};
</pre>                        
                    </span>
                </div>
            </label>
            <select id="selFormat">
                <option value="HEX0" selected>Hex (0x00)</option>
                <option value="HEX_SLASH">Hex (\x00)</option>
                <option value="DEC">Decimal (000)</option>
                <option value="BIN">Binary (B00000000)</option>
            </select>
        </div>
        <div class="fileIsImage">
            <label for="cbForceBinary">Treat as binary
                <div class="tooltip">
                    <span class="tooltiptext">This tells the converter to treat the current file as a binary, eventhough it is an image.<br/>Every image conversion option will be disabled.<br/>Also will exclude the widht and height info from the output.</span>
                </div>
            </label>
            <input type="checkbox" id="cbForceBinary" />
        </div>
        <hr class="fileIsImage"/>
        <div class="fileIsImage">
            <label id="lblPaletteMod" for="cbPaletteMod">Palette mod
                <div class="tooltip">
                    <span class="tooltiptext">This controls the output palette format of the image<br/>For more info on this, please see <a href="https://en.wikipedia.org/wiki/Color_depth" target="_blank">the WikiPedia aricle on color depth</a></span>
                </div>
            </label>
            <select id="cbPaletteMod" class="imageConversionOption">
                <option value="32">32 bit RGBA (4bytes/pixel)</option>
                <option value="32r">32-bit BGRA (4bytes/pixel)</option>
                <option value="24" selected>24bit RGB (3bytes/pixel)</option>
                <option value="16">16bit RRRRRGGGGGGBBBBB (2byte/pixel)</option>
                <option value="16bgr">16bit BBBBBGGGGGGRRRRR (2byte/pixel)</option>
                <option value="15">15bit RRRRRGGGGGBBBBBA (2byte/pixel)</option>
                <option value="8">8bit RRRGGGBB (1byte/pixel)</option>
                <option value="8G">8bit grayscale (1byte/pixel)</option>
                <option value="1">1bit line art (1bit/pixel)</option>
            </select>
        </div>
        <div class="fileIsImage">
            <label for="txtResizeX">Resize
                <div class="tooltip">
                    <span class="tooltiptext">Fill only one of the size boxes to resize the image maintaining the original spect ratio.<br/>Keep both empty, if you do not want to change the size.</span>
                </div>
            </label>
            <input type="text" id="txtResizeX" class="imageConversionOption shortInput" />&nbsp;x&nbsp;<input type="text" id="txtResizeY" class="imageConversionOption shortInput" />&nbsp;(Fill only one to maintain aspect ratio)
        </div>
        <div class="fileIsImage">
            <label for="cbMultiLine" class="imageConversionOption">Multi line
                <div class="tooltip">
                    <span class="tooltiptext">The bytes in the array will be split into multiple lines, roughly matching the aspect ratio of the image.<br/>Unselect this to have all the bytes in a single row.</span>
                </div>
            </label>
            <input type="checkbox" id="cbMultiLine" class="imageConversionOption" checked />
        </div>
        <div class="fileIsImage image1BitModeOnly">
            <label for="cbForColumnRead" class="imageConversionOption image1BitModeOnly">Optimize for column read
                <div class="tooltip">
                    <span class="tooltiptext">For applications where you need to display the image one 8bit column at a time - as opposed to the normal mode where you plot the image by row.<br/><b>Row read optimized:</b><img alt="" src="Images/RowReadOptimized_small.png"/><br/><b>Column read optimized:</b><img alt="" src="Images/ColumnReadOptimized_small.png"/><br/>This is only available in 1bit mode!</span>
                </div> 
            </label>
            <input type="checkbox" id="cbForColumnRead" class="imageConversionOption image1BitModeOnly" />
        </div>
        <div class="fileIsImage">
            <label for="cbSeparateBytes" class="imageConversionOption">Separate bytes of pixels
                <div class="tooltip">
                    <span class="tooltiptext">If ticked every byte of every pixel will be added to the array separately. Otherwise one item in the array will represent all bytes of one pixel.</span>
                </div> 
            </label>
            <input type="checkbox" id="cbSeparateBytes" class="imageConversionOption"/>
        </div>
        <div class="fileIsImage">
            <label id="lblEndianness" for="selEndianness" class="imageConversionOption">Endianness</label>
            <select id="selEndianness" class="imageConversionOption">
                <option value="le">Little-endian</option>
                <option value="be">Big-endian</option>
            </select>
        </div>
        <!--<div class="fileIsImage">
            <label for="cbArrayType">Array type</label>
            <input type="radio" name="cbArrayType" value="SINGLE" class="imageConversionOption" checked />Single
            <input type="radio" name="cbArrayType" value="PERPIXEL" class="imageConversionOption" />Per pixel
        </div>-->
        <hr/>
        <div>
            <label for="cbStatic">static</label>
            <input type="checkbox" id="cbStatic" value="static" checked />
        </div>
        <div>
            <label for="cbConst">const</label>
            <input type="checkbox" id="cbConst" value="const" checked />
        </div>
        <div>
            <label for="cbUnsigned">unsigned</label>
            <input type="checkbox" id="cbUnsigned" value="unsigned" />
        </div>
        <div>
            <label id="lblDataType" for="selDataType">Data type</label>
            <select id="selDataType">
                <option value="byte">byte</option>
                <option value="char">char</option>
                <option value="int">int</option>
                <option value="word">word</option>
                <option value="short">short</option>
                <option value="long">long</option>
                <option value="int8_t">int8_t</option>
                <option value="int16_t">int16_t</option>
                <option value="int32_t">int32_t</option>
                <option value="int64_t">int64_t</option>
                <option value="uint8_t">uint8_t</option>
                <option value="uint16_t">uint16_t</option>
                <option value="uint32_t">uint32_t</option>
                <option value="uint64_t">uint64_t</option>
            </select>
        </div>
        <div>
            <label for="cbPROGMEM">PROGMEM</label>
            <input type="checkbox" id="cbPROGMEM" value="PROGMEM" checked />
        </div>
        <div>
            <div class="tooltip">
                <span class="tooltiptext">These control the signature generated.<br/>Please note, not all of them make sense in all languages!</span>
            </div> 
            <span id="spnSignaturePreview" class="signaturePreview">...</span>
        </div>
    </fieldset>
    <p>
        <input type="button" name="btnConvert" id="btnConvert" value="Convert" />&nbsp;&nbsp;<span id="spnStatus"></span>
    </p>
    <fieldset id="fsResult">
        <legend>Result
            &nbsp;<input type="button" name="btnCopyToClipboard" id="btnCopyToClipboard" value="Copy to clipboard" />
            &nbsp;<a download="download" id="aSaveFile" class="linkButtons"><input type="button" name="btnSaveFile" id="btnSaveFile" value="Save as file" class="" /></a>
            &nbsp;<a download="download" id="aSaveImage" class="fileIsImage linkButtons"><input type="button" name="btnSaveImage" id="btnSaveImage" value="Save image" class="fileIsImage" /></a>
        </legend>
        <textarea id="txtResult" wrap="off"></textarea>
        <div id="divResult"></div>
    </fieldset>
</body>
</html>
<!-- https://www.w3schools.com/css/css_tooltip.asp -->