#pragma once

#include <algorithm>
#include <array>
#include <cstdint>
#include <istream>
#include <ostream>
#include <sstream>
#include <string>

#include "register.h"
#include <magic_enum/magic_enum.hpp>
#include <spdlog/spdlog.h>

namespace x86_64 {

constexpr uint8_t REX_B = 0x01;
constexpr uint8_t REX_X = 0x02;
constexpr uint8_t REX_R = 0x04;
constexpr uint8_t REX_W = 0x08;

/**
 * @brief x86_64 instruction types, including SIMD and system instructions.
 */
enum class InstructionType {
  Unknown,
  // Basic Instructions
  Nop,
  Ret,
  Syscall,
  Sysenter,
  Sysexit,
  Sysret,
  Lidt,
  Lgdt,
  Lldt,
  Ltr,
  Sidt,
  Sgdt,
  Sldt,
  Str,

  // Control Flow
  Jcc,
  Jump,
  Call,
  Loop,
  Loope,
  Loopne,
  Jecxz,
  Jrcxz,

  // Stack Operations
  Push,
  Pop,
  Pusha,
  Popa,
  Pushad,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>fd,
  <PERSON>fd,
  <PERSON><PERSON>f<PERSON>,
  <PERSON>f<PERSON>,
  <PERSON><PERSON>,
  Leave,

  // Data Movement
  Mov,
  Movsx,
  Movzx,
  Movsxd,
  Xchg,
  Bswap,
  Xadd,
  Cmpxchg,
  Cmpxchg8b,
  Cmpxchg16b,

  // Arithmetic
  Add,
  Adc,
  Sub,
  Sbb,
  Mul,
  Imul,
  Div,
  Idiv,
  Inc,
  Dec,
  Neg,
  Cmp,

  // Logical
  And,
  Or,
  Xor,
  Not,
  Test,

  // Bit Operations
  Bt,
  Btc,
  Btr,
  Bts,
  Bsf,
  Bsr,
  Popcnt,
  Lzcnt,
  Tzcnt,

  // Shift and Rotate
  Shl,
  Shr,
  Sar,
  Rol,
  Ror,
  Rcl,
  Rcr,
  Shld,
  Shrd,

  // String Operations
  Movsb,
  Movsw,
  Movsd,
  Movsq,
  Stosb,
  Stosw,
  Stosd,
  Stosq,
  Lodsb,
  Lodsw,
  Lodsd,
  Lodsq,
  Scasb,
  Scasw,
  Scasd,
  Scasq,
  Cmpsb,
  Cmpsw,
  Cmpsd,
  Cmpsq,

  // Conditional Operations
  Cmovcc,
  Setcc,

  // Address Calculation
  Lea,

  // Interrupts and Exceptions
  Int,
  Int3,
  Into,
  Iret,
  Iretd,
  Iretq,
  Bound,

  // Processor Control
  Hlt,
  Nop2,
  Pause,
  Mfence,
  Lfence,
  Sfence,
  Clflush,
  Clflushopt,
  Clwb,
  Prefetch,
  Prefetchw,
  Prefetcht0,
  Prefetcht1,
  Prefetcht2,
  Prefetchnta,

  // Segment Operations
  Lds,
  Les,
  Lfs,
  Lgs,
  Lss,

  // Flag Operations
  Stc,
  Clc,
  Cmc,
  Std,
  Cld,
  Sti,
  Cli,
  Lahf,
  Sahf,

  // I/O Operations
  In,
  Out,
  Ins,
  Outs,

  // x87 FPU Instructions
  Fld,
  Fst,
  Fstp,
  Fild,
  Fist,
  Fistp,
  Fadd,
  Fsub,
  Fmul,
  Fdiv,
  Fabs,
  Fchs,
  Fsqrt,
  Fsin,
  Fcos,
  Fptan,
  Fpatan,
  F2xm1,
  Fyl2x,
  Fyl2xp1,
  Fscale,
  Frndint,
  Fxtract,
  Fprem,
  Fprem1,
  Fcom,
  Fcomp,
  Fcompp,
  Fucom,
  Fucomp,
  Fucompp,
  Ftst,
  Fxam,
  Fldz,
  Fld1,
  Fldpi,
  Fldl2t,
  Fldl2e,
  Fldlg2,
  Fldln2,
  Finit,
  Fninit,
  Fclex,
  Fnclex,
  Fstcw,
  Fnstcw,
  Fldcw,
  Fstenv,
  Fnstenv,
  Fldenv,
  Fsave,
  Fnsave,
  Frstor,
  Ffree,
  Ffreep,
  Fincstp,
  Fdecstp,
  Fnop,
  Fwait,

  // SSE Instructions
  Movaps,
  Movups,
  Movss,
  Movsd_sse,
  Movlps,
  Movhps,
  Movlhps,
  Movhlps,
  Movmskps,
  Movmskpd,
  Addps,
  Addss,
  Addpd,
  Addsd,
  Subps,
  Subss,
  Subpd,
  Subsd,
  Mulps,
  Mulss,
  Mulpd,
  Mulsd,
  Divps,
  Divss,
  Divpd,
  Divsd,
  Sqrtps,
  Sqrtss,
  Sqrtpd,
  Sqrtsd,
  Rsqrtps,
  Rsqrtss,
  Rcpps,
  Rcpss,
  Maxps,
  Maxss,
  Maxpd,
  Maxsd,
  Minps,
  Minss,
  Minpd,
  Minsd,
  Cmpps,
  Cmpss,
  Cmppd,
  Cmpsd_sse,
  Comiss,
  Comisd,
  Ucomiss,
  Ucomisd,
  Andps,
  Andpd,
  Andnps,
  Andnpd,
  Orps,
  Orpd,
  Xorps,
  Xorpd,
  Shufps,
  Shufpd,
  Unpcklps,
  Unpcklpd,
  Unpckhps,
  Unpckhpd,

  // SSE Conversion Instructions
  Cvtps2pd,
  Cvtpd2ps,
  Cvtss2sd,
  Cvtsd2ss,
  Cvtps2dq,
  Cvtpd2dq,
  Cvtdq2ps,
  Cvtdq2pd,
  Cvttps2dq,
  Cvttpd2dq,
  Cvtps2pi,
  Cvtpd2pi,
  Cvtpi2ps,
  Cvtpi2pd,
  Cvttps2pi,
  Cvttpd2pi,
  Cvtsi2ss,
  Cvtsi2sd,
  Cvttss2si,
  Cvttsd2si,
  Cvtss2si,
  Cvtsd2si,

  // SSE2 Instructions
  Movapd,
  Movupd,
  Movlpd,
  Movhpd,
  Movdqa,
  Movdqu,
  Movq,
  Movd,
  Pshufd,
  Pshufhw,
  Pshuflw,
  Punpcklbw,
  Punpcklwd,
  Punpckldq,
  Punpcklqdq,
  Punpckhbw,
  Punpckhwd,
  Punpckhdq,
  Punpckhqdq,
  Packsswb,
  Packssdw,
  Packuswb,
  Paddb,
  Paddw,
  Paddd,
  Paddq,
  Paddsb,
  Paddsw,
  Paddusb,
  Paddusw,
  Psubb,
  Psubw,
  Psubd,
  Psubq,
  Psubsb,
  Psubsw,
  Psubusb,
  Psubusw,
  Pmullw,
  Pmulhw,
  Pmulhuw,
  Pmuludq,
  Pmaddwd,
  Psadbw,
  Pand,
  Pandn,
  Por,
  Pxor,
  Psllw,
  Pslld,
  Psllq,
  Psrlw,
  Psrld,
  Psrlq,
  Psraw,
  Psrad,
  Pcmpeqb,
  Pcmpeqw,
  Pcmpeqd,
  Pcmpgtb,
  Pcmpgtw,
  Pcmpgtd,
  Pmaxub,
  Pmaxsw,
  Pminub,
  Pminsw,
  Pavgb,
  Pavgw,
  Pextrw,
  Pinsrw,
  Pmovmskb,

  // SSE3 Instructions
  Addsubps,
  Addsubpd,
  Haddps,
  Haddpd,
  Hsubps,
  Hsubpd,
  Movshdup,
  Movsldup,
  Movddup,
  Lddqu,
  Monitor,
  Mwait,

  // SSSE3 Instructions
  Pabsb,
  Pabsw,
  Pabsd,
  Psignb,
  Psignw,
  Psignd,
  Pshufb,
  Pmulhrsw,
  Pmaddubsw,
  Phaddw,
  Phaddd,
  Phaddsw,
  Phsubw,
  Phsubd,
  Phsubsw,
  Palignr,

  // SSE4.1 Instructions
  Pblendvb,
  Pblendw,
  Blendps,
  Blendpd,
  Blendvps,
  Blendvpd,
  Ptest,
  Pmovsxbw,
  Pmovsxbd,
  Pmovsxbq,
  Pmovsxwd,
  Pmovsxwq,
  Pmovsxdq,
  Pmovzxbw,
  Pmovzxbd,
  Pmovzxbq,
  Pmovzxwd,
  Pmovzxwq,
  Pmovzxdq,
  Pminsb,
  Pminsd,
  Pminuw,
  Pminud,
  Pmaxsb,
  Pmaxsd,
  Pmaxuw,
  Pmaxud,
  Roundps,
  Roundpd,
  Roundss,
  Roundsd,
  Insertps,
  Pinsrb,
  Pinsrd,
  Pinsrq,
  Pextrb,
  Pextrd,
  Pextrq,
  Pmuldq,
  Pcmpeqq,
  Packusdw,
  Movntdqa,
  Phminposuw,
  Dpps,
  Dppd,
  Mpsadbw,
  Pcmpestri,
  Pcmpestrm,
  Pcmpistri,
  Pcmpistrm,

  // SSE4.2 Instructions
  Pcmpgtq,
  Crc32,

  // AES Instructions
  Aesenc,
  Aesenclast,
  Aesdec,
  Aesdeclast,
  Aeskeygenassist,
  Aesimc,

  // PCLMULQDQ Instructions
  Pclmulqdq,

  // AVX Instructions
  Vaddps,
  Vaddpd,
  Vaddss,
  Vaddsd,
  Vsubps,
  Vsubpd,
  Vsubss,
  Vsubsd,
  Vmulps,
  Vmulpd,
  Vmulss,
  Vmulsd,
  Vdivps,
  Vdivpd,
  Vdivss,
  Vdivsd,
  Vsqrtps,
  Vsqrtpd,
  Vsqrtss,
  Vsqrtsd,
  Vrsqrtps,
  Vrsqrtss,
  Vrcpps,
  Vrcpss,
  Vmaxps,
  Vmaxpd,
  Vmaxss,
  Vmaxsd,
  Vminps,
  Vminpd,
  Vminss,
  Vminsd,
  Vandps,
  Vandpd,
  Vandnps,
  Vandnpd,
  Vorps,
  Vorpd,
  Vxorps,
  Vxorpd,
  Vblendps,
  Vblendpd,
  Vblendvps,
  Vblendvpd,
  Vdpps,
  Vdppd,
  Vhaddps,
  Vhaddpd,
  Vhsubps,
  Vhsubpd,
  Vaddsubps,
  Vaddsubpd,
  Vcmpps,
  Vcmppd,
  Vcmpss,
  Vcmpsd,
  Vcomiss,
  Vcomisd,
  Vucomiss,
  Vucomisd,
  Vmovaps,
  Vmovapd,
  Vmovups,
  Vmovupd,
  Vmovss,
  Vmovsd,
  Vmovlps,
  Vmovlpd,
  Vmovhps,
  Vmovhpd,
  Vmovlhps,
  Vmovhlps,
  Vmovmskps,
  Vmovmskpd,
  Vmovshdup,
  Vmovsldup,
  Vmovddup,
  Vshufps,
  Vshufpd,
  Vunpcklps,
  Vunpcklpd,
  Vunpckhps,
  Vunpckhpd,
  Vextractps,
  Vinsertps,
  Vroundps,
  Vroundpd,
  Vroundss,
  Vroundsd,
  Vtestps,
  Vtestpd,
  Vzeroupper,
  Vzeroall,

  // AVX Integer Instructions
  Vmovd,
  Vmovq,
  Vmovdqa,
  Vmovdqu,
  Vlddqu,
  Vmovntdq,
  Vmovntdqa,
  Vmaskmovps,
  Vmaskmovpd,
  Vmaskmovdqu,
  Vpshufb,
  Vpshufd,
  Vpshufhw,
  Vpshuflw,
  Vpunpcklbw,
  Vpunpcklwd,
  Vpunpckldq,
  Vpunpcklqdq,
  Vpunpckhbw,
  Vpunpckhwd,
  Vpunpckhdq,
  Vpunpckhqdq,
  Vpacksswb,
  Vpackssdw,
  Vpackuswb,
  Vpackusdw,
  Vpaddb,
  Vpaddw,
  Vpaddd,
  Vpaddq,
  Vpaddsb,
  Vpaddsw,
  Vpaddusb,
  Vpaddusw,
  Vpsubb,
  Vpsubw,
  Vpsubd,
  Vpsubq,
  Vpsubsb,
  Vpsubsw,
  Vpsubusb,
  Vpsubusw,
  Vpmullw,
  Vpmulhw,
  Vpmulhuw,
  Vpmulld,
  Vpmuludq,
  Vpmuldq,
  Vpmaddwd,
  Vpmaddubsw,
  Vpsadbw,
  Vpand,
  Vpandn,
  Vpor,
  Vpxor,
  Vpsllw,
  Vpslld,
  Vpsllq,
  Vpsrlw,
  Vpsrld,
  Vpsrlq,
  Vpsraw,
  Vpsrad,
  Vpsllvd,
  Vpsllvq,
  Vpsrlvd,
  Vpsrlvq,
  Vpsravd,
  Vpcmpeqb,
  Vpcmpeqw,
  Vpcmpeqd,
  Vpcmpeqq,
  Vpcmpgtb,
  Vpcmpgtw,
  Vpcmpgtd,
  Vpcmpgtq,
  Vpmaxub,
  Vpmaxsb,
  Vpmaxuw,
  Vpmaxsw,
  Vpmaxud,
  Vpmaxsd,
  Vpminub,
  Vpminsb,
  Vpminuw,
  Vpminsw,
  Vpminud,
  Vpminsd,
  Vpavgb,
  Vpavgw,
  Vpblendvb,
  Vpblendw,
  Vphaddw,
  Vphaddd,
  Vphaddsw,
  Vphsubw,
  Vphsubd,
  Vphsubsw,
  Vpmulhrsw,
  Vpsignb,
  Vpsignw,
  Vpsignd,
  Vpabsb,
  Vpabsw,
  Vpabsd,
  Vpalignr,
  Vpextrb,
  Vpextrw,
  Vpextrd,
  Vpextrq,
  Vpinsrb,
  Vpinsrw,
  Vpinsrd,
  Vpinsrq,
  Vpmovmskb,
  Vptest,
  Vbroadcastss,
  Vbroadcastsd,
  Vbroadcastf128,
  Vextractf128,
  Vinsertf128,
  Vperm2f128,

  // BMI1 Instructions
  Andn,
  Bextr,
  Blsi,
  Blsmsk,
  Blsr,

  // BMI2 Instructions
  Bzhi,
  Mulx,
  Pdep,
  Pext,
  Rorx,
  Sarx,
  Shlx,
  Shrx,

  // FMA Instructions
  Vfmadd132ps,
  Vfmadd132pd,
  Vfmadd132ss,
  Vfmadd132sd,
  Vfmadd213ps,
  Vfmadd213pd,
  Vfmadd213ss,
  Vfmadd213sd,
  Vfmadd231ps,
  Vfmadd231pd,
  Vfmadd231ss,
  Vfmadd231sd,
  Vfmaddsub132ps,
  Vfmaddsub132pd,
  Vfmaddsub213ps,
  Vfmaddsub213pd,
  Vfmaddsub231ps,
  Vfmaddsub231pd,
  Vfmsubadd132ps,
  Vfmsubadd132pd,
  Vfmsubadd213ps,
  Vfmsubadd213pd,
  Vfmsubadd231ps,
  Vfmsubadd231pd,
  Vfmsub132ps,
  Vfmsub132pd,
  Vfmsub132ss,
  Vfmsub132sd,
  Vfmsub213ps,
  Vfmsub213pd,
  Vfmsub213ss,
  Vfmsub213sd,
  Vfmsub231ps,
  Vfmsub231pd,
  Vfmsub231ss,
  Vfmsub231sd,
  Vfnmadd132ps,
  Vfnmadd132pd,
  Vfnmadd132ss,
  Vfnmadd132sd,
  Vfnmadd213ps,
  Vfnmadd213pd,
  Vfnmadd213ss,
  Vfnmadd213sd,
  Vfnmadd231ps,
  Vfnmadd231pd,
  Vfnmadd231ss,
  Vfnmadd231sd,
  Vfnmsub132ps,
  Vfnmsub132pd,
  Vfnmsub132ss,
  Vfnmsub132sd,
  Vfnmsub213ps,
  Vfnmsub213pd,
  Vfnmsub213ss,
  Vfnmsub213sd,
  Vfnmsub231ps,
  Vfnmsub231pd,
  Vfnmsub231ss,
  Vfnmsub231sd,

  // AVX2 Instructions
  Vbroadcasti128,
  Vbroadcastb,
  Vbroadcastw,
  Vbroadcastd,
  Vbroadcastq,
  Vextracti128,
  Vinserti128,
  Vperm2i128,
  Vpermps,
  Vpermd,
  Vpermq,
  Vpermpd,
  Vpblendd,
  Vpsllvw,
  Vpsrlvw,
  Vpsravw,
  Vpgatherdd,
  Vpgatherqd,
  Vpgatherdq,
  Vpgatherqq,
  Vgatherdps,
  Vgatherqps,
  Vgatherdpd,
  Vgatherqpd,

  // AVX-512 Foundation Instructions (subset)
  Vaddps_512,
  Vaddpd_512,
  Vsubps_512,
  Vsubpd_512,
  Vmulps_512,
  Vmulpd_512,
  Vdivps_512,
  Vdivpd_512,
  Vsqrtps_512,
  Vsqrtpd_512,
  Vfmadd132ps_512,
  Vfmadd132pd_512,
  Vfmadd213ps_512,
  Vfmadd213pd_512,
  Vfmadd231ps_512,
  Vfmadd231pd_512,
  Vmovaps_512,
  Vmovapd_512,
  Vmovups_512,
  Vmovupd_512,
  Vbroadcastf32x4,
  Vbroadcastf64x4,
  Vbroadcasti32x4,
  Vbroadcasti64x4,
  Vextractf32x4,
  Vextractf64x4,
  Vextracti32x4,
  Vextracti64x4,
  Vinsertf32x4,
  Vinsertf64x4,
  Vinserti32x4,
  Vinserti64x4,
  Vshuff32x4,
  Vshuff64x2,
  Vshufi32x4,
  Vshufi64x2,
  Vpermps_512,
  Vpermpd_512,
  Vpermq_512,
  Vpermd_512,
  Vpermw,
  Vpermt2ps,
  Vpermt2pd,
  Vpermt2d,
  Vpermt2q,
  Vpermt2w,
  Vpermt2b,
  Vpermi2ps,
  Vpermi2pd,
  Vpermi2d,
  Vpermi2q,
  Vpermi2w,
  Vpermi2b,
  Vcompressps,
  Vcompresspd,
  Vcompressd,
  Vcompressq,
  Vexpandps,
  Vexpandpd,
  Vexpandd,
  Vexpandq,
  Vgatherdps_512,
  Vgatherqps_512,
  Vgatherdpd_512,
  Vgatherqpd_512,
  Vpgatherdd_512,
  Vpgatherqd_512,
  Vpgatherdq_512,
  Vpgatherqq_512,
  Vscatterdps,
  Vscatterqps,
  Vscatterdpd,
  Vscatterqpd,
  Vpscatterdd,
  Vpscatterqd,
  Vpscatterdq,
  Vpscatterqq,
  Vblendmps,
  Vblendmpd,
  Vblendmd,
  Vblendmq,
  Vblendmw,
  Vblendmb,
  Vpcmpb,
  Vpcmpw,
  Vpcmpd,
  Vpcmpq,
  Vpcmpub,
  Vpcmpuw,
  Vpcmpud,
  Vpcmpuq,
  Vptestmb,
  Vptestmw,
  Vptestmd,
  Vptestmq,
  Vptestnmb,
  Vptestnmw,
  Vptestnmd,
  Vptestnmq,
  Vpmovb2m,
  Vpmovw2m,
  Vpmovd2m,
  Vpmovq2m,
  Vpmovm2b,
  Vpmovm2w,
  Vpmovm2d,
  Vpmovm2q,
  Vpmovdb,
  Vpmovdw,
  Vpmovqb,
  Vpmovqw,
  Vpmovqd,
  Vpmovwb,
  Vpmovsdb,
  Vpmovsdw,
  Vpmovsqb,
  Vpmovsqw,
  Vpmovsqd,
  Vpmovswb,
  Vpmovusdb,
  Vpmovusdw,
  Vpmovusqb,
  Vpmovusqw,
  Vpmovusqd,
  Vpmovuswb,
  Vcvtps2ph,
  Vcvtph2ps,
  Vfpclassps,
  Vfpclasspd,
  Vfpclassss,
  Vfpclasssd,
  Vgetexpps,
  Vgetexppd,
  Vgetexpss,
  Vgetexpsd,
  Vgetmantps,
  Vgetmantpd,
  Vgetmantss,
  Vgetmantsd,
  Vfixupimmps,
  Vfixupimmpd,
  Vfixupimmss,
  Vfixupimmsd,
  Vreduceps,
  Vreducepd,
  Vreducess,
  Vreducesd,
  Vrndscaleps,
  Vrndscalepd,
  Vrndscaless,
  Vrndscalesd,
  Vscalefps,
  Vscalefpd,
  Vscalefss,
  Vscalefsd,

  // System Instructions
  Cpuid,
  Rdtsc,
  Rdtscp,
  Rdmsr,
  Wrmsr,
  Rdpmc,
  Invd,
  Wbinvd,
  Invlpg,
  Invpcid,
  Lmsw,
  Smsw,
  Clts,
  Arpl,
  Lar,
  Lsl,
  Verr,
  Verw,
  Jmp_far,
  Call_far,
  Ret_far,
  Iret_far,
  Loadall,

  // Memory Protection Instructions
  Xsave,
  Xrstor,
  Xsaveopt,
  Xsavec,
  Xsaves,
  Xrstors,
  Xgetbv,
  Xsetbv,

  // Virtualization Instructions
  Vmcall,
  Vmlaunch,
  Vmresume,
  Vmxoff,
  Vmxon,
  Vmclear,
  Vmptrld,
  Vmptrst,
  Vmread,
  Vmwrite,
  Invept,
  Invvpid,

  // Security Instructions
  Rdrand,
  Rdseed,

  // Transactional Memory Instructions
  Xbegin,
  Xend,
  Xabort,
  Xtest,

  // Control Flow Enforcement Technology
  Endbr32,
  Endbr64,

  // Memory Protection Keys
  Rdpkru,
  Wrpkru,

  // User Interrupt Instructions
  Uiret,
  Testui,
  Clui,
  Stui,
  Senduipi,

  // Advanced Matrix Extensions (AMX)
  Ldtilecfg,
  Sttilecfg,
  Tileloadd,
  Tileloaddt1,
  Tilestored,
  Tilerelease,
  Tilezero,
  Tdpbf16ps,
  Tdpbssd,
  Tdpbsud,
  Tdpbusd,
  Tdpbuud,

  // Additional Missing Instructions
  Ud0,
  Ud1,
  Ud2,
  Getsec,
  Rsm,
  Fxsave,
  Fxrstor,
  Ldmxcsr,
  Stmxcsr,
  Maskmovq,
  Maskmovdqu,
  Movnti,
  Movntps,
  Movntpd,
  Movntq,
  Movntdq,
  Pcommit,
  Clzero,
  Ptwrite,

  // CET Instructions
  Incsspd,
  Incsspq,
  Rdsspd,
  Rdsspq,
  Saveprevssp,
  Rstorssp,
  Wrssd,
  Wrssq,
  Wrussd,
  Wrussq,
  Setssbsy,
  Clrssbsy,

  // Key Locker Instructions
  Loadiwkey,
  Encodekey128,
  Encodekey256,
  Aesenc128kl,
  Aesdec128kl,
  Aesenc256kl,
  Aesdec256kl,
  Aesencwide128kl,
  Aesdecwide128kl,
  Aesencwide256kl,
  Aesdecwide256kl,

  // Additional FPU Instructions (only new ones not already defined)
  Fiadd,
  Fimul,
  Ficom,
  Ficomp,
  Fisub,
  Fisubr,
  Fidiv,
  Fidivr,
  Fisttp,
  Fbld,
  Fbstp,
  Fsubr,
  Fdivr,
  Fnstsw,

  // Additional System Instructions (only new ones not already defined)
  Swapgs,

  // Additional Missing Instructions (only new ones not already defined)
  Pshufw,

  // Missing SIMD Instructions
  Pmulld,
  Extractps,

  // Missing VEX Instructions
  Vcvtpi2ps,
  Vmovntps,
  Vcvttps2pi,
  Vcvtps2pi,
  Vcvtps2pd,
  Vcvtdq2ps,
  Vcvttpd2dq,
  Vpermilps,
  Vpermilpd
};

/**
 * @brief Decoded x86_64 instruction structure.
 */
struct DecodedInstruction {
  struct VEX {
    uint8_t raw[3] = {0}; // Store raw VEX bytes for 2-byte or 3-byte VEX

    // Accessors for VEX fields
    uint8_t m() const { return (raw[1] >> 6) & 0x3; } // Instruction set
    uint8_t L() const { return (raw[1] >> 5) & 1; }   // Vector length
    uint8_t pp() const { return raw[1] & 0x3; }       // Prefix
    uint8_t vvvv() const {
      return (raw[2] >> 3) & 0xF;
    } // Third operand register
    uint8_t W() const { return (raw[2] >> 7) & 1; } // Wide operand

    void set_3byte(uint8_t m_val, uint8_t L_val, uint8_t pp_val,
                   uint8_t vvvv_val, uint8_t W_val) {
      raw[0] = 0xC4;
      raw[1] = ((m_val & 0x3) << 6) | ((L_val & 1) << 5) | (pp_val & 0x3);
      raw[2] = ((W_val & 1) << 7) | ((vvvv_val & 0xF) << 3);
    }

    void set_2byte(uint8_t L_val, uint8_t pp_val, uint8_t vvvv_val) {
      raw[0] = 0xC5;
      raw[1] = ((L_val & 1) << 5) | (pp_val & 0x3);
      raw[2] = ((vvvv_val & 0xF) << 3);
    }
  };

  struct Operand {
    enum class Type {
      NONE,
      REGISTER,
      MEMORY,
      IMMEDIATE,
      XMM,
      YMM,
      ZMM,
      MMX,
      ST,
      SEGMENT,
      CONTROL,
      DEBUG,
      MASK
    };
    Type type = Type::NONE;
    uint16_t size = 64; // Size in bits
    Register reg = Register::NONE;

    struct Memory {
      Register base = Register::NONE;
      Register index = Register::NONE;
      uint8_t scale = 1;
      int32_t displacement = 0;
    } memory;

    uint64_t immediate = 0;
  };

  Operand operands[4];
  uint8_t opcode = 0;
  uint8_t prefixes[4] = {0};
  uint8_t rex = 0;
  VEX vex = {};
  uint8_t length = 0;
  uint8_t operandCount = 0;
  uint8_t conditionCode = 0; // For Jcc and Cmovcc
  InstructionType instType = InstructionType::Unknown;
  bool repPrefix = false;
  bool repePrefix = false;
  bool repnePrefix = false;
  bool operandSizeOverride = false;
  bool addressSizeOverride = false;
  bool isVex = false;

  DecodedInstruction() = default;

  void reset() {
    opcode = 0;
    std::fill(std::begin(prefixes), std::end(prefixes), 0);
    rex = 0;
    vex = {};
    length = 0;
    operandCount = 0;
    conditionCode = 0;
    instType = InstructionType::Unknown;
    repPrefix = false;
    repePrefix = false;
    repnePrefix = false;
    operandSizeOverride = false;
    addressSizeOverride = false;
    isVex = false;
    std::fill(std::begin(operands), std::end(operands), Operand{});
  }

  bool HasRexW() const { return (rex & REX_W) != 0; }
  bool validate() const {
    if (operandCount > 4) {
      spdlog::error("Invalid operand count: {}",
                    static_cast<int>(operandCount));
      return false;
    }
    for (uint8_t i = 0; i < operandCount; ++i) {
      const auto &op = operands[i];
      if (op.type == Operand::Type::REGISTER || op.type == Operand::Type::XMM) {
        if (!IsValidRegister(op.reg)) {
          spdlog::error("Invalid register in operand {}: {}",
                        static_cast<int>(i), static_cast<int>(op.reg));
          return false;
        }
      }
      if (op.type == Operand::Type::MEMORY) {
        if (op.memory.base != Register::NONE &&
            !IsValidRegister(op.memory.base)) {
          spdlog::error("Invalid base register in operand {}: {}",
                        static_cast<int>(i), static_cast<int>(op.memory.base));
          return false;
        }
        if (op.memory.index != Register::NONE &&
            !IsValidRegister(op.memory.index)) {
          spdlog::error("Invalid index register in operand {}: {}",
                        static_cast<int>(i), static_cast<int>(op.memory.index));
          return false;
        }
        if (op.memory.scale != 1 && op.memory.scale != 2 &&
            op.memory.scale != 4 && op.memory.scale != 8) {
          spdlog::error("Invalid scale in operand {}: {}", static_cast<int>(i),
                        op.memory.scale);
          return false;
        }
      }
    }
    return true;
  }

  void Save(std::ostream &out) const {
    uint32_t version = 1;
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));
    out.write(reinterpret_cast<const char *>(&opcode), sizeof(opcode));
    out.write(reinterpret_cast<const char *>(prefixes), sizeof(prefixes));
    out.write(reinterpret_cast<const char *>(&rex), sizeof(rex));
    out.write(reinterpret_cast<const char *>(vex.raw), sizeof(vex.raw));
    out.write(reinterpret_cast<const char *>(&length), sizeof(length));
    out.write(reinterpret_cast<const char *>(&operandCount),
              sizeof(operandCount));
    out.write(reinterpret_cast<const char *>(&conditionCode),
              sizeof(conditionCode));
    out.write(reinterpret_cast<const char *>(&instType), sizeof(instType));
    out.write(reinterpret_cast<const char *>(&repPrefix), sizeof(repPrefix));
    out.write(reinterpret_cast<const char *>(&repePrefix), sizeof(repePrefix));
    out.write(reinterpret_cast<const char *>(&repnePrefix),
              sizeof(repnePrefix));
    out.write(reinterpret_cast<const char *>(&operandSizeOverride),
              sizeof(operandSizeOverride));
    out.write(reinterpret_cast<const char *>(&addressSizeOverride),
              sizeof(addressSizeOverride));
    out.write(reinterpret_cast<const char *>(&isVex), sizeof(isVex));
    for (const auto &op : operands) {
      out.write(reinterpret_cast<const char *>(&op.type), sizeof(op.type));
      out.write(reinterpret_cast<const char *>(&op.size), sizeof(op.size));
      out.write(reinterpret_cast<const char *>(&op.reg), sizeof(op.reg));
      out.write(reinterpret_cast<const char *>(&op.memory), sizeof(op.memory));
      out.write(reinterpret_cast<const char *>(&op.immediate),
                sizeof(op.immediate));
    }
  }

  void Load(std::istream &in) {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      spdlog::error("Unsupported DecodedInstruction version: {}", version);
      throw std::runtime_error("Invalid DecodedInstruction version");
    }
    in.read(reinterpret_cast<char *>(&opcode), sizeof(opcode));
    in.read(reinterpret_cast<char *>(prefixes), sizeof(prefixes));
    in.read(reinterpret_cast<char *>(&rex), sizeof(rex));
    in.read(reinterpret_cast<char *>(vex.raw), sizeof(vex.raw));
    in.read(reinterpret_cast<char *>(&length), sizeof(length));
    in.read(reinterpret_cast<char *>(&operandCount), sizeof(operandCount));
    in.read(reinterpret_cast<char *>(&conditionCode), sizeof(conditionCode));
    in.read(reinterpret_cast<char *>(&instType), sizeof(instType));
    in.read(reinterpret_cast<char *>(&repPrefix), sizeof(repPrefix));
    in.read(reinterpret_cast<char *>(&repePrefix), sizeof(repePrefix));
    in.read(reinterpret_cast<char *>(&repnePrefix), sizeof(repnePrefix));
    in.read(reinterpret_cast<char *>(&operandSizeOverride),
            sizeof(operandSizeOverride));
    in.read(reinterpret_cast<char *>(&addressSizeOverride),
            sizeof(addressSizeOverride));
    in.read(reinterpret_cast<char *>(&isVex), sizeof(isVex));
    for (auto &op : operands) {
      in.read(reinterpret_cast<char *>(&op.type), sizeof(op.type));
      in.read(reinterpret_cast<char *>(&op.size), sizeof(op.size));
      in.read(reinterpret_cast<char *>(&op.reg), sizeof(op.reg));
      in.read(reinterpret_cast<char *>(&op.memory), sizeof(op.memory));
      in.read(reinterpret_cast<char *>(&op.immediate), sizeof(op.immediate));
    }
    if (!validate()) {
      throw std::runtime_error("Invalid DecodedInstruction loaded");
    }
  }

  std::string to_string() const {
    std::stringstream ss;
    ss << "Instruction: " << magic_enum::enum_name(instType) << ", Opcode: 0x"
       << std::hex << static_cast<int>(opcode)
       << ", Length: " << static_cast<int>(length)
       << ", Operands: " << static_cast<int>(operandCount);
    for (uint8_t i = 0; i < operandCount; ++i) {
      ss << "\n  Operand " << i << ": ";
      switch (operands[i].type) {
      case Operand::Type::REGISTER:
        ss << "Register " << magic_enum::enum_name(operands[i].reg)
           << ", Size: " << operands[i].size;
        break;
      case Operand::Type::MEMORY:
        ss << "Memory [Base: " << magic_enum::enum_name(operands[i].memory.base)
           << ", Index: " << magic_enum::enum_name(operands[i].memory.index)
           << ", Scale: " << static_cast<int>(operands[i].memory.scale)
           << ", Disp: 0x" << std::hex << operands[i].memory.displacement
           << "], Size: " << operands[i].size;
        break;
      case Operand::Type::IMMEDIATE:
        ss << "Immediate 0x" << std::hex << operands[i].immediate
           << ", Size: " << operands[i].size;
        break;
      case Operand::Type::XMM:
        ss << "XMM " << magic_enum::enum_name(operands[i].reg)
           << ", Size: " << operands[i].size;
        break;
      default:
        ss << "None";
      }
    }
    if (rex != 0)
      ss << "\nREX: 0x" << std::hex << static_cast<int>(rex);
    if (isVex) {
      ss << "\nVEX: m=0x" << static_cast<int>(vex.m())
         << ", L=" << static_cast<int>(vex.L()) << ", pp=0x"
         << static_cast<int>(vex.pp()) << ", vvvv=0x"
         << static_cast<int>(vex.vvvv()) << ", W=" << static_cast<int>(vex.W());
    }
    return ss.str();
  }
};

} // namespace x86_64