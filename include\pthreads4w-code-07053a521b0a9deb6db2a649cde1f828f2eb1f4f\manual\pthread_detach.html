<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>PTHREAD_DETACH(3) manual page</TITLE>
</HEAD>
<BODY LANG="en-GB" BGCOLOR="#ffffff" DIR="LTR">
<H4>POSIX Threads for Windows – REFERENCE - <A HREF="https://sourceforge.net/projects/pthreads4w/">Pthreads4W</A></H4>
<P><A HREF="index.html">Reference Index</A></P>
<P><A HREF="#toc">Table of Contents</A></P>
<H2><A HREF="#toc0" NAME="sect0">Name</A></H2>
<P>pthread_detach - put a running thread in the detached state 
</P>
<H2><A HREF="#toc1" NAME="sect1">Synopsis</A></H2>
<P><B>#include &lt;pthread.h&gt;</B> 
</P>
<P><B>int pthread_detach(pthread_t </B><I>th</I><B>);</B> 
</P>
<H2><A HREF="#toc2" NAME="sect2">Description</A></H2>
<P><B>pthread_detach</B> puts the thread <I>th</I> in the detached
state. This guarantees that the resources consumed by <I>th</I> will
be freed immediately when <I>th</I> terminates. However, this
prevents other threads from synchronizing on the termination of <I>th</I>
using <B>pthread_join</B>. If, when <B>pthread_detach</B> is called,
<I>th</I> has already terminated, all of <I>th</I>'s remaining
resources will be freed.</P>
<P>A thread can be created initially in the detached state, using the
<B>detachstate</B> attribute to <A HREF="pthread_create.html"><B>pthread_create</B>(3)</A>
. In contrast, <B>pthread_detach</B> applies to threads created in
the joinable state, and which need to be put in the detached state
later. 
</P>
<P>After <B>pthread_detach</B> completes, subsequent attempts to
perform <B>pthread_join</B> on <I>th</I> will fail. If another thread
is already joining the thread <I>th</I> at the time <B>pthread_detach</B>
is called, <I>th</I> will be detached and <B>pthread_join</B> will
eventually return when <I>th</I> terminates but may not return with
<I>th</I>'s correct return code. 
</P>
<H2><A HREF="#toc3" NAME="sect3">Return Value</A></H2>
<P>On success, 0 is returned. On error, a non-zero error code is
returned. 
</P>
<H2><A HREF="#toc4" NAME="sect4">Errors</A></H2>
<DL>
	<DT><B>ESRCH</B> 
	</DT><DD>
	No thread could be found corresponding to that specified by <I>th</I>
		</DD><DT>
	<B>EINVAL</B> 
	</DT><DD STYLE="margin-bottom: 0.5cm">
	the thread <I>th</I> is already in the detached state 
	</DD></DL>
<H2>
<A HREF="#toc5" NAME="sect5">Author</A></H2>
<P>Xavier Leroy &lt;<EMAIL>&gt; 
</P>
<P>Modified by Ross Johnson for use with PThreads4W.</P>
<H2><A HREF="#toc6" NAME="sect6">See Also</A></H2>
<P><A HREF="pthread_create.html"><B>pthread_create</B>(3)</A> ,
<A HREF="pthread_join.html"><B>pthread_join</B>(3)</A> ,
<A HREF="pthread_attr_init.html"><B>pthread_attr_setdetachstate</B>(3)</A>
</P>
<HR>
<P><A NAME="toc"></A><B>Table of Contents</B></P>
<UL>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect0" NAME="toc0">Name</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect1" NAME="toc1">Synopsis</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect2" NAME="toc2">Description</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect3" NAME="toc3">Return
	Value</A> 
	</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect4" NAME="toc4">Errors</A>
		</P>
	<LI><P STYLE="margin-bottom: 0cm"><A HREF="#sect5" NAME="toc5">Author</A>
		</P>
	<LI><P><A HREF="#sect6" NAME="toc6">See Also</A> 
	</P>
</UL>
</BODY>
</HTML>