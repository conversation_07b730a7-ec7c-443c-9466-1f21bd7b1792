// Copyright 2025 <Copyright Owner>

#pragma once

/**
 * @file lock_ordering.h
 * @brief Defines strict lock ordering to prevent deadlocks in the PS4 emulator
 *
 * CRITICAL: All code must follow this lock ordering to prevent deadlocks.
 * Locks must be acquired in the order specified below and released in reverse
 * order.
 *
 * Lock Hierarchy (acquire in this order):
 * 1. PS4Emulator::m_emulatorMutex (Global emulator state)
 * 2. IOManager mutexes (m_deviceMutex, m_pitMutex, m_eventMutex, m_irqMutex)
 * 3. Memory system mutexes (PS4MMU::m_mutex, Cache::m_mutex)
 * 4. CPU mutexes (X86_64CPU::mutex)
 * 5. InterruptHandler::m_mutex (recursive)
 * 6. JIT compiler mutexes (X86_64JITCompiler::m_cacheMutex)
 * 7. Component-specific mutexes (GPU, Audio, etc.)
 *
 * DEADLOCK PREVENTION RULES:
 *
 * 1. NEVER acquire locks in reverse order
 * 2. NEVER hold a higher-level lock when acquiring a lower-level lock
 * 3. Release locks before calling external code or callbacks when possible
 * 4. Use RAII lock guards to ensure proper cleanup
 * 5. Minimize lock scope - hold locks for the shortest time possible
 * 6. Prefer read locks (std::shared_lock) over write locks when possible
 * 7. Use recursive mutexes only when absolutely necessary (InterruptHandler)
 *
 * SPECIFIC DEADLOCK SCENARIOS TO AVOID:
 *
 * 1. CPU -> InterruptHandler deadlock:
 *    - NEVER call TriggerInterrupt while holding CPU mutex
 *    - InterruptHandler uses recursive mutex to handle re-entrant calls
 *
 * 2. IOManager -> CPU deadlock:
 *    - NEVER call TriggerInterrupt while holding IOManager mutexes
 *    - Update IRQ state first, then trigger interrupt without holding mutex
 *
 * 3. PS4Emulator -> Component deadlock:
 *    - NEVER hold emulator mutex when calling component methods
 *    - Minimize emulator mutex scope to state updates only
 *
 * 4. Memory allocation deadlock:
 *    - ElfLoader -> OrbisOS -> PS4MMU chain is safe (consistent order)
 *    - NEVER call memory allocation while holding component mutexes
 *
 * THREAD SANITIZER SUPPORT:
 *
 * Compile with -fsanitize=thread to detect race conditions and deadlocks:
 * - GCC: g++ -fsanitize=thread -g -O1
 * - Clang: clang++ -fsanitize=thread -g -O1
 * - MSVC: Use /fsanitize=thread (Visual Studio 2022+)
 */

#include <mutex>
#include <shared_mutex>
#include <vector>

namespace ps4 {

/**
 * @brief Lock ordering levels for deadlock prevention
 */
enum class LockLevel : int {
  EMULATOR = 1000,      ///< PS4Emulator global mutex
  IO_MANAGER = 2000,    ///< IOManager mutexes
  MEMORY_SYSTEM = 3000, ///< Memory and MMU mutexes
  CPU = 4000,           ///< CPU mutexes
  INTERRUPT = 5000,     ///< InterruptHandler mutex
  JIT = 6000,           ///< JIT compiler mutexes
  COMPONENTS = 7000     ///< Component-specific mutexes
};

/**
 * @brief Debug lock ordering checker (enabled in debug builds)
 */
class LockOrderChecker {
public:
  static void AcquireLock(LockLevel level, const char *name);
  static void ReleaseLock(LockLevel level, const char *name);
  static void CheckOrder(LockLevel level, const char *name);

private:
  static thread_local std::vector<std::pair<LockLevel, const char *>>
      s_heldLocks;
};

/**
 * @brief RAII lock guard with ordering validation
 */
template <typename MutexType> class OrderedLockGuard {
public:
  OrderedLockGuard(MutexType &mutex, LockLevel level, const char *name)
      : m_lock(mutex), m_level(level), m_name(name) {
#ifdef _DEBUG
    LockOrderChecker::CheckOrder(level, name);
    LockOrderChecker::AcquireLock(level, name);
#endif
  }

  ~OrderedLockGuard() {
#ifdef _DEBUG
    LockOrderChecker::ReleaseLock(m_level, m_name);
#endif
  }

  // Non-copyable, non-movable
  OrderedLockGuard(const OrderedLockGuard &) = delete;
  OrderedLockGuard &operator=(const OrderedLockGuard &) = delete;
  OrderedLockGuard(OrderedLockGuard &&) = delete;
  OrderedLockGuard &operator=(OrderedLockGuard &&) = delete;

private:
  std::unique_lock<MutexType> m_lock;
  LockLevel m_level;
  const char *m_name;
};

/**
 * @brief Convenience macros for ordered locking
 */
#define ORDERED_LOCK(mutex, level, name)                                       \
  ps4::OrderedLockGuard<decltype(mutex)> lock_##__LINE__(mutex, level, name)

#define EMULATOR_LOCK(mutex)                                                   \
  ORDERED_LOCK(mutex, ps4::LockLevel::EMULATOR, "EmulatorMutex")

#define IO_LOCK(mutex, name)                                                   \
  ORDERED_LOCK(mutex, ps4::LockLevel::IO_MANAGER, name)

#define MEMORY_LOCK(mutex, name)                                               \
  ORDERED_LOCK(mutex, ps4::LockLevel::MEMORY_SYSTEM, name)

#define CPU_LOCK(mutex) ORDERED_LOCK(mutex, ps4::LockLevel::CPU, "CPUMutex")

#define INTERRUPT_LOCK(mutex)                                                  \
  ORDERED_LOCK(mutex, ps4::LockLevel::INTERRUPT, "InterruptMutex")

#define JIT_LOCK(mutex) ORDERED_LOCK(mutex, ps4::LockLevel::JIT, "JITMutex")

#define COMPONENT_LOCK(mutex, name)                                            \
  ORDERED_LOCK(mutex, ps4::LockLevel::COMPONENTS, name)

} // namespace ps4
