#include "gnm_shader_translator.h"
#include <chrono>
#include <iomanip>
#include <shared_mutex>
#include <spdlog/spdlog.h>
#include <sstream>
#include <stdexcept>
#include <string>
#include <vector>

namespace ps4 {

GNMShaderTranslator::GNMShaderTranslator() : m_stats() {
  spdlog::info("GNMShaderTranslator constructed");
}

GNMShaderTranslator::~GNMShaderTranslator() {
  Shutdown();
  spdlog::info("GNMShaderTranslator destroyed");
}

bool GNMShaderTranslator::Initialize() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    InitializeOpcodeTables();
    m_stats = ShaderTranslatorStats();
    m_shaderCache.clear();
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    m_stats.cacheHits++;
    spdlog::info("GNMShaderTranslator initialized, latency={}us", latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GNMShaderTranslator initialization failed: {}", e.what());
    throw ShaderTranslatorException("Initialization failed: " +
                                    std::string(e.what()));
  }
}

void GNMShaderTranslator::Shutdown() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    m_scalarOpcodes.clear();
    m_vectorOpcodes.clear();
    m_memoryOpcodes.clear();
    m_flowControlOpcodes.clear();
    m_shaderCache.clear();
    m_stats = ShaderTranslatorStats();
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    m_stats.cacheHits++;
    spdlog::info("GNMShaderTranslator shutdown, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GNMShaderTranslator shutdown failed: {}", e.what());
  }
}

std::string
GNMShaderTranslator::Disassemble(const std::vector<uint32_t> &bytecode,
                                 GCNShaderType type) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    if (bytecode.empty()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::warn("Disassemble: Empty shader bytecode");
      return "Empty shader bytecode";
    }

    ShaderParseState state;
    state.bytecode = &bytecode;
    state.position = 0;
    state.type = type;

    if (!ParseShader(state)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("Disassemble: Failed to parse shader");
      return "Failed to parse shader";
    }

    lock.unlock(); // Release mutex during string building
    std::stringstream ss;
    ss << "Shader Type: ";
    switch (type) {
    case GCNShaderType::VERTEX:
      ss << "Vertex";
      break;
    case GCNShaderType::PIXEL:
      ss << "Pixel";
      break;
    case GCNShaderType::GEOMETRY:
      ss << "Geometry";
      break;
    case GCNShaderType::COMPUTE:
      ss << "Compute";
      break;
    case GCNShaderType::HULL:
      ss << "Hull";
      break;
    case GCNShaderType::DOMAIN_SHADER:
      ss << "Domain";
      break;
    default:
      ss << "Unknown";
      break;
    }
    ss << "\nInstruction Count: " << state.instructions.size()
       << "\nBytecode Size: " << bytecode.size() << " dwords\n\n";

    for (size_t i = 0; i < state.instructions.size(); ++i) {
      uint32_t address = static_cast<uint32_t>(i * 4);
      auto labelIt = state.labelMap.find(address);
      if (labelIt != state.labelMap.end()) {
        ss << labelIt->second << ":\n";
      }
      ss << "    " << std::setw(8) << std::setfill('0') << std::hex << address
         << ": " << DisassembleInstruction(state.instructions[i], address)
         << "\n";
    }

    std::unique_lock<std::shared_mutex> writeLock(m_translatorMutex);
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("Disassemble: type={}, instructions={}, latency={}us",
                 static_cast<int>(type), state.instructions.size(), latency);
    return ss.str();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("Disassemble failed: {}", e.what());
    throw ShaderTranslatorException("Disassemble failed: " +
                                    std::string(e.what()));
  }
}

std::vector<uint32_t>
GNMShaderTranslator::TranslateToSPIRV(const std::vector<uint32_t> &bytecode,
                                      GCNShaderType type) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    if (bytecode.empty()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ShaderTranslatorException("Empty shader bytecode");
    }

    uint64_t bytecodeHash = 0;
    for (const auto &word : bytecode) {
      bytecodeHash ^= std::hash<uint32_t>{}(word);
    }
    std::vector<uint32_t> spirvCode;
    std::string glslCode;
    if (GetCachedShader(bytecodeHash, type, spirvCode, glslCode)) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info(
          "TranslateToSPIRV: Cached shader, type={}, hash=0x{:x}, latency={}us",
          static_cast<int>(type), bytecodeHash, latency);
      return spirvCode;
    }

    ShaderParseState state;
    state.bytecode = &bytecode;
    state.position = 0;
    state.type = type;

    if (!ParseShader(state)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ShaderTranslatorException("Shader parsing failed");
    }

    lock.unlock(); // Release mutex during translation
    spirvCode.clear();
    spirvCode.push_back(0x07230203); // SPIR-V magic number
    spirvCode.push_back(0x00010300); // SPIR-V version 1.3
    spirvCode.push_back(0);          // Generator ID
    spirvCode.push_back(0);          // Bound (to be updated)
    spirvCode.push_back(0);          // Instruction schema

    uint32_t id = 1;
    spirvCode.push_back((4 << 16) | 1); // OpCapability Shader
    spirvCode.push_back(id++);          // Capability ID
    spirvCode.push_back((2 << 16) | 2); // OpExtInstImport "GLSL.std.450"
    spirvCode.push_back(id++);
    spirvCode.push_back('G' | ('L' << 8) | ('S' << 16) |
                        ('L' << 24)); // Add type declarations for SPIR-V
    uint32_t voidType = id++;
    uint32_t floatType = id++;
    uint32_t intType = id++;
    uint32_t vec4Type = id++;
    uint32_t vec4ArrayType = id++;
    uint32_t intArrayType = id++;
    uint32_t functionType = id++;
    uint32_t mainFunction = id++;
    uint32_t entryPoint = id++;

    // OpTypeVoid %void
    spirvCode.push_back((2 << 16) | 0x0013); // OpTypeVoid
    spirvCode.push_back(voidType);

    // OpTypeFloat %float 32
    spirvCode.push_back((3 << 16) | 0x0016); // OpTypeFloat
    spirvCode.push_back(floatType);
    spirvCode.push_back(32);

    // OpTypeInt %int 32 1
    spirvCode.push_back((4 << 16) | 0x0015); // OpTypeInt
    spirvCode.push_back(intType);
    spirvCode.push_back(32);
    spirvCode.push_back(1);

    // OpTypeVector %vec4 %float 4
    spirvCode.push_back((4 << 16) | 0x0017); // OpTypeVector
    spirvCode.push_back(vec4Type);
    spirvCode.push_back(floatType);
    spirvCode.push_back(4);

    // Map GCN instructions to SPIR-V with comprehensive coverage
    m_stats.instructionsCovered = 0;
    m_stats.instructionsSkipped = 0;

    for (const auto &instr : state.instructions) {
      switch (instr.opcode) {
      // Scalar ALU Instructions
      case 0xBE:                                 // S_MOV_B32
        spirvCode.push_back((4 << 16) | 0x003d); // OpCopyObject
        spirvCode.push_back(intType);            // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand ID
        m_stats.instructionsCovered++;
        break;
      case 0x80:                                 // S_ADD_U32
        spirvCode.push_back((5 << 16) | 0x0064); // OpIAdd
        spirvCode.push_back(intType);            // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x81:                                 // S_SUB_U32
        spirvCode.push_back((5 << 16) | 0x0065); // OpISub
        spirvCode.push_back(intType);            // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x82:                                 // S_MUL_I32
        spirvCode.push_back((5 << 16) | 0x0066); // OpIMul
        spirvCode.push_back(intType);            // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x83:                                 // S_AND_B32
        spirvCode.push_back((5 << 16) | 0x007f); // OpBitwiseAnd
        spirvCode.push_back(intType);            // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x84:                                 // S_OR_B32
        spirvCode.push_back((5 << 16) | 0x0080); // OpBitwiseOr
        spirvCode.push_back(intType);            // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x85:                                 // S_XOR_B32
        spirvCode.push_back((5 << 16) | 0x0081); // OpBitwiseXor
        spirvCode.push_back(intType);            // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x86:                                 // S_NOT_B32
        spirvCode.push_back((4 << 16) | 0x007e); // OpNot
        spirvCode.push_back(intType);            // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand
        m_stats.instructionsCovered++;
        break;
      case 0x87:                                 // S_LSHL_B32
        spirvCode.push_back((5 << 16) | 0x0089); // OpShiftLeftLogical
        spirvCode.push_back(intType);            // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Base
        spirvCode.push_back(id++);               // Shift
        m_stats.instructionsCovered++;
        break;
      case 0x88:                                 // S_LSHR_B32
        spirvCode.push_back((5 << 16) | 0x008a); // OpShiftRightLogical
        spirvCode.push_back(intType);            // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Base
        spirvCode.push_back(id++);               // Shift
        m_stats.instructionsCovered++;
        break;
      case 0x89:                                 // S_ASHR_I32
        spirvCode.push_back((5 << 16) | 0x008b); // OpShiftRightArithmetic
        spirvCode.push_back(intType);            // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Base
        spirvCode.push_back(id++);               // Shift
        m_stats.instructionsCovered++;
        break;
      case 0x90:                                 // S_CMP_EQ_I32
        spirvCode.push_back((5 << 16) | 0x00ac); // OpIEqual
        spirvCode.push_back(intType);            // Result Type (bool)
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x91:                                 // S_CMP_NE_I32
        spirvCode.push_back((5 << 16) | 0x00ad); // OpINotEqual
        spirvCode.push_back(intType);            // Result Type (bool)
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x92:                                 // S_CMP_GT_I32
        spirvCode.push_back((5 << 16) | 0x00b0); // OpSGreaterThan
        spirvCode.push_back(intType);            // Result Type (bool)
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;

      // Vector ALU Instructions
      case 0x01:                                 // V_MOV_B32
        spirvCode.push_back((4 << 16) | 0x003d); // OpCopyObject
        spirvCode.push_back(vec4Type);           // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand ID
        m_stats.instructionsCovered++;
        break;
      case 0x02:                                 // V_ADD_F32
        spirvCode.push_back((5 << 16) | 0x0081); // OpFAdd
        spirvCode.push_back(vec4Type);           // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x03:                                 // V_SUB_F32
        spirvCode.push_back((5 << 16) | 0x0082); // OpFSub
        spirvCode.push_back(vec4Type);           // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x04:                                 // V_MUL_F32
        spirvCode.push_back((5 << 16) | 0x0085); // OpFMul
        spirvCode.push_back(vec4Type);           // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x05:                                 // V_MAD_F32 / V_FMA_F32
        spirvCode.push_back((6 << 16) | 0x0088); // OpExtInst (for FMA)
        spirvCode.push_back(vec4Type);           // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(1);                  // GLSL.std.450 set
        spirvCode.push_back(50);                 // Fma instruction
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        spirvCode.push_back(id++);               // Operand 3
        m_stats.instructionsCovered++;
        break;
      case 0x06:                                 // V_DIV_F32
        spirvCode.push_back((5 << 16) | 0x0086); // OpFDiv
        spirvCode.push_back(vec4Type);           // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x07:                                 // V_MIN_F32
        spirvCode.push_back((6 << 16) | 0x0088); // OpExtInst
        spirvCode.push_back(vec4Type);           // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(1);                  // GLSL.std.450 set
        spirvCode.push_back(37);                 // FMin instruction
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x08:                                 // V_MAX_F32
        spirvCode.push_back((6 << 16) | 0x0088); // OpExtInst
        spirvCode.push_back(vec4Type);           // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(1);                  // GLSL.std.450 set
        spirvCode.push_back(40);                 // FMax instruction
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x09:                                 // V_ADD_I32
        spirvCode.push_back((5 << 16) | 0x0064); // OpIAdd
        spirvCode.push_back(vec4Type);           // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x0A:                                 // V_SUB_I32
        spirvCode.push_back((5 << 16) | 0x0065); // OpISub
        spirvCode.push_back(vec4Type);           // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x0B:                                 // V_MUL_I32
        spirvCode.push_back((5 << 16) | 0x0066); // OpIMul
        spirvCode.push_back(vec4Type);           // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x0C:                                 // V_AND_B32
        spirvCode.push_back((5 << 16) | 0x007f); // OpBitwiseAnd
        spirvCode.push_back(vec4Type);           // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x0D:                                 // V_OR_B32
        spirvCode.push_back((5 << 16) | 0x0080); // OpBitwiseOr
        spirvCode.push_back(vec4Type);           // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;

      // Memory Instructions
      case 0xA0:                                 // BUFFER_LOAD
        spirvCode.push_back((4 << 16) | 0x003f); // OpLoad
        spirvCode.push_back(vec4Type);           // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Pointer
        m_stats.instructionsCovered++;
        break;
      case 0xA1:                                 // BUFFER_STORE
        spirvCode.push_back((3 << 16) | 0x003e); // OpStore
        spirvCode.push_back(id++);               // Pointer
        spirvCode.push_back(id++);               // Object
        m_stats.instructionsCovered++;
        break;
      case 0xA2:                                 // IMAGE_LOAD
        spirvCode.push_back((5 << 16) | 0x0063); // OpImageRead
        spirvCode.push_back(vec4Type);           // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Image
        spirvCode.push_back(id++);               // Coordinate
        m_stats.instructionsCovered++;
        break;
      case 0xA3:                                 // IMAGE_STORE
        spirvCode.push_back((4 << 16) | 0x0064); // OpImageWrite
        spirvCode.push_back(id++);               // Image
        spirvCode.push_back(id++);               // Coordinate
        spirvCode.push_back(id++);               // Texel
        m_stats.instructionsCovered++;
        break;
      case 0xA4:                                 // DS_READ
        spirvCode.push_back((4 << 16) | 0x003f); // OpLoad (LDS access)
        spirvCode.push_back(vec4Type);           // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Pointer
        m_stats.instructionsCovered++;
        break;
      case 0xA5:                                 // DS_WRITE
        spirvCode.push_back((3 << 16) | 0x003e); // OpStore (LDS access)
        spirvCode.push_back(id++);               // Pointer
        spirvCode.push_back(id++);               // Object
        m_stats.instructionsCovered++;
        break;

      // Control Flow Instructions
      case 0xA6:                                 // S_BRANCH
        spirvCode.push_back((2 << 16) | 0x00f9); // OpBranch
        spirvCode.push_back(id++);               // Target Label
        m_stats.instructionsCovered++;
        break;
      case 0xA7:                                 // S_CBRANCH_SCC1
        spirvCode.push_back((4 << 16) | 0x00fa); // OpBranchConditional
        spirvCode.push_back(id++);               // Condition
        spirvCode.push_back(id++);               // True Label
        spirvCode.push_back(id++);               // False Label
        m_stats.instructionsCovered++;
        break;
      case 0xA8:                                 // S_WAITCNT
        spirvCode.push_back((1 << 16) | 0x00e0); // OpMemoryBarrier
        spirvCode.push_back(2);                  // Device scope
        spirvCode.push_back(72);                 // Memory semantics
        m_stats.instructionsCovered++;
        break;
      case 0xA9:                                 // S_BARRIER
        spirvCode.push_back((1 << 16) | 0x00e2); // OpControlBarrier
        spirvCode.push_back(2);                  // Execution scope
        spirvCode.push_back(2);                  // Memory scope
        spirvCode.push_back(72);                 // Memory semantics
        m_stats.instructionsCovered++;
        break;
      case 0xAA:                                 // S_ENDPGM
        spirvCode.push_back((1 << 16) | 0x00fd); // OpReturn
        m_stats.instructionsCovered++;
        break;

      // Vector Comparison Instructions
      case 0x10:                                 // V_CMP_EQ_F32
        spirvCode.push_back((5 << 16) | 0x00b4); // OpFOrdEqual
        spirvCode.push_back(intType);            // Result Type (bool)
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x11:                                 // V_CMP_NE_F32
        spirvCode.push_back((5 << 16) | 0x00b7); // OpFOrdNotEqual
        spirvCode.push_back(intType);            // Result Type (bool)
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x12:                                 // V_CMP_GT_F32
        spirvCode.push_back((5 << 16) | 0x00b8); // OpFOrdGreaterThan
        spirvCode.push_back(intType);            // Result Type (bool)
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;
      case 0x13:                                 // V_CMP_LT_F32
        spirvCode.push_back((5 << 16) | 0x00b6); // OpFOrdLessThan
        spirvCode.push_back(intType);            // Result Type (bool)
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Operand 1
        spirvCode.push_back(id++);               // Operand 2
        m_stats.instructionsCovered++;
        break;

      // Texture Instructions
      case 0xB0:                                 // IMAGE_SAMPLE
        spirvCode.push_back((5 << 16) | 0x0057); // OpImageSampleImplicitLod
        spirvCode.push_back(vec4Type);           // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Sampled Image
        spirvCode.push_back(id++);               // Coordinate
        m_stats.instructionsCovered++;
        break;
      case 0xB1:                                 // IMAGE_GATHER
        spirvCode.push_back((6 << 16) | 0x005b); // OpImageGather
        spirvCode.push_back(vec4Type);           // Result Type
        spirvCode.push_back(id++);               // Result ID
        spirvCode.push_back(id++);               // Sampled Image
        spirvCode.push_back(id++);               // Coordinate
        spirvCode.push_back(id++);               // Component
        m_stats.instructionsCovered++;
        break;

      // Export Instructions
      case 0xC0:                                 // EXP_POS
        spirvCode.push_back((3 << 16) | 0x003e); // OpStore (position output)
        spirvCode.push_back(id++);               // Position pointer
        spirvCode.push_back(id++);               // Value
        m_stats.instructionsCovered++;
        break;
      case 0xC1:                                 // EXP_PARAM
        spirvCode.push_back((3 << 16) | 0x003e); // OpStore (parameter output)
        spirvCode.push_back(id++);               // Parameter pointer
        spirvCode.push_back(id++);               // Value
        m_stats.instructionsCovered++;
        break;
      case 0xC2: // EXP_MRT
        spirvCode.push_back((3 << 16) |
                            0x003e); // OpStore (render target output)
        spirvCode.push_back(id++);   // MRT pointer
        spirvCode.push_back(id++);   // Value
        m_stats.instructionsCovered++;
        break;

      default:
        spdlog::debug("TranslateToSPIRV: Unhandled opcode 0x{:x}",
                      instr.opcode);
        m_stats.instructionsSkipped++;
        continue;
      }
    } // Add function end
    spirvCode.push_back((1 << 16) | 0x00fd); // OpReturn
    spirvCode.push_back((1 << 16) | 0x0038); // OpFunctionEnd

    // Update bound field (total number of IDs used)
    spirvCode[3] = id;

    ShaderCacheEntry cacheEntry{spirvCode, "", type};
    std::unique_lock<std::shared_mutex> writeLock(m_translatorMutex);
    m_shaderCache[bytecodeHash] = cacheEntry;
    m_stats.operationCount++;
    m_stats.spirvGenerations++;
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("TranslateToSPIRV: Enhanced shader translation, type={}, "
                 "instructions={}, spirv_size={} words, covered={}, "
                 "skipped={}, latency={}us",
                 static_cast<int>(type), state.instructions.size(),
                 spirvCode.size(), m_stats.instructionsCovered,
                 m_stats.instructionsSkipped, latency);
    // Notify PS4GPU of shader translation via callback
    if (m_spirvCallback) {
      m_spirvCallback(type, bytecodeHash, spirvCode);
    }
    return spirvCode;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("TranslateToSPIRV failed: {}", e.what());
    throw ShaderTranslatorException("TranslateToSPIRV failed: " +
                                    std::string(e.what()));
  }
}

std::string
GNMShaderTranslator::TranslatetoGLSL(const std::vector<uint32_t> &bytecode,
                                     GCNShaderType type) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    if (bytecode.empty()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ShaderTranslatorException("Empty shader bytecode");
    }

    uint64_t bytecodeHash = 0;
    for (const auto &word : bytecode) {
      bytecodeHash ^= std::hash<uint32_t>{}(word);
    }
    std::vector<uint32_t> spirvCode;
    std::string glslCode;
    if (GetCachedShader(bytecodeHash, type, spirvCode, glslCode)) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info(
          "TranslatetoGLSL: Cached shader, type={}, hash=0x{:x}, latency={}us",
          static_cast<int>(type), bytecodeHash, latency);
      return glslCode;
    }

    ShaderParseState state;
    state.bytecode = &bytecode;
    state.position = 0;
    state.type = type;

    if (!ParseShader(state)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ShaderTranslatorException("Shader parsing failed");
    }

    lock.unlock(); // Release mutex during string building
    std::stringstream ss;
    ss << "#version 450\n";
    switch (type) {
    case GCNShaderType::VERTEX:
      ss << "layout(location = 0) in vec4 inPosition;\n"
         << "layout(location = 0) out vec4 outColor;\n"
         << "void main() {\n"
         << "    gl_Position = inPosition;\n"
         << "    outColor = vec4(1.0);\n";
      break;
    case GCNShaderType::PIXEL:
      ss << "layout(location = 0) in vec4 inColor;\n"
         << "layout(location = 0) out vec4 fragColor;\n"
         << "void main() {\n"
         << "    fragColor = inColor;\n";
      break;
    case GCNShaderType::GEOMETRY:
      ss << "layout(triangles) in;\n"
         << "layout(triangle_strip, max_vertices = 3) out;\n"
         << "void main() {\n"
         << "    for (int i = 0; i < 3; i++) {\n"
         << "        gl_Position = gl_in[i].gl_Position;\n"
         << "        EmitVertex();\n"
         << "    }\n"
         << "    EndPrimitive();\n"
         << "}\n";
      break;
    case GCNShaderType::COMPUTE:
      ss << "layout(local_size_x = 1, local_size_y = 1, local_size_z = 1) in;\n"
         << "void main() {\n";
      break;
    case GCNShaderType::HULL:
      ss << "layout(vertices = 3) out;\n"
         << "void main() {\n"
         << "    gl_TessLevelOuter[0] = 1.0;\n"
         << "    gl_TessLevelOuter[1] = 1.0;\n"
         << "    gl_TessLevelOuter[2] = 1.0;\n"
         << "    gl_TessLevelInner[0] = 1.0;\n"
         << "    gl_out[gl_InvocationID].gl_Position = "
            "gl_in[gl_InvocationID].gl_Position;\n"
         << "}\n";
      break;
    case GCNShaderType::DOMAIN_SHADER:
      ss << "layout(triangles, equal_spacing, cw) in;\n"
         << "void main() {\n"
         << "    gl_Position = gl_in[0].gl_Position;\n";
      break;
    default:
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ShaderTranslatorException("Unsupported shader type: " +
                                      std::to_string(static_cast<int>(type)));
    } // Enhanced GLSL instruction generation
    ss << "    // Scalar registers\n";
    ss << "    uint s[256];\n";
    ss << "    // Vector registers  \n";
    ss << "    vec4 v[256];\n\n";

    for (const auto &instr : state.instructions) {
      switch (instr.opcode) {
      case 0xBE: // S_MOV_B32
        ss << "    s[" << instr.dst << "] = s[" << instr.src0 << "];\n";
        break;
      case 0x80: // S_ADD_U32
        ss << "    s[" << instr.dst << "] = s[" << instr.src0 << "] + s["
           << instr.src1 << "];\n";
        break;
      case 0x81: // S_SUB_U32
        ss << "    s[" << instr.dst << "] = s[" << instr.src0 << "] - s["
           << instr.src1 << "];\n";
        break;
      case 0x82: // S_MUL_I32
        ss << "    s[" << instr.dst << "] = s[" << instr.src0 << "] * s["
           << instr.src1 << "];\n";
        break;
      case 0x87: // S_AND_B32
        ss << "    s[" << instr.dst << "] = s[" << instr.src0 << "] & s["
           << instr.src1 << "];\n";
        break;
      case 0x88: // S_OR_B32
        ss << "    s[" << instr.dst << "] = s[" << instr.src0 << "] | s["
           << instr.src1 << "];\n";
        break;
      case 0x89: // S_XOR_B32
        ss << "    s[" << instr.dst << "] = s[" << instr.src0 << "] ^ s["
           << instr.src1 << "];\n";
        break;
      case 0x8A: // S_NOT_B32
        ss << "    s[" << instr.dst << "] = ~s[" << instr.src0 << "];\n";
        break;
      case 0x8B: // S_LSHL_B32
        ss << "    s[" << instr.dst << "] = s[" << instr.src0 << "] << s["
           << instr.src1 << "];\n";
        break;
      case 0x8C: // S_LSHR_B32
        ss << "    s[" << instr.dst << "] = s[" << instr.src0 << "] >> s["
           << instr.src1 << "];\n";
        break;
      case 0x8D: // S_ASHR_I32
        ss << "    s[" << instr.dst << "] = int(s[" << instr.src0
           << "]) >> int(s[" << instr.src1 << "]);\n";
        break;
      case 0x90: // S_CMP_EQ_I32
        ss << "    s[" << instr.dst << "] = (s[" << instr.src0 << "] == s["
           << instr.src1 << "]) ? 1u : 0u;\n";
        break;
      case 0x91: // S_CMP_LT_I32
        ss << "    s[" << instr.dst << "] = (int(s[" << instr.src0
           << "]) < int(s[" << instr.src1 << "])) ? 1u : 0u;\n";
        break;
      case 0x92: // S_CMP_LE_I32
        ss << "    s[" << instr.dst << "] = (int(s[" << instr.src0
           << "]) <= int(s[" << instr.src1 << "])) ? 1u : 0u;\n";
        break;
      case 0x93: // S_CMP_GT_I32
        ss << "    s[" << instr.dst << "] = (int(s[" << instr.src0
           << "]) > int(s[" << instr.src1 << "])) ? 1u : 0u;\n";
        break;
      case 0x94: // S_CMP_GE_I32
        ss << "    s[" << instr.dst << "] = (int(s[" << instr.src0
           << "]) >= int(s[" << instr.src1 << "])) ? 1u : 0u;\n";
        break;
      case 0xA8: // S_WAITCNT
        ss << "    memoryBarrier();\n";
        break;
      case 0x01: // V_MOV_B32
        ss << "    v[" << instr.dst << "] = v[" << instr.src0 << "];\n";
        break;
      case 0x02: // V_ADD_F32
        ss << "    v[" << instr.dst << "] = v[" << instr.src0 << "] + v["
           << instr.src1 << "];\n";
        break;
      case 0x03: // V_SUB_F32
        ss << "    v[" << instr.dst << "] = v[" << instr.src0 << "] - v["
           << instr.src1 << "];\n";
        break;
      case 0x04: // V_MUL_F32
        ss << "    v[" << instr.dst << "] = v[" << instr.src0 << "] * v["
           << instr.src1 << "];\n";
        break;
      case 0x05: // V_MAD_F32
        ss << "    v[" << instr.dst << "] = fma(v[" << instr.src0 << "], v["
           << instr.src1 << "], v[" << instr.src2 << "]);\n";
        break;
      case 0x06: // V_DIV_F32
        ss << "    v[" << instr.dst << "] = v[" << instr.src0 << "] / v["
           << instr.src1 << "];\n";
        break;
      case 0x07: // V_MIN_F32
        ss << "    v[" << instr.dst << "] = min(v[" << instr.src0 << "], v["
           << instr.src1 << "]);\n";
        break;
      case 0x08: // V_MAX_F32
        ss << "    v[" << instr.dst << "] = max(v[" << instr.src0 << "], v["
           << instr.src1 << "]);\n";
        break;
      case 0x09: // V_ADD_I32
        ss << "    v[" << instr.dst << "] = ivec4(ivec4(v[" << instr.src0
           << "]) + ivec4(v[" << instr.src1 << "]));\n";
        break;
      case 0x0A: // V_SUB_I32
        ss << "    v[" << instr.dst << "] = ivec4(ivec4(v[" << instr.src0
           << "]) - ivec4(v[" << instr.src1 << "]));\n";
        break;
      case 0x0B: // V_MUL_I32
        ss << "    v[" << instr.dst << "] = ivec4(ivec4(v[" << instr.src0
           << "]) * ivec4(v[" << instr.src1 << "]));\n";
        break;
      case 0x0C: // V_AND_B32
        ss << "    v[" << instr.dst << "] = uintBitsToFloat(floatBitsToUint(v["
           << instr.src0 << "]) & floatBitsToUint(v[" << instr.src1 << "]));\n";
        break;
      case 0x0D: // V_OR_B32
        ss << "    v[" << instr.dst << "] = uintBitsToFloat(floatBitsToUint(v["
           << instr.src0 << "]) | floatBitsToUint(v[" << instr.src1 << "]));\n";
        break;
      case 0x0E: // V_XOR_B32
        ss << "    v[" << instr.dst << "] = uintBitsToFloat(floatBitsToUint(v["
           << instr.src0 << "]) ^ floatBitsToUint(v[" << instr.src1 << "]));\n";
        break;
      case 0x0F: // V_NOT_B32
        ss << "    v[" << instr.dst << "] = uintBitsToFloat(~floatBitsToUint(v["
           << instr.src0 << "]));\n";
        break;
      case 0x10: // V_LSHL_B32
        ss << "    v[" << instr.dst << "] = uintBitsToFloat(floatBitsToUint(v["
           << instr.src0 << "]) << floatBitsToUint(v[" << instr.src1
           << "]));\n";
        break;
      case 0x11: // V_LSHR_B32
        ss << "    v[" << instr.dst << "] = uintBitsToFloat(floatBitsToUint(v["
           << instr.src0 << "]) >> floatBitsToUint(v[" << instr.src1
           << "]));\n";
        break;
      case 0x12: // V_ASHR_I32
        ss << "    v[" << instr.dst << "] = intBitsToFloat(floatBitsToInt(v["
           << instr.src0 << "]) >> floatBitsToInt(v[" << instr.src1 << "]));\n";
        break;
      case 0x20: // V_CMP_EQ_F32
        ss << "    s[" << instr.dst << "] = (v[" << instr.src0 << "] == v["
           << instr.src1 << "]) ? 1u : 0u;\n";
        break;
      case 0x21: // V_CMP_LT_F32
        ss << "    s[" << instr.dst << "] = any(lessThan(v[" << instr.src0
           << "], v[" << instr.src1 << "])) ? 1u : 0u;\n";
        break;
      case 0x22: // V_CMP_LE_F32
        ss << "    s[" << instr.dst << "] = any(lessThanEqual(v[" << instr.src0
           << "], v[" << instr.src1 << "])) ? 1u : 0u;\n";
        break;
      case 0x23: // V_CMP_GT_F32
        ss << "    s[" << instr.dst << "] = any(greaterThan(v[" << instr.src0
           << "], v[" << instr.src1 << "])) ? 1u : 0u;\n";
        break;
      case 0x24: // V_CMP_GE_F32
        ss << "    s[" << instr.dst << "] = any(greaterThanEqual(v["
           << instr.src0 << "], v[" << instr.src1 << "])) ? 1u : 0u;\n";
        break;
      case 0x40: // BUFFER_LOAD_DWORD
        ss << "    v[" << instr.dst << "] = texelFetch(buffer_texture, int(s["
           << instr.src0 << "]));\n";
        break;
      case 0x41: // BUFFER_STORE_DWORD
        ss << "    imageStore(buffer_image, int(s[" << instr.src0 << "]), v["
           << instr.src1 << "]);\n";
        break;
      case 0x42: // BUFFER_LOAD_DWORDX2
        ss << "    {\n";
        ss << "        vec4 data = texelFetch(buffer_texture, int(s["
           << instr.src0 << "]));\n";
        ss << "        v[" << instr.dst << "] = data;\n";
        ss << "        v[" << (instr.dst + 1)
           << "] = texelFetch(buffer_texture, int(s[" << instr.src0
           << "]) + 1);\n";
        ss << "    }\n";
        break;
      case 0x43: // BUFFER_STORE_DWORDX2
        ss << "    imageStore(buffer_image, int(s[" << instr.src0 << "]), v["
           << instr.src1 << "]);\n";
        ss << "    imageStore(buffer_image, int(s[" << instr.src0
           << "]) + 1, v[" << (instr.src1 + 1) << "]);\n";
        break;
      case 0x44: // BUFFER_LOAD_DWORDX4
        ss << "    {\n";
        ss << "        for (int i = 0; i < 4; i++) {\n";
        ss << "            v[" << instr.dst
           << " + i] = texelFetch(buffer_texture, int(s[" << instr.src0
           << "]) + i);\n";
        ss << "        }\n";
        ss << "    }\n";
        break;
      case 0x45: // BUFFER_STORE_DWORDX4
        ss << "    for (int i = 0; i < 4; i++) {\n";
        ss << "        imageStore(buffer_image, int(s[" << instr.src0
           << "]) + i, v[" << instr.src1 << " + i]);\n";
        ss << "    }\n";
        break;
      case 0x50: // DS_READ_B32
        ss << "    v[" << instr.dst << "] = texelFetch(local_memory, int(s["
           << instr.src0 << "]));\n";
        break;
      case 0x51: // DS_WRITE_B32
        ss << "    imageStore(local_memory, int(s[" << instr.src0 << "]), v["
           << instr.src1 << "]);\n";
        break;
      case 0xA0: // S_BRANCH
        ss << "    // Unconditional branch to address 0x" << std::hex
           << instr.imm << std::dec << "\n";
        break;
      case 0xA1: // S_CBRANCH
        ss << "    if (s[" << instr.src0 << "] != 0u) {\n";
        ss << "        // Conditional branch to address 0x" << std::hex
           << instr.imm << std::dec << "\n";
        ss << "    }\n";
        break;
      default:
        ss << "    // Unhandled opcode: 0x" << std::hex << instr.opcode
           << std::dec << "\n";
        m_stats.instructionsSkipped++;
        break;
      }
      m_stats.instructionsCovered++;
    }

    if (type != GCNShaderType::GEOMETRY) {
      ss << "}\n";
    }

    glslCode = ss.str();
    ShaderCacheEntry cacheEntry{{}, glslCode, type};
    std::unique_lock<std::shared_mutex> writeLock(m_translatorMutex);
    m_shaderCache[bytecodeHash] = cacheEntry;
    m_stats.operationCount++;
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("TranslatetoGLSL: Translated shader, type={}, latency={}us",
                 static_cast<int>(type), latency);
    // Notify PS4GPU of shader translation via callback
    if (m_glslCallback) {
      m_glslCallback(type, bytecodeHash, glslCode);
    }
    return glslCode;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("TranslatetoGLSL failed: {}", e.what());
    throw ShaderTranslatorException("TranslatetoGLSL failed: " +
                                    std::string(e.what()));
  }
}

bool GNMShaderTranslator::Translate(const std::vector<uint32_t> &spirv,
                                    std::vector<uint32_t> &gnmBinary) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    if (spirv.empty()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ShaderTranslatorException("Empty SPIR-V input");
    }

    gnmBinary.clear();
    // Detailed SPIR-V to GCN mapping
    for (size_t i = 0; i < spirv.size();) {
      uint32_t wordCount = spirv[i] >> 16;
      uint32_t opcode = spirv[i] & 0xFFFF;
      if (opcode == 0x003d) { // OpCopyObject (maps to S_MOV_B32 or V_MOV_B32)
        if (wordCount >= 3) {
          GCNInstruction instr;
          instr.opcode =
              0xBE; // S_MOV_B32 as default, could be V_MOV_B32 based on context
          instr.dst = spirv[i + 1];
          instr.src0 = spirv[i + 2];
          gnmBinary.push_back((instr.opcode << 24) | (instr.dst << 16) |
                              (instr.src0 << 8));
        }
      } else if (opcode == 0x0064) { // OpIAdd (maps to S_ADD_U32)
        if (wordCount >= 4) {
          GCNInstruction instr;
          instr.opcode = 0x80; // S_ADD_U32
          instr.dst = spirv[i + 2];
          instr.src0 = spirv[i + 3];
          instr.src1 = spirv[i + 4];
          gnmBinary.push_back((instr.opcode << 24) | (instr.dst << 16) |
                              (instr.src0 << 8) | instr.src1);
        }
      } else if (opcode == 0x0065) { // OpISub (maps to S_SUB_U32)
        if (wordCount >= 4) {
          GCNInstruction instr;
          instr.opcode = 0x81; // S_SUB_U32
          instr.dst = spirv[i + 2];
          instr.src0 = spirv[i + 3];
          instr.src1 = spirv[i + 4];
          gnmBinary.push_back((instr.opcode << 24) | (instr.dst << 16) |
                              (instr.src0 << 8) | instr.src1);
        }
      } else if (opcode == 0x0080) { // OpFAdd (maps to V_ADD_F32)
        if (wordCount >= 4) {
          GCNInstruction instr;
          instr.opcode = 0x02; // V_ADD_F32
          instr.dst = spirv[i + 2];
          instr.src0 = spirv[i + 3];
          instr.src1 = spirv[i + 4];
          gnmBinary.push_back((instr.opcode << 24) | (instr.dst << 16) |
                              (instr.src0 << 8) | instr.src1);
        }
      } else if (opcode == 0x0081) { // OpFSub (maps to V_SUB_F32)
        if (wordCount >= 4) {
          GCNInstruction instr;
          instr.opcode = 0x03; // V_SUB_F32
          instr.dst = spirv[i + 2];
          instr.src0 = spirv[i + 3];
          instr.src1 = spirv[i + 4];
          gnmBinary.push_back((instr.opcode << 24) | (instr.dst << 16) |
                              (instr.src0 << 8) | instr.src1);
        }
      } else if (opcode == 0x0082) { // OpFMul (maps to V_MUL_F32)
        if (wordCount >= 4) {
          GCNInstruction instr;
          instr.opcode = 0x04; // V_MUL_F32
          instr.dst = spirv[i + 2];
          instr.src0 = spirv[i + 3];
          instr.src1 = spirv[i + 4];
          gnmBinary.push_back((instr.opcode << 24) | (instr.dst << 16) |
                              (instr.src0 << 8) | instr.src1);
        }
      } else if (opcode == 0x0083) { // OpFMA (maps to V_MAD_F32)
        if (wordCount >= 5) {
          GCNInstruction instr;
          instr.opcode = 0x05; // V_MAD_F32
          instr.dst = spirv[i + 2];
          instr.src0 = spirv[i + 3];
          instr.src1 = spirv[i + 4];
          instr.src2 = spirv[i + 5];
          gnmBinary.push_back((instr.opcode << 24) | (instr.dst << 16) |
                              (instr.src0 << 8) | instr.src1);
          gnmBinary.push_back(instr.src2
                              << 24); // Additional word for third source
        }
      } else if (opcode == 0x0084) { // OpFDiv (maps to V_DIV_F32)
        if (wordCount >= 4) {
          GCNInstruction instr;
          instr.opcode = 0x06; // V_DIV_F32
          instr.dst = spirv[i + 2];
          instr.src0 = spirv[i + 3];
          instr.src1 = spirv[i + 4];
          gnmBinary.push_back((instr.opcode << 24) | (instr.dst << 16) |
                              (instr.src0 << 8) | instr.src1);
        }
      }
      // CRITICAL FIX: Prevent infinite loop with proper bounds checking
      if (wordCount == 0) {
        spdlog::warn("Translate: Zero word count at position {}, skipping", i);
        i++;
      } else if (i + wordCount > spirv.size()) {
        spdlog::warn(
            "Translate: Word count {} exceeds remaining size at position {}",
            wordCount, i);
        break;
      } else {
        i += wordCount;
      }
    }

    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info(
        "Translate: SPIR-V to GNM, spirv_size={}, gnm_size={}, latency={}us",
        spirv.size(), gnmBinary.size(), latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("Translate failed: {}", e.what());
    throw ShaderTranslatorException("Translate failed: " +
                                    std::string(e.what()));
  }
}

bool GNMShaderTranslator::GetCachedShader(uint64_t bytecodeHash,
                                          GCNShaderType type,
                                          std::vector<uint32_t> &spirvCode,
                                          std::string &glslCode) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    auto it = m_shaderCache.find(bytecodeHash);
    if (it != m_shaderCache.end() && it->second.type == type) {
      spirvCode = it->second.spirvCode;
      glslCode = it->second.glslCode;
      it->second.cacheHits++;
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("GetCachedShader: hash=0x{:x}, type={}, hit, latency={}us",
                    bytecodeHash, static_cast<int>(type), latency);
      return true;
    }
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetCachedShader: hash=0x{:x}, type={}, miss, latency={}us",
                  bytecodeHash, static_cast<int>(type), latency);
    return false;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetCachedShader failed: {}", e.what());
    return false;
  }
}

void GNMShaderTranslator::ClearShaderCache() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    m_shaderCache.clear();
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ClearShaderCache: Cleared cache, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ClearShaderCache failed: {}", e.what());
  }
}

ShaderTranslatorStats GNMShaderTranslator::GetStats() const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetStats: latency={}us", latency);
    return m_stats;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetStats failed: {}", e.what());
    return m_stats;
  }
}

bool GNMShaderTranslator::ParseShader(ShaderParseState &state) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    const std::vector<uint32_t> &bytecode = *state.bytecode;
    state.instructions.clear();
    state.labelMap.clear();

    while (state.position < bytecode.size()) {
      GCNInstruction instr;
      if (!ParseInstruction(state, instr)) {
        m_stats.errorCount++;
        m_stats.cacheMisses++;
        spdlog::error("ParseShader: Failed to parse instruction at position {}",
                      state.position);
        return false;
      }
      // instr.Validate(); // removed: no such member

      state.instructions.push_back(instr);

      if ((instr.opcode & 0xF0) == 0xA0) {
        uint32_t targetAddress = instr.imm * 4;
        std::stringstream labelName;
        labelName << "L_" << std::hex << std::setw(8) << std::setfill('0')
                  << targetAddress;
        state.labelMap[targetAddress] = labelName.str();
      }
    }

    // state.cacheHits++; // removed: ShaderParseState has no cacheHits

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ParseShader: instructions={}, latency={}us",
                  state.instructions.size(), latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ParseShader failed: {}", e.what());
    return false;
  }
}

bool GNMShaderTranslator::ParseInstruction(ShaderParseState &state,
                                           GCNInstruction &instr) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    const std::vector<uint32_t> &bytecode = *state.bytecode;
    if (state.position >= bytecode.size()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      return false;
    }

    uint32_t word = bytecode[state.position++];
    instr.opcode = (word >> 24) & 0xFF;
    instr.dst = (word >> 16) & 0xFF;
    instr.src0 = (word >> 8) & 0xFF;
    instr.src1 = word & 0xFF;

    if (state.position < bytecode.size()) {
      word = bytecode[state.position++];
      instr.src2 = (word >> 24) & 0xFF;
      instr.imm = word & 0xFFFFFF;
    } else {
      instr.src2 = 0;
      instr.imm = 0;
    }

    instr.predicated = false;
    instr.predicate = 0;
    // instr.usageCount++; // removed: no such member
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ParseInstruction: opcode=0x{:x}, dst={}, src0={}, src1={}, "
                  "latency={}us",
                  instr.opcode, instr.dst, instr.src0, instr.src1, latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ParseInstruction failed: {}", e.what());
    return false;
  }
}

std::string
GNMShaderTranslator::DisassembleInstruction(const GCNInstruction &instr,
                                            uint32_t address) {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    std::string result;
    if ((instr.opcode & 0xF0) == 0x80 || (instr.opcode & 0xF0) == 0x90 ||
        instr.opcode == 0xBE) {
      result = FormatScalarInstruction(instr);
    } else if ((instr.opcode & 0xF0) == 0x00 || (instr.opcode & 0xF0) == 0x10 ||
               (instr.opcode & 0xF0) == 0x20) {
      result = FormatVectorInstruction(instr);
    } else if ((instr.opcode & 0xF0) == 0x40 || (instr.opcode & 0xF0) == 0x50) {
      result = FormatMemoryInstruction(instr);
    } else if ((instr.opcode & 0xF0) == 0xA0) {
      result = FormatFlowControlInstruction(instr);
    } else {
      std::stringstream ss;
      ss << "UNKNOWN_" << std::hex << std::setw(2) << std::setfill('0')
         << instr.opcode;
      result = ss.str();
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace(
        "DisassembleInstruction: opcode=0x{:x}, address=0x{:x}, latency={}us",
        instr.opcode, address, latency);
    return result;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("DisassembleInstruction failed: {}", e.what());
    return "ERROR";
  }
}

std::string GNMShaderTranslator::GetRegisterName(GCNRegisterType type,
                                                 uint32_t index) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    std::stringstream ss;
    switch (type) {
    case GCNRegisterType::SCALAR:
      ss << "s" << index;
      break;
    case GCNRegisterType::VECTOR:
      ss << "v" << index;
      break;
    case GCNRegisterType::SPECIAL:
      switch (index) {
      case 0:
        ss << "vcc";
        break;
      case 1:
        ss << "exec";
        break;
      case 2:
        ss << "scc";
        break;
      default:
        ss << "special" << index;
        break;
      }
      break;
    default:
      ss << "unknown" << index;
      break;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetRegisterName: type={}, index={}, latency={}us",
                  static_cast<int>(type), index, latency);
    return ss.str();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetRegisterName failed: {}", e.what());
    return "ERROR";
  }
}

std::string GNMShaderTranslator::GetOpcodeName(uint32_t opcode) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    auto scalarIt = m_scalarOpcodes.find(opcode);
    if (scalarIt != m_scalarOpcodes.end()) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("GetOpcodeName: opcode=0x{:x}, name={}, latency={}us",
                    opcode, scalarIt->second, latency);
      return scalarIt->second;
    }
    auto vectorIt = m_vectorOpcodes.find(opcode);
    if (vectorIt != m_vectorOpcodes.end()) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("GetOpcodeName: opcode=0x{:x}, name={}, latency={}us",
                    opcode, vectorIt->second, latency);
      return vectorIt->second;
    }
    auto memoryIt = m_memoryOpcodes.find(opcode);
    if (memoryIt != m_memoryOpcodes.end()) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("GetOpcodeName: opcode=0x{:x}, name={}, latency={}us",
                    opcode, memoryIt->second, latency);
      return memoryIt->second;
    }
    auto flowIt = m_flowControlOpcodes.find(opcode);
    if (flowIt != m_flowControlOpcodes.end()) {
      m_stats.cacheHits++;
      auto end = std::chrono::steady_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace("GetOpcodeName: opcode=0x{:x}, name={}, latency={}us",
                    opcode, flowIt->second, latency);
      return flowIt->second;
    }
    std::stringstream ss;
    ss << "UNKNOWN_" << std::hex << std::setw(2) << std::setfill('0') << opcode;
    m_stats.cacheMisses++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetOpcodeName: opcode=0x{:x}, name={}, latency={}us", opcode,
                  ss.str(), latency);
    return ss.str();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetOpcodeName failed: {}", e.what());
    return "ERROR";
  }
}

std::string GNMShaderTranslator::FormatScalarInstruction(
    const GCNInstruction &instr) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    std::stringstream ss;
    ss << GetOpcodeName(instr.opcode) << " ";
    switch (instr.opcode) {
    case 0xBE: // S_MOV_B32
      ss << GetRegisterName(GCNRegisterType::SCALAR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src0);
      break;
    case 0x80:
    case 0x81:
    case 0x82:
    case 0x87:
    case 0x88:
    case 0x89:
    case 0x8B:
    case 0x8C:
    case 0x8D:
      ss << GetRegisterName(GCNRegisterType::SCALAR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src0) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src1);
      break;
    case 0x8A: // S_NOT_B32
      ss << GetRegisterName(GCNRegisterType::SCALAR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src0);
      break;
    case 0x90:
    case 0x91:
    case 0x92:
    case 0x93:
    case 0x94:
      ss << GetRegisterName(GCNRegisterType::SCALAR, instr.src0) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src1);
      break;
    case 0xA8: // S_WAITCNT
      ss << "0x" << std::hex << instr.imm;
      break;
    default:
      ss << GetRegisterName(GCNRegisterType::SCALAR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src0) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src1);
      break;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("FormatScalarInstruction: opcode=0x{:x}, latency={}us",
                  instr.opcode, latency);
    return ss.str();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("FormatScalarInstruction failed: {}", e.what());
    return "ERROR";
  }
}

std::string GNMShaderTranslator::FormatVectorInstruction(
    const GCNInstruction &instr) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    std::stringstream ss;
    ss << GetOpcodeName(instr.opcode) << " ";
    switch (instr.opcode) {
    case 0x01: // V_MOV_B32
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src0);
      break;
    case 0x02:
    case 0x03:
    case 0x04:
    case 0x06:
    case 0x07:
    case 0x08:
    case 0x09:
    case 0x0A:
    case 0x0B:
    case 0x0C:
    case 0x0D:
    case 0x0E:
    case 0x10:
    case 0x11:
    case 0x12:
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src0) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src1);
      break;
    case 0x05: // V_MAD_F32
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src0) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src1) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src2);
      break;
    case 0x0F: // V_NOT_B32
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src0);
      break;
    case 0x20:
    case 0x21:
    case 0x22:
    case 0x23:
    case 0x24:
      ss << GetRegisterName(GCNRegisterType::SPECIAL, 0) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src0) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src1);
      break;
    default:
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src0) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src1);
      break;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("FormatVectorInstruction: opcode=0x{:x}, latency={}us",
                  instr.opcode, latency);
    return ss.str();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("FormatVectorInstruction failed: {}", e.what());
    return "ERROR";
  }
}

std::string GNMShaderTranslator::FormatMemoryInstruction(
    const GCNInstruction &instr) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    std::stringstream ss;
    ss << GetOpcodeName(instr.opcode) << " ";
    switch (instr.opcode) {
    case 0x40:
    case 0x41:
    case 0x42:
    case 0x43:
    case 0x44:
    case 0x45:
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src0) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src1) << ", "
         << "0x" << std::hex << instr.imm << " offen";
      break;
    case 0x50:
    case 0x51:
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src0);
      if (instr.imm != 0) {
        ss << " offset:" << std::hex << instr.imm;
      }
      break;
    default:
      ss << GetRegisterName(GCNRegisterType::VECTOR, instr.dst) << ", "
         << GetRegisterName(GCNRegisterType::VECTOR, instr.src0) << ", "
         << GetRegisterName(GCNRegisterType::SCALAR, instr.src1) << ", "
         << "0x" << std::hex << instr.imm;
      break;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("FormatMemoryInstruction: opcode=0x{:x}, latency={}us",
                  instr.opcode, latency);
    return ss.str();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("FormatMemoryInstruction failed: {}", e.what());
    return "ERROR";
  }
}

std::string GNMShaderTranslator::FormatFlowControlInstruction(
    const GCNInstruction &instr) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    std::stringstream ss;
    ss << GetOpcodeName(instr.opcode) << " ";
    switch (instr.opcode) {
    case 0xA0: // S_BRANCH
      ss << "L_" << std::hex << std::setw(8) << std::setfill('0')
         << (instr.imm * 4);
      break;
    case 0xA1: // S_CBRANCH
      ss << GetRegisterName(GCNRegisterType::SPECIAL, instr.src0) << ", "
         << "L_" << std::hex << std::setw(8) << std::setfill('0')
         << (instr.imm * 4);
      break;
    default:
      ss << "0x" << std::hex << instr.imm;
      break;
    }
    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("FormatFlowControlInstruction: opcode=0x{:x}, latency={}us",
                  instr.opcode, latency);
    return ss.str();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("FormatFlowControlInstruction failed: {}", e.what());
    return "ERROR";
  }
}

void GNMShaderTranslator::InitializeOpcodeTables() {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    m_scalarOpcodes[0xBE] = "s_mov_b32";
    m_scalarOpcodes[0x80] = "s_add_u32";
    m_scalarOpcodes[0x81] = "s_sub_u32";
    m_scalarOpcodes[0x82] = "s_mul_i32";
    m_scalarOpcodes[0x87] = "s_and_b32";
    m_scalarOpcodes[0x88] = "s_or_b32";
    m_scalarOpcodes[0x89] = "s_xor_b32";
    m_scalarOpcodes[0x8A] = "s_not_b32";
    m_scalarOpcodes[0x8B] = "s_lshl_b32";
    m_scalarOpcodes[0x8C] = "s_lshr_b32";
    m_scalarOpcodes[0x8D] = "s_ashr_i32";
    m_scalarOpcodes[0x90] = "s_cmp_eq_i32";
    m_scalarOpcodes[0x91] = "s_cmp_lt_i32";
    m_scalarOpcodes[0x92] = "s_cmp_le_i32";
    m_scalarOpcodes[0x93] = "s_cmp_gt_i32";
    m_scalarOpcodes[0x94] = "s_cmp_ge_i32";
    m_scalarOpcodes[0xA8] = "s_waitcnt";

    m_vectorOpcodes[0x01] = "v_mov_b32";
    m_vectorOpcodes[0x02] = "v_add_f32";
    m_vectorOpcodes[0x03] = "v_sub_f32";
    m_vectorOpcodes[0x04] = "v_mul_f32";
    m_vectorOpcodes[0x05] = "v_mad_f32";
    m_vectorOpcodes[0x06] = "v_div_f32";
    m_vectorOpcodes[0x07] = "v_min_f32";
    m_vectorOpcodes[0x08] = "v_max_f32";
    m_vectorOpcodes[0x09] = "v_add_i32";
    m_vectorOpcodes[0x0A] = "v_sub_i32";
    m_vectorOpcodes[0x0B] = "v_mul_i32";
    m_vectorOpcodes[0x0C] = "v_and_b32";
    m_vectorOpcodes[0x0D] = "v_or_b32";
    m_vectorOpcodes[0x0E] = "v_xor_b32";
    m_vectorOpcodes[0x0F] = "v_not_b32";
    m_vectorOpcodes[0x10] = "v_lshl_b32";
    m_vectorOpcodes[0x11] = "v_lshr_b32";
    m_vectorOpcodes[0x12] = "v_ashr_i32";
    m_vectorOpcodes[0x20] = "v_cmp_eq_f32";
    m_vectorOpcodes[0x21] = "v_cmp_lt_f32";
    m_vectorOpcodes[0x22] = "v_cmp_le_f32";
    m_vectorOpcodes[0x23] = "v_cmp_gt_f32";
    m_vectorOpcodes[0x24] = "v_cmp_ge_f32";

    m_memoryOpcodes[0x40] = "buffer_load_dword";
    m_memoryOpcodes[0x41] = "buffer_store_dword";
    m_memoryOpcodes[0x42] = "buffer_load_dwordx2";
    m_memoryOpcodes[0x43] = "buffer_store_dwordx2";
    m_memoryOpcodes[0x44] = "buffer_load_dwordx4";
    m_memoryOpcodes[0x45] = "buffer_store_dwordx4";
    m_memoryOpcodes[0x50] = "ds_read_b32";
    m_memoryOpcodes[0x51] = "ds_write_b32";

    m_flowControlOpcodes[0xA0] = "s_branch";
    m_flowControlOpcodes[0xA1] = "s_cbranch";

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("InitializeOpcodeTables: scalar={}, vector={}, memory={}, "
                  "flow={}, latency={}us",
                  m_scalarOpcodes.size(), m_vectorOpcodes.size(),
                  m_memoryOpcodes.size(), m_flowControlOpcodes.size(), latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("InitializeOpcodeTables failed: {}", e.what());
    throw ShaderTranslatorException("InitializeOpcodeTables failed: " +
                                    std::string(e.what()));
  }
}

void GNMShaderTranslator::SaveState(std::ostream &out) const {
  auto start = std::chrono::steady_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    uint32_t version = 1; // Serialization version
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));

    uint64_t scalarCount = m_scalarOpcodes.size();
    out.write(reinterpret_cast<const char *>(&scalarCount),
              sizeof(scalarCount));
    for (const auto &[opcode, name] : m_scalarOpcodes) {
      out.write(reinterpret_cast<const char *>(&opcode), sizeof(opcode));
      uint32_t nameLen = static_cast<uint32_t>(name.size());
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
      out.write(name.data(), nameLen);
    }

    uint64_t vectorCount = m_vectorOpcodes.size();
    out.write(reinterpret_cast<const char *>(&vectorCount),
              sizeof(vectorCount));
    for (const auto &[opcode, name] : m_vectorOpcodes) {
      out.write(reinterpret_cast<const char *>(&opcode), sizeof(opcode));
      uint32_t nameLen = static_cast<uint32_t>(name.size());
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
      out.write(name.data(), nameLen);
    }

    uint64_t memoryCount = m_memoryOpcodes.size();
    out.write(reinterpret_cast<const char *>(&memoryCount),
              sizeof(memoryCount));
    for (const auto &[opcode, name] : m_memoryOpcodes) {
      out.write(reinterpret_cast<const char *>(&opcode), sizeof(opcode));
      uint32_t nameLen = static_cast<uint32_t>(name.size());
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
      out.write(name.data(), nameLen);
    }

    uint64_t flowCount = m_flowControlOpcodes.size();
    out.write(reinterpret_cast<const char *>(&flowCount), sizeof(flowCount));
    for (const auto &[opcode, name] : m_flowControlOpcodes) {
      out.write(reinterpret_cast<const char *>(&opcode), sizeof(opcode));
      uint32_t nameLen = static_cast<uint32_t>(name.size());
      out.write(reinterpret_cast<const char *>(&nameLen), sizeof(nameLen));
      out.write(name.data(), nameLen);
    }

    uint64_t cacheCount = m_shaderCache.size();
    out.write(reinterpret_cast<const char *>(&cacheCount), sizeof(cacheCount));
    for (const auto &[hash, entry] : m_shaderCache) {
      out.write(reinterpret_cast<const char *>(&hash), sizeof(hash));
      uint32_t spirvSize = static_cast<uint32_t>(entry.spirvCode.size());
      out.write(reinterpret_cast<const char *>(&spirvSize), sizeof(spirvSize));
      out.write(reinterpret_cast<const char *>(entry.spirvCode.data()),
                spirvSize * sizeof(uint32_t));
      uint32_t glslSize = static_cast<uint32_t>(entry.glslCode.size());
      out.write(reinterpret_cast<const char *>(&glslSize), sizeof(glslSize));
      out.write(entry.glslCode.data(), glslSize);
      out.write(reinterpret_cast<const char *>(&entry.type),
                sizeof(entry.type));
      out.write(reinterpret_cast<const char *>(&entry.cacheHits),
                sizeof(entry.cacheHits));
      out.write(reinterpret_cast<const char *>(&entry.cacheMisses),
                sizeof(entry.cacheMisses));
    }

    out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));

    if (!out.good()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ShaderTranslatorException(
          "Failed to write shader translator state");
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("SaveState: Saved shader translator state, scalar_ops={}, "
                 "vector_ops={}, "
                 "memory_ops={}, flow_ops={}, cache_count={}, latency={}us",
                 scalarCount, vectorCount, memoryCount, flowCount, cacheCount,
                 latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SaveState failed: {}", e.what());
    throw ShaderTranslatorException("SaveState failed: " +
                                    std::string(e.what()));
  }
}

void GNMShaderTranslator::LoadState(std::istream &in) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ShaderTranslatorException(
          "Unsupported shader translator state version: " +
          std::to_string(version));
    }

    m_scalarOpcodes.clear();
    uint64_t scalarCount;
    in.read(reinterpret_cast<char *>(&scalarCount), sizeof(scalarCount));
    for (uint64_t i = 0; i < scalarCount && in.good(); ++i) {
      uint32_t opcode;
      uint32_t nameLen;
      in.read(reinterpret_cast<char *>(&opcode), sizeof(opcode));
      in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
      std::string name(nameLen, '\0');
      in.read(name.data(), nameLen);
      m_scalarOpcodes[opcode] = name;
    }

    m_vectorOpcodes.clear();
    uint64_t vectorCount;
    in.read(reinterpret_cast<char *>(&vectorCount), sizeof(vectorCount));
    for (uint64_t i = 0; i < vectorCount && in.good(); ++i) {
      uint32_t opcode;
      uint32_t nameLen;
      in.read(reinterpret_cast<char *>(&opcode), sizeof(opcode));
      in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
      std::string name(nameLen, '\0');
      in.read(name.data(), nameLen);
      m_vectorOpcodes[opcode] = name;
    }

    m_memoryOpcodes.clear();
    uint64_t memoryCount;
    in.read(reinterpret_cast<char *>(&memoryCount), sizeof(memoryCount));
    for (uint64_t i = 0; i < memoryCount && in.good(); ++i) {
      uint32_t opcode;
      uint32_t nameLen;
      in.read(reinterpret_cast<char *>(&opcode), sizeof(opcode));
      in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
      std::string name(nameLen, '\0');
      in.read(name.data(), nameLen);
      m_memoryOpcodes[opcode] = name;
    }

    m_flowControlOpcodes.clear();
    uint64_t flowCount;
    in.read(reinterpret_cast<char *>(&flowCount), sizeof(flowCount));
    for (uint64_t i = 0; i < flowCount && in.good(); ++i) {
      uint32_t opcode;
      uint32_t nameLen;
      in.read(reinterpret_cast<char *>(&opcode), sizeof(opcode));
      in.read(reinterpret_cast<char *>(&nameLen), sizeof(nameLen));
      std::string name(nameLen, '\0');
      in.read(name.data(), nameLen);
      m_flowControlOpcodes[opcode] = name;
    }

    m_shaderCache.clear();
    uint64_t cacheCount;
    in.read(reinterpret_cast<char *>(&cacheCount), sizeof(cacheCount));
    for (uint64_t i = 0; i < cacheCount && in.good(); ++i) {
      uint64_t hash;
      ShaderCacheEntry entry;
      in.read(reinterpret_cast<char *>(&hash), sizeof(hash));
      uint32_t spirvSize;
      in.read(reinterpret_cast<char *>(&spirvSize), sizeof(spirvSize));
      entry.spirvCode.resize(spirvSize);
      in.read(reinterpret_cast<char *>(entry.spirvCode.data()),
              spirvSize * sizeof(uint32_t));
      uint32_t glslSize;
      in.read(reinterpret_cast<char *>(&glslSize), sizeof(glslSize));
      entry.glslCode.resize(glslSize);
      in.read(entry.glslCode.data(), glslSize);
      in.read(reinterpret_cast<char *>(&entry.type), sizeof(entry.type));
      in.read(reinterpret_cast<char *>(&entry.cacheHits),
              sizeof(entry.cacheHits));
      in.read(reinterpret_cast<char *>(&entry.cacheMisses),
              sizeof(entry.cacheMisses));
      m_shaderCache[hash] = entry;
    }

    in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));

    if (!in.good()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw ShaderTranslatorException("Failed to read shader translator state");
    }

    m_stats.cacheHits++;
    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("LoadState: Loaded shader translator state, scalar_ops={}, "
                 "vector_ops={}, "
                 "memory_ops={}, flow_ops={}, cache_count={}, latency={}us",
                 scalarCount, vectorCount, memoryCount, flowCount, cacheCount,
                 latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("LoadState failed: {}", e.what());
    throw ShaderTranslatorException("LoadState failed: " +
                                    std::string(e.what()));
  }
}

void GNMShaderTranslator::SetShaderTranslationCallback_SPIRV(
    const ShaderTranslationCallback_SPIRV &callback) {
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  m_spirvCallback = callback;
  spdlog::debug("GNMShaderTranslator: SPIR-V callback set");
}

void GNMShaderTranslator::SetShaderTranslationCallback_GLSL(
    const ShaderTranslationCallback_GLSL &callback) {
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  m_glslCallback = callback;
  spdlog::debug("GNMShaderTranslator: GLSL callback set");
}

/**
 * @brief Enhanced GCN instruction translation implementation.
 */
bool GNMShaderTranslator::TranslateGCNInstruction(
    const EnhancedGCNInstruction &instr, std::string &spirvCode,
    std::string &glslCode) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_translatorMutex);
  try {
    bool success = false;

    switch (instr.type) {
    case GCNInstructionType::S_MOV:
    case GCNInstructionType::S_ADD:
    case GCNInstructionType::S_SUB:
    case GCNInstructionType::S_MUL:
    case GCNInstructionType::S_AND:
    case GCNInstructionType::S_OR:
    case GCNInstructionType::S_XOR:
    case GCNInstructionType::S_NOT:
    case GCNInstructionType::S_LSHL:
    case GCNInstructionType::S_LSHR:
    case GCNInstructionType::S_ASHR:
    case GCNInstructionType::S_CMP:
      success = ProcessScalarALU(instr, spirvCode);
      if (success)
        success = ProcessScalarALU(instr, glslCode);
      break;

    case GCNInstructionType::V_MOV:
    case GCNInstructionType::V_ADD_F32:
    case GCNInstructionType::V_SUB_F32:
    case GCNInstructionType::V_MUL_F32:
    case GCNInstructionType::V_DIV_F32:
    case GCNInstructionType::V_MAD_F32:
    case GCNInstructionType::V_FMA_F32:
    case GCNInstructionType::V_MIN_F32:
    case GCNInstructionType::V_MAX_F32:
    case GCNInstructionType::V_ADD_I32:
    case GCNInstructionType::V_SUB_I32:
    case GCNInstructionType::V_MUL_I32:
    case GCNInstructionType::V_AND_B32:
    case GCNInstructionType::V_OR_B32:
      success = ProcessVectorALU(instr, spirvCode);
      if (success)
        success = ProcessVectorALU(instr, glslCode);
      break;

    case GCNInstructionType::BUFFER_LOAD:
    case GCNInstructionType::BUFFER_STORE:
    case GCNInstructionType::IMAGE_LOAD:
    case GCNInstructionType::IMAGE_STORE:
    case GCNInstructionType::DS_READ:
    case GCNInstructionType::DS_WRITE:
    case GCNInstructionType::FLAT_LOAD:
    case GCNInstructionType::FLAT_STORE:
      success = ProcessMemoryInstruction(instr, spirvCode);
      if (success)
        success = ProcessMemoryInstruction(instr, glslCode);
      break;

    case GCNInstructionType::S_BRANCH:
    case GCNInstructionType::S_CBRANCH:
    case GCNInstructionType::S_ENDPGM:
    case GCNInstructionType::S_BARRIER:
    case GCNInstructionType::S_WAITCNT:
    case GCNInstructionType::S_SENDMSG:
      success = ProcessControlFlow(instr, spirvCode);
      if (success)
        success = ProcessControlFlow(instr, glslCode);
      break;

    case GCNInstructionType::IMAGE_SAMPLE:
    case GCNInstructionType::IMAGE_GATHER:
    case GCNInstructionType::IMAGE_GET_RESINFO:
      success = ProcessTextureInstruction(instr, spirvCode);
      if (success)
        success = ProcessTextureInstruction(instr, glslCode);
      break;

    case GCNInstructionType::EXP_POS:
    case GCNInstructionType::EXP_PARAM:
    case GCNInstructionType::EXP_MRT:
      success = ProcessExportInstruction(instr, spirvCode);
      if (success)
        success = ProcessExportInstruction(instr, glslCode);
      break;

    case GCNInstructionType::UNKNOWN:
    default:
      m_stats.instructionsSkipped++;
      spdlog::warn("GNMShaderTranslator: Unsupported instruction type: {}",
                   static_cast<int>(instr.type));
      return false;
    }

    if (success) {
      m_stats.instructionsCovered++;
    } else {
      m_stats.instructionsSkipped++;
      m_stats.errorCount++;
    }

    auto end = std::chrono::steady_clock::now();
    m_stats.totalLatencyUs +=
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();

    return success;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    spdlog::error("TranslateGCNInstruction failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Processes scalar ALU instructions.
 */
bool GNMShaderTranslator::ProcessScalarALU(const EnhancedGCNInstruction &instr,
                                           std::string &output) {
  try {
    std::string operation;

    switch (instr.type) {
    case GCNInstructionType::S_MOV:
      operation = "OpCopyObject";
      break;
    case GCNInstructionType::S_ADD:
      operation = "OpIAdd";
      break;
    case GCNInstructionType::S_SUB:
      operation = "OpISub";
      break;
    case GCNInstructionType::S_MUL:
      operation = "OpIMul";
      break;
    case GCNInstructionType::S_AND:
      operation = "OpBitwiseAnd";
      break;
    case GCNInstructionType::S_OR:
      operation = "OpBitwiseOr";
      break;
    case GCNInstructionType::S_XOR:
      operation = "OpBitwiseXor";
      break;
    case GCNInstructionType::S_NOT:
      operation = "OpNot";
      break;
    case GCNInstructionType::S_LSHL:
      operation = "OpShiftLeftLogical";
      break;
    case GCNInstructionType::S_LSHR:
      operation = "OpShiftRightLogical";
      break;
    case GCNInstructionType::S_ASHR:
      operation = "OpShiftRightArithmetic";
      break;
    case GCNInstructionType::S_CMP:
      operation = "OpIEqual"; // Simplified comparison
      break;
    default:
      return false;
    }

    // Generate SPIR-V or GLSL code based on operation
    if (output.find("OpCapability") != std::string::npos) {
      // SPIR-V generation
      output += fmt::format("%{} = {} %uint %{} %{}\n", instr.operands[0],
                            operation, instr.operands[1], instr.operands[2]);
    } else {
      // GLSL generation
      std::string glslOp;
      switch (instr.type) {
      case GCNInstructionType::S_ADD:
        glslOp = "+";
        break;
      case GCNInstructionType::S_SUB:
        glslOp = "-";
        break;
      case GCNInstructionType::S_MUL:
        glslOp = "*";
        break;
      case GCNInstructionType::S_AND:
        glslOp = "&";
        break;
      case GCNInstructionType::S_OR:
        glslOp = "|";
        break;
      case GCNInstructionType::S_XOR:
        glslOp = "^";
        break;
      default:
        glslOp = "=";
        break;
      }

      output += fmt::format("  s{} = s{} {} s{};\n", instr.operands[0],
                            instr.operands[1], glslOp, instr.operands[2]);
    }

    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessScalarALU failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Processes vector ALU instructions.
 */
bool GNMShaderTranslator::ProcessVectorALU(const EnhancedGCNInstruction &instr,
                                           std::string &output) {
  try {
    std::string operation;

    switch (instr.type) {
    case GCNInstructionType::V_MOV:
      operation = "OpCopyObject";
      break;
    case GCNInstructionType::V_ADD_F32:
      operation = "OpFAdd";
      break;
    case GCNInstructionType::V_SUB_F32:
      operation = "OpFSub";
      break;
    case GCNInstructionType::V_MUL_F32:
      operation = "OpFMul";
      break;
    case GCNInstructionType::V_DIV_F32:
      operation = "OpFDiv";
      break;
    case GCNInstructionType::V_MAD_F32:
    case GCNInstructionType::V_FMA_F32:
      operation = "OpFma";
      break;
    case GCNInstructionType::V_MIN_F32:
      operation = "OpFMin";
      break;
    case GCNInstructionType::V_MAX_F32:
      operation = "OpFMax";
      break;
    case GCNInstructionType::V_ADD_I32:
      operation = "OpIAdd";
      break;
    case GCNInstructionType::V_SUB_I32:
      operation = "OpISub";
      break;
    case GCNInstructionType::V_MUL_I32:
      operation = "OpIMul";
      break;
    case GCNInstructionType::V_AND_B32:
      operation = "OpBitwiseAnd";
      break;
    case GCNInstructionType::V_OR_B32:
      operation = "OpBitwiseOr";
      break;
    default:
      return false;
    }

    // Generate SPIR-V or GLSL code
    if (output.find("OpCapability") != std::string::npos) {
      // SPIR-V generation
      if (instr.type == GCNInstructionType::V_MAD_F32 ||
          instr.type == GCNInstructionType::V_FMA_F32) {
        output += fmt::format("%{} = {} %float %{} %{} %{}\n",
                              instr.operands[0], operation, instr.operands[1],
                              instr.operands[2], instr.operands[3]);
      } else {
        output += fmt::format("%{} = {} %float %{} %{}\n", instr.operands[0],
                              operation, instr.operands[1], instr.operands[2]);
      }
    } else {
      // GLSL generation
      std::string glslOp;
      switch (instr.type) {
      case GCNInstructionType::V_ADD_F32:
        glslOp = "+";
        break;
      case GCNInstructionType::V_SUB_F32:
        glslOp = "-";
        break;
      case GCNInstructionType::V_MUL_F32:
        glslOp = "*";
        break;
      case GCNInstructionType::V_DIV_F32:
        glslOp = "/";
        break;
      case GCNInstructionType::V_MIN_F32:
        glslOp = "min";
        break;
      case GCNInstructionType::V_MAX_F32:
        glslOp = "max";
        break;
      default:
        glslOp = "=";
        break;
      }

      if (instr.type == GCNInstructionType::V_MAD_F32 ||
          instr.type == GCNInstructionType::V_FMA_F32) {
        output += fmt::format("  v{} = fma(v{}, v{}, v{});\n",
                              instr.operands[0], instr.operands[1],
                              instr.operands[2], instr.operands[3]);
      } else if (instr.type == GCNInstructionType::V_MIN_F32 ||
                 instr.type == GCNInstructionType::V_MAX_F32) {
        output += fmt::format("  v{} = {}(v{}, v{});\n", instr.operands[0],
                              glslOp, instr.operands[1], instr.operands[2]);
      } else {
        output += fmt::format("  v{} = v{} {} v{};\n", instr.operands[0],
                              instr.operands[1], glslOp, instr.operands[2]);
      }
    }

    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessVectorALU failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Processes memory instructions for shader translation.
 */
bool GNMShaderTranslator::ProcessMemoryInstruction(
    const EnhancedGCNInstruction &instr, std::string &output) {
  try {
    std::stringstream ss;

    switch (instr.type) {
    case GCNInstructionType::BUFFER_LOAD:
      ss << "// Buffer load operation\n";
      if (instr.operands.size() >= 2) {
        ss << "vec4 buffer_data = texelFetch(buffer_texture, int("
           << instr.operands[1] << "));\n";
        ss << "r" << instr.operands[0] << " = buffer_data;\n";
      }
      break;

    case GCNInstructionType::BUFFER_STORE:
      ss << "// Buffer store operation\n";
      if (instr.operands.size() >= 2) {
        ss << "imageStore(buffer_image, int(" << instr.operands[0] << "), r"
           << instr.operands[1] << ");\n";
      }
      break;

    case GCNInstructionType::IMAGE_LOAD:
      ss << "// Image load operation\n";
      if (instr.operands.size() >= 3) {
        ss << "vec4 image_data = texelFetch(image_texture, ivec2("
           << instr.operands[1] << ", " << instr.operands[2] << "));\n";
        ss << "r" << instr.operands[0] << " = image_data;\n";
      }
      break;

    case GCNInstructionType::IMAGE_STORE:
      ss << "// Image store operation\n";
      if (instr.operands.size() >= 3) {
        ss << "imageStore(image_texture, ivec2(" << instr.operands[0] << ", "
           << instr.operands[1] << "), r" << instr.operands[2] << ");\n";
      }
      break;

    default:
      ss << "// Unsupported memory instruction\n";
      m_stats.instructionsSkipped++;
      break;
    }

    output += ss.str();
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessMemoryInstruction failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Processes control flow instructions for shader translation.
 */
bool GNMShaderTranslator::ProcessControlFlow(
    const EnhancedGCNInstruction &instr, std::string &output) {
  try {
    std::stringstream ss;

    switch (instr.type) {
    case GCNInstructionType::S_BRANCH:
      ss << "// Unconditional branch\n";
      if (instr.operands.size() >= 1) {
        ss << "// Branch to address: 0x" << std::hex << instr.operands[0]
           << "\n";
      }
      break;

    case GCNInstructionType::S_CBRANCH:
      ss << "// Conditional branch\n";
      if (instr.operands.size() >= 2) {
        ss << "if (s" << instr.operands[0] << " != 0) {\n";
        ss << "  // Branch to address: 0x" << std::hex << instr.operands[1]
           << "\n";
        ss << "}\n";
      }
      break;

    case GCNInstructionType::S_ENDPGM:
      ss << "// End program\n";
      ss << "return;\n";
      break;

    case GCNInstructionType::S_BARRIER:
      ss << "// Synchronization barrier\n";
      ss << "barrier();\n";
      break;

    case GCNInstructionType::S_WAITCNT:
      ss << "// Wait for memory operations\n";
      ss << "memoryBarrier();\n";
      break;

    default:
      ss << "// Unsupported control flow instruction\n";
      m_stats.instructionsSkipped++;
      break;
    }

    output += ss.str();
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessControlFlow failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Processes texture instructions for shader translation.
 */
bool GNMShaderTranslator::ProcessTextureInstruction(
    const EnhancedGCNInstruction &instr, std::string &output) {
  try {
    std::stringstream ss;

    switch (instr.type) {
    case GCNInstructionType::IMAGE_SAMPLE:
      ss << "// Texture sample operation\n";
      if (instr.operands.size() >= 3) {
        ss << "vec4 sample_result = texture(sampler_" << instr.operands[2]
           << ", vec2(r" << instr.operands[1] << ".xy));\n";
        ss << "r" << instr.operands[0] << " = sample_result;\n";
      }
      break;

    case GCNInstructionType::IMAGE_GATHER:
      ss << "// Texture gather operation\n";
      if (instr.operands.size() >= 3) {
        ss << "vec4 gather_result = textureGather(sampler_" << instr.operands[2]
           << ", vec2(r" << instr.operands[1] << ".xy));\n";
        ss << "r" << instr.operands[0] << " = gather_result;\n";
      }
      break;

    case GCNInstructionType::IMAGE_GET_RESINFO:
      ss << "// Get texture resolution info\n";
      if (instr.operands.size() >= 2) {
        ss << "ivec2 tex_size = textureSize(sampler_" << instr.operands[1]
           << ", 0);\n";
        ss << "r" << instr.operands[0] << " = vec4(tex_size.xy, 0.0, 0.0);\n";
      }
      break;

    default:
      ss << "// Unsupported texture instruction\n";
      m_stats.instructionsSkipped++;
      break;
    }

    output += ss.str();
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessTextureInstruction failed: {}", e.what());
    return false;
  }
}

/**
 * @brief Processes export instructions for shader translation.
 */
bool GNMShaderTranslator::ProcessExportInstruction(
    const EnhancedGCNInstruction &instr, std::string &output) {
  try {
    std::stringstream ss;

    switch (instr.type) {
    case GCNInstructionType::EXP_POS:
      ss << "// Export position\n";
      if (instr.operands.size() >= 1) {
        ss << "gl_Position = r" << instr.operands[0] << ";\n";
      }
      break;

    case GCNInstructionType::EXP_PARAM:
      ss << "// Export parameter\n";
      if (instr.operands.size() >= 2) {
        ss << "out_param_" << instr.operands[0] << " = r" << instr.operands[1]
           << ";\n";
      }
      break;

    case GCNInstructionType::EXP_MRT:
      ss << "// Export to multiple render targets\n";
      if (instr.operands.size() >= 2) {
        ss << "out_color_" << instr.operands[0] << " = r" << instr.operands[1]
           << ";\n";
      }
      break;

    default:
      ss << "// Unsupported export instruction\n";
      m_stats.instructionsSkipped++;
      break;
    }

    output += ss.str();
    return true;
  } catch (const std::exception &e) {
    spdlog::error("ProcessExportInstruction failed: {}", e.what());
    return false;
  }
}

} // namespace ps4