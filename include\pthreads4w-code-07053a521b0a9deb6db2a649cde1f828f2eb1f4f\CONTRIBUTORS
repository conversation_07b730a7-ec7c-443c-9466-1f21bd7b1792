Contributors (in approximate order of appearance)

[See also the ChangeLog file where individuals are
attributed in log entries. Likewise in the FAQ file.]

<PERSON>		bje at cygnus dot com
					Initiated the project;
					setup the project infrastructure (CVS, web page, etc.);
					early prototype routines.
<PERSON> dot Johnson at  dot homemail dot com dot au
					early prototype routines;
					ongoing project coordination/maintenance;
					implementation of spin locks and barriers;
					various enhancements;
					bug fixes;
					documentation;
					testsuite.
<PERSON>	rjc at trump dot net dot au
					Early bug fixes.
<PERSON>		john dot bossom at gmail dot com
					Contributed substantial original working implementation;
					bug fixes;
<PERSON>	an<PERSON> at hem2 dot passagen dot se
					Early enhancements and runtime checking for supported
					Win32 routines.
<PERSON>lqvist		tml at iki dot fi
					General enhancements;
					early bug fixes to condition variables.
<PERSON>		scott at curriculum dot com
					Bug fix.
<PERSON>		Kevin dot Ruland at anheuser-busch dot com
					Various bug fixes.
<PERSON>		miker at eai dot com
					Bug fix.
<PERSON>. <PERSON>	avail at pacbell dot net
					Bug fixes.
<PERSON><PERSON> 	lmh at xiphos dot ca
					general bug fixes; bug fixes to condition variables.
<PERSON>		Peter dot <PERSON>cik at tatramed dot sk
					Bug fixes.
<PERSON><PERSON> <PERSON>		khan at xraylith dot wisc dot edu
					Fixes to work with Mingw32.
<PERSON> Gardian		mg at tatramed dot sk
					Bug fixes and reports/analyses of obscure problems.
<PERSON>relio Medina		aureliom at crt dot com
					First implementation of read-write locks.
<PERSON> Dumpleton	<PERSON> dot Dumpleton at ra dot pad dot otc dot telstra dot com dot au
					Bug fix in condition variables.
Tristan Savatier	tristan at mpegtv dot com
					WinCE port.
Erik Hensema		erik at hensema dot xs4all dot nl
					Bug fixes.
Rich Peters		rpeters at micro-magic dot com
Todd Owen		towen at lucidcalm dot dropbear dot id dot au
					Bug fixes to dll loading.
Jason Nye		jnye at nbnet dot nb dot ca
					Implementation of async cancellation.
Fred Forester		fforest at eticomm dot net
Kevin D. Clark		kclark at cabletron dot com
David Baggett		dmb at itasoftware dot com
					Bug fixes.
Paul Redondo		paul at matchvision dot com
Scott McCaskill 	scott at 3dfx dot com
					Bug fixes.
Jef Gearhart		jgearhart at tpssys dot com
					Bug fix.
Arthur Kantor		akantor at bexusa dot com
					Mutex enhancements.
Steven Reddie		smr at essemer dot com dot au
					Bug fix.
Alexander Terekhov	TEREKHOV at de dot ibm dot com
					Re-implemented and improved read-write locks;
					(with Louis Thomas) re-implemented and improved
					condition variables;
					enhancements to semaphores;
					enhancements to mutexes;
					new mutex implementation in 'futex' style;
					suggested a robust implementation of pthread_once
					using a named mutex;
					system clock change handling re CV timeouts;
					bug fixes.
Thomas Pfaff		tpfaff at gmx dot net
					Changes to make C version usable with C++ applications;
					re-implemented mutex routines to avoid Win32 mutexes
					and TryEnterCriticalSection;
					procedure to fix Mingw32 thread-safety issues.
Franco Bez			franco dot bez at gmx dot de
					procedure to fix Mingw32 thread-safety issues.
Louis Thomas		lthomas at arbitrade dot com
					(with Alexander Terekhov) re-implemented and improved
					condition variables.
David Korn			dgk at research dot att dot com
					Ported to UWIN.
Phil Frisbie, Jr.	phil at hawksoft dot com
					Bug fix.
Ralf Brese			Ralf dot Brese at pdb4 dot siemens dot de
					Bug fix.
prionx at juno dot com 	prionx at juno dot com
					Bug fixes.
Max Woodbury		mtew at cds dot duke dot edu
					POSIX versioning conditionals;
					reduced namespace pollution;
					idea to separate routines to reduce statically
					linked image sizes.
Rob Fanner			rfanner at stonethree dot com
					Bug fix.
Michael Johnson 	michaelj at maine dot rr dot com
					Bug fix.
Nicolas Barry		boozai at yahoo dot com
					Bug fixes.
Piet van Bruggen	pietvb at newbridges dot nl
					Bug fix.
Makoto Kato			raven at oldskool dot jp
					AMD64 port.
Panagiotis E. Hadjidoukas	peh at hpclab dot ceid dot upatras dot gr
                                phadjido at cs dot uoi dot gr
					Contributed the QueueUserAPCEx package which
					makes preemptive async cancellation possible.
Will Bryant			will dot bryant at ecosm dot com
					Borland compiler patch and makefile.
Anuj Goyal			anuj dot goyal at gmail dot com
					Port to Digital Mars compiler.
Gottlob Frege		gottlobfrege at  gmail dot com
					re-implemented pthread_once (version 2)
					(pthread_once cancellation added by rpj).
Vladimir Kliatchko	vladimir at kliatchko dot com
					reimplemented pthread_once with the same form
					as described by A.Terekhov (later version 2);
					implementation of MCS (Mellor-Crummey/Scott) locks.
Ramiro Polla		ramiro.polla at gmail dot com
					static library auto init/cleanup on application
					start/exit via RT hooks (MSC and GCC compilers only).
Daniel Richard G.	skunk at iSKUNK dot org
					Patches and cleanups for x86 and x64, particularly
					across a range of MS build environments.
John Kamp			john dot kamp at globalgraphics dot com
					Patches to fix various problems on x64; brutal testing
					particularly using high memory run environments.

